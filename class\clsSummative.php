<?php
class clsSummative
{
	var $studentSummativeMasterId = '';
	var $clinicanId = '';
	var $studentId = '';
	var $rotationId = '';
	var $schoolId = '';
	var $createdBy = '';
	var $createdDate = '';
	var $preceptorId = '';
	var $updatedBy = '';
	var $updatedDate = '';
	var $schoolSummativeQuestionId = '';
	var $summativeQuestionType = '';
	var $schoolSummativeQuestionTitle = '';
	var $sectionMasterId = '';
	var $schoolSummativeQuestionType = '';
	var $schoolSummativeOptionValue = '';
	var $schoolSummativeOptionAnswerText  = '';
	var $studentSummativeDetailId  = '';

	function SaveSummative($studentSummativeMasterId)
	{
		$objDB = new clsDB();
		$sql = '';
		if ($studentSummativeMasterId > 0) {

			$sql = "UPDATE studentsummativemaster SET 						 
						clinicanId = '" . addslashes($this->clinicanId) . "',
						rotationId = '" . addslashes($this->rotationId) . "',
						evaluationDate='" . addslashes($this->evaluationDate) . "',
						preceptorId = '" . addslashes($this->preceptorId) . "',
						isPreceptorCompletedStatus = '" . addslashes($this->isPreceptorCompletedStatus) . "',
						studentSignature='" . addslashes($this->studentSignature) . "',
						studentComment='" . addslashes($this->studentComment) . "',
						updatedBy = '" . addslashes($this->createdBy) . "',
						updatedDate = '" . (date("Y-m-d h:i:s")) . "' 
						Where studentSummativeMasterId= " . $studentSummativeMasterId;
			// echo 'update->'.$sql;exit;
			$objDB->ExecuteQuery($sql);
		} else {
			$sql = "INSERT INTO studentsummativemaster (schoolId,clinicanId, studentId, rotationId,
					evaluationDate,preceptorId,studentSignature,studentComment,
					createdBy, createdDate) 
				VALUES ('" . addslashes($this->schoolId) . "',
						'" . addslashes($this->clinicanId) . "',
						'" . addslashes($this->studentId) . "',
						'" . addslashes($this->rotationId) . "',						
						'" . addslashes($this->evaluationDate) . "',
						'" . addslashes($this->preceptorId) . "',
						'" . addslashes($this->studentSignature) . "',
						'" . addslashes($this->studentComment) . "',
						'" . addslashes($this->createdBy) . "',
						'" . (date("Y-m-d h:i:s")) . "'
						)";
			// echo 'Insert->'.$sql;exit;

			$studentSummativeMasterId = $objDB->ExecuteInsertQuery($sql);
		}

		unset($objDB);
		return $studentSummativeMasterId;
	}

	function SaveSummativeevaluationSection($sectionId)
	{
		$objDB = new clsDB();
		$sql = '';
		if ($sectionId > 0) {

			$sql = "UPDATE summativesectionmaster  SET 						
						 title = '" . addslashes($this->title) . "',						
						 sortOrder = '" . addslashes($this->sortOrder) . "'
						 Where summativeSectionMasterId= " . $sectionId;
			//echo 'update->'.$sql;exit;
			$objDB->ExecuteQuery($sql);
		} else {
			$sql = "INSERT INTO summativesectionmaster  (schoolId,title,sortOrder) 
				VALUES ('" . addslashes($this->schoolId) . "',
						'" . addslashes($this->title) . "',
						'" . addslashes($this->sortOrder) . "'						
						)";
			// echo 'Insert->'.$sql;exit;
			$sectionId = $objDB->ExecuteInsertQuery($sql);
		}

		unset($objDB);
		return $sectionId;
	}

	function SaveDefaultSummativeevaluationSection($sectionId)
	{
		$objDB = new clsDB();
		$sql = '';
		if ($sectionId > 0) {

			$sql = "UPDATE defaultsummativesectionmaster  SET 						
						 title = '" . addslashes($this->title) . "',						
						 sortOrder = '" . addslashes($this->sortOrder) . "'
						 Where sectionMasterId= " . $sectionId;
			//echo 'update->'.$sql;exit;
			$objDB->ExecuteQuery($sql);
		} else {
			$sql = "INSERT INTO defaultsummativesectionmaster  (title,sortOrder) 
				VALUES ('" . addslashes($this->title) . "',
						'" . addslashes($this->sortOrder) . "'						
						)";
			// echo 'Insert->'.$sql;exit;
			$sectionId = $objDB->ExecuteInsertQuery($sql);
		}

		unset($objDB);
		return $sectionId;
	}


	function DeleteSummativeEvaluationQuestion($questionId)
	{
		$objDB = new clsDB();

		$sql = "DELETE  FROM schoolsummativequestionmaster  WHERE schoolSummativeQuestionId=" . $questionId;
		//ECHO 'DELETE->'.$sql;EXIT;
		$result = $objDB->ExecuteQuery($sql);
		unset($objDB);
		return $result;
	}
	function DeleteDefaultSummativeEvaluationQuestion($questionId)
	{
		$objDB = new clsDB();
		$sql = "DELETE  FROM summativequestionmaster  WHERE summativeQuestionId=" . $questionId;
		//ECHO 'DELETE->'.$sql;EXIT;
		$result = $objDB->ExecuteQuery($sql);
		unset($objDB);
		return $result;
	}

	function DeleteSummativeEvaluationQuestions($questionId)
	{
		$objDB = new clsDB();

		$sql = "DELETE  FROM schoolsummativequestiondetail  WHERE schoolSummativeQuestionId=" . $questionId;
		//ECHO 'DELETE->'.$sql;EXIT;
		$result = $objDB->ExecuteQuery($sql);
		unset($objDB);
		return $result;
	}
	function DeleteDefaultSummativeEvaluationQuestionOption($questionId)
	{
		$objDB = new clsDB();

		$sql = "DELETE  FROM summativequestiondetail  WHERE summativeQuestionId=" . $questionId;
		//ECHO 'DELETE->'.$sql;EXIT;
		$result = $objDB->ExecuteQuery($sql);
		unset($objDB);
		return $result;
	}


	function GetAllSummativeEvaluationQuestionToSetting($currentSchoolId, $sectionMasterId)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT schoolsummativequestionmaster.*,questiontypemaster.title as questionType FROM  schoolsummativequestionmaster
				LEFT JOIN questiontypemaster ON schoolsummativequestionmaster.schoolSummativeQuestionType=questiontypemaster.questionType
				WHERE schoolId=" . $currentSchoolId . " AND schoolsummativequestionmaster.sectionMasterId=" . $sectionMasterId;
		$sql .= " ORDER BY sortOrder ASC ";
		//echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetAllDefaultSummativeEvaluationQuestionToSetting($sectionMasterId)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT summativequestionmaster.*,questiontypemaster.title as questionType FROM  summativequestionmaster
				LEFT JOIN questiontypemaster ON summativequestionmaster.summativeQuestionType=questiontypemaster.questionType
				WHERE summativequestionmaster.sectionMasterId=" . $sectionMasterId;
		$sql .= " ORDER BY sortOrder ASC ";
		//echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function SaveAdminSummative($studentSummativeMasterId)
	{
		$objDB = new clsDB();
		$sql = '';
		if ($studentSummativeMasterId > 0) {

			$sql = "UPDATE studentsummativemaster SET 						 
						 clinicanId = '" . addslashes($this->clinicanId) . "',
						 rotationId = '" . addslashes($this->rotationId) . "',
						 evaluationDate='" . addslashes($this->evaluationDate) . "',						 
						 updatedBy = '" . addslashes($this->createdBy) . "',
						 updatedDate = '" . (date("Y-m-d h:i:s")) . "' 
						 Where studentSummativeMasterId= " . $studentSummativeMasterId;
			//echo 'update->'.$sql;exit;
			$objDB->ExecuteQuery($sql);
		} else {
			$sql = "INSERT INTO studentsummativemaster (schoolId,clinicanId, studentId, rotationId,
					evaluationDate,	createdBy, createdDate) 
				VALUES ('" . addslashes($this->schoolId) . "',
						'" . addslashes($this->clinicanId) . "',
						'" . addslashes($this->studentId) . "',
						'" . addslashes($this->rotationId) . "',						
						'" . addslashes($this->evaluationDate) . "',						
						 '" . addslashes($this->createdBy) . "',
						'" . (date("Y-m-d h:i:s")) . "'
						)";

			$studentSummativeMasterId = $objDB->ExecuteInsertQuery($sql);
		}

		unset($objDB);
		return $studentSummativeMasterId;
	}

	function SaveStudentSummative($studentSummativeMasterId)
	{
		$objDB = new clsDB();
		$sql = '';
		if ($studentSummativeMasterId > 0) {

			$sql = "UPDATE studentsummativemaster SET 						 
						 clinicanId = '" . addslashes($this->clinicanId) . "',
						 rotationId = '" . addslashes($this->rotationId) . "',
						 evaluationDate='" . addslashes($this->evaluationDate) . "',
						 studentSignature='" . addslashes($this->studentSignature) . "',
						 dateOfStudentSignature='" . addslashes($this->dateOfStudentSignature) . "',
						 studentComment='" . addslashes($this->studentComment) . "',
						 updatedBy = '" . addslashes($this->createdBy) . "',
						 updatedDate = '" . (date("Y-m-d h:i:s")) . "' 
						 Where studentSummativeMasterId= " . $studentSummativeMasterId;
			//echo 'update->'.$sql;exit;
			$objDB->ExecuteQuery($sql);
		}

		unset($objDB);
		return $studentSummativeMasterId;
	}

	function SaveStudentSummativeSignoffForApp($studentSummativeMasterId, $StudentSignatureDate, $UserId, $StudentComment)
	{

		$objDB = new clsDB();
		$sql = '';
		if ($studentSummativeMasterId > 0) {

			$sql = "UPDATE studentsummativemaster SET 						 
						
						 dateOfStudentSignature='" . $StudentSignatureDate . "',
						 studentComment='" . $StudentComment . "',
						 updatedBy = '" . $UserId . "',
						 updatedDate = '" . (date("Y-m-d h:i:s")) . "' 
						 Where studentSummativeMasterId= " . $studentSummativeMasterId;
			//  echo 'update->'.$sql;exit;
			$objDB->ExecuteQuery($sql);
		}

		unset($objDB);
		return $studentSummativeMasterId;
	}

	function DeleteStudentSummativeDetails($studentSummativeMasterId)
	{
		$objDB = new clsDB();

		$sql = "DELETE  FROM studentsummativedetail WHERE studentSummativeMasterId=" . $studentSummativeMasterId;
		//ECHO 'DELETE->'.$sql;EXIT;
		$result = $objDB->ExecuteQuery($sql);
		unset($objDB);
		return $result;
	}

	function SaveStudentSummativeDetail($studentSummativeDetailId)
	{
		$sql = '';
		$objDB = new clsDB();
		$sql = "INSERT INTO studentsummativedetail (studentSummativeMasterId,schoolSummativeQuestionId,
								schoolSummativeOptionValue,schoolSummativeOptionAnswerText) 
					 VALUES ('" . ($this->studentSummativeMasterId) . "',
							 '" . ($this->schoolSummativeQuestionId) . "',
							 '" . ($this->schoolSummativeOptionValue) . "',
							 '" . addslashes($this->schoolSummativeOptionAnswerText) . "')";
		//echo '<hr>'.$sql;
		$studentSummativeDetailId = $objDB->ExecuteInsertQuery($sql);
		unset($objDB);
		return $studentSummativeDetailId;
	}

	function CopyAllSummativeQuestionMaster($currentSchoolId, $isNewSection = 0)
	{
		$currentschoolSectionId = '';
		$this->schoolId = $currentSchoolId;
		$objDB = new clsDB();
		$savedQuestionIds = array();
		$savedSectionIds = array();

		$sql = "select defaultsummativesectionmaster.* from 
							summativequestionmaster 
							INNER JOIN defaultsummativesectionmaster ON  defaultsummativesectionmaster.sectionMasterId = summativequestionmaster.sectionMasterId";

		if ($isNewSection == 1)
			$sql .= " where defaultsummativesectionmaster.isSectionAdded=1";

		$sql .= " GROUP BY defaultsummativesectionmaster.sectionMasterId ";

		$rowsSectionMaster = $objDB->GetResultset($sql);
		//echo $count=mysqli_num_rowss($rowsSectionMaster);exit;
		if ($rowsSectionMaster != "") {
			while ($sectionMaster = mysqli_fetch_array($rowsSectionMaster)) {
				$currentschoolQuestionId = array();
				//default assignment
				$currentschoolQuestionId[] = 0;
				$sectionId = $sectionMaster['sectionMasterId'];

				//Skipp
				if (array_key_exists($sectionId, $savedSectionIds)) {
					$currentschoolSectionId = $savedSectionIds[$sectionId];
				} else {
					$this->title = $sectionMaster['title'];
					$this->sortOrder = $sectionMaster['sortOrder'];
					$this->schoolId = $currentSchoolId;
					$currentschoolSectionId = $this->SaveSchoolSectionMaster($sectionId);
					$savedSectionIds[$sectionId] = $currentschoolSectionId;
				}

				$sql = "select summativequestionmaster.* from summativequestiondetail
									RIGHT JOIN summativequestionmaster ON summativequestionmaster.summativeQuestionId=summativequestiondetail.summativeQuestionId
									WHERE sectionMasterId =" . $sectionId;
				$rowsQuestionMaster = $objDB->GetResultset($sql);
				if ($rowsQuestionMaster != "") {
					while ($row = mysqli_fetch_array($rowsQuestionMaster)) {
						$masterQuestionId = $row['summativeQuestionId'];
						// If already used then skipp
						if (array_key_exists($masterQuestionId, $savedQuestionIds)) {
							$currentschoolQuestionId[] = $savedQuestionIds[$masterQuestionId];
							continue;
						} else {
							$this->optionText = $row['optionText'];
							$this->schoolSummativeQuestionType = $row['summativeQuestionType'];
							$this->summativeQuestionId = $masterQuestionId;
							$this->sectionMasterId = $currentschoolSectionId;
							$this->sortOrder = $row['sortOrder'];
							$schoolQuestionId = $this->CopyMasterQuestionToSchool($currentSchoolId, $masterQuestionId, $currentschoolSectionId);
							//Bind in array
							$savedQuestionIds[$masterQuestionId] = $schoolQuestionId;
							$currentschoolQuestionId[] = $schoolQuestionId;
							//-----------------------------------------------------
							//Copy Question Choices
							//-----------------------------------------------------
							$this->CopyMasterQuestionChoicesToSchool($masterQuestionId, $schoolQuestionId);
							//-----------------------------------------------------
						}
					} //while end


				} //if end
			} //1st while end
		} //1st if end 

	}

	function SaveSchoolSectionMaster($schoolSectionId)
	{
		$objDB = new clsDB();
		$sql = "INSERT INTO summativesectionmaster (schoolId,title,sortOrder) 
					 VALUES (
								" . addslashes($this->schoolId) . ",
								'" . addslashes($this->title) . "',
								" . addslashes($this->sortOrder) . "							 
							)";
		//echo 'section->'. $sql;exit;
		$schoolSectionId = $objDB->ExecuteInsertQuery($sql);
		$this->schoolSectionId = $schoolSectionId;
		unset($objDB);
		return $schoolSectionId;
	}
	function CopyMasterQuestionToSchool($schoolId, $questionId, $currentschoolSectionId)
	{
		$objDB = new clsDB();
		$sql = "INSERT INTO schoolsummativequestionmaster (optionText,schoolSummativeQuestionType,schoolId,sectionMasterId,sortOrder) 
                
                SELECT optionText,summativeQuestionType," . $schoolId . "," . $currentschoolSectionId . " ,sortOrder FROM summativequestionmaster
                WHERE summativeQuestionId=" . $questionId;
		$schoolQuestionId = $objDB->ExecuteInsertQuery($sql);
		unset($objDB);
		return  $schoolQuestionId;;
	}
	function CopyMasterQuestionChoicesToSchool($questionMasterId, $schoolQuestionId)
	{
		$sql = "INSERT INTO schoolsummativequestiondetail (schoolSummativeQuestionId,optionText,schoolOptionValue) 
                        SELECT " . $schoolQuestionId . ",optionText,summativeOptionValue
                        FROM summativequestiondetail  WHERE summativeQuestionId=" . $questionMasterId;

		$objDB = new clsDB();
		$schoolQuestionDId = $objDB->ExecuteInsertQuery($sql);
		unset($objDB);
	}

	function SaveSchoolSummativeQuestionMaster()
	{
		$objDB = new clsDB();
		$sql = "INSERT INTO schoolsummativequestionmaster 
						(optionText,schoolSummativeQuestionType,schoolId,sectionMasterId) 
						 VALUES ('" . addslashes($this->optionText) . "',
								 '" . addslashes($this->summativeQuestionType) . "',
								 '" . addslashes($this->schoolId) . "',
								 '" . addslashes($this->sectionMasterId) . "'
								 
								)";
		//echo $sql;exit;
		$schoolSummativeQuestionId = $objDB->ExecuteInsertQuery($sql);
	}

	function GetAllSummativeQuestionMaster($currentSchoolId, $sectionMasterId)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT * FROM  schoolsummativequestionmaster
						WHERE schoolId=" . $currentSchoolId . " AND sectionMasterId=" . $sectionMasterId;
		$sql .= " ORDER BY sortOrder ASC";
		// echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetSummativeQuestionCountbySections($currentSchoolId, $sectionMasterId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT count(sectionMasterId) as count
				FROM schoolsummativequestionmaster 
				WHERE schoolId=" . $currentSchoolId . " AND sectionMasterId=" . $sectionMasterId;
		//echo $sql;		
		$rows = $objDB->GetSingleFieldValue($sql);
		return $rows;
		unset($objDB);
	}
	function GetDefaultSummativeQuestionCountbySections($sectionMasterId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT count(sectionMasterId) as count
				FROM summativequestionmaster 
				WHERE  sectionMasterId=" . $sectionMasterId;
		//echo $sql;		
		$rows = $objDB->GetSingleFieldValue($sql);
		return $rows;
		unset($objDB);
	}

	function GetsummativeEvaluationSectionDetail($sectionMasterId)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT * FROM  summativesectionmaster 
					WHERE summativeSectionMasterId=" . $sectionMasterId;
		$rows = $objDB->GetDataRow($sql);
		return $rows;
		unset($objDB);
	}

	function GetDefaultsummativeEvaluationSectionDetail($sectionMasterId)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT * FROM  defaultsummativesectionmaster 
					WHERE sectionMasterId=" . $sectionMasterId;
		$rows = $objDB->GetDataRow($sql);
		return $rows;
		unset($objDB);
	}


	function GetSections($currentSchoolId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT summativesectionmaster.* ,schools.schoolId
						FROM summativesectionmaster 
						LEFT JOIN schools ON summativesectionmaster.schoolId=schools.schoolId
						WHERE summativesectionmaster.schoolId=" . $currentSchoolId . " and summativesectionmaster.isActive=1";
		// echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetAllSections($currentSchoolId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT summativesectionmaster.* ,schools.schoolId
						FROM summativesectionmaster 
						LEFT JOIN schools ON summativesectionmaster.schoolId=schools.schoolId
						WHERE summativesectionmaster.schoolId=" . $currentSchoolId;
		//echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}
	function GetAllDefaultSections()
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT * FROM defaultsummativesectionmaster";
		//echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetAllSummative($rotationId, $studentId, $canvasStatus = '', $schoolId = 0, $isclinician = 0)
	{
		$objDB = new clsDB();
		$sql = "SELECT studentsummativemaster.*,studentsummativedetail.*,student.studentId,
						rotation.rotationId,rotation.title,student.firstName,student.lastName,
						student.rankId,rankmaster.rankId,rankmaster.title AS Ranktitle,
						rotation.parentRotationId,rotation.locationId as rotationLocationId,courses.locationId,studentsummativemaster.studentSummativeMasterId
						FROM studentsummativemaster
						LEFT JOIN studentsummativedetail ON studentsummativemaster.studentSummativeMasterId=
															studentsummativedetail.studentSummativeMasterId
						LEFT JOIN rotation ON studentsummativemaster.rotationId=rotation.rotationId
						LEFT JOIN student ON studentsummativemaster.studentId=student.studentId
						LEFT JOIN  courses ON rotation.courseId =courses.courseId
						LEFT JOIN rankmaster ON student.rankId=rankmaster.rankId";
		if ($isclinician > 0) {
			$sql .=	"  INNER JOIN clinician on studentsummativemaster.clinicanId = clinician.clinicianId";
		}
		$sql .= " WHERE studentsummativemaster.studentSummativeMasterId !=0 ";
		if ($rotationId > 0) {
			$sql .=	"  AND studentsummativemaster.rotationId=" . $rotationId;
		}
		if ($studentId > 0)
			$sql .= "  AND studentsummativemaster.studentId=" . $studentId;

		if ($canvasStatus != '')
			$sql .= "  AND studentsummativemaster.isSendToCanvas=" . $canvasStatus . " AND studentsummativemaster.schoolId=" . $schoolId;

		$sql .=	" GROUP BY	studentsummativedetail.studentSummativeMasterId";
		$sql .=	" order BY	studentsummativemaster.evaluationDate desc";
		// echo $sql;	
		$row = $objDB->GetResultset($sql);
		unset($objDB);
		return $row;
	}
	function DeleteSummative($studentSummativeMasterId)
	{
		$objDB = new clsDB();
		$result = "";
		if ($studentSummativeMasterId > 0) {
			$objDB = new clsDB();
			$sql = "DELETE studentsummativemaster,studentsummativedetail FROM studentsummativemaster
									LEFT JOIN studentsummativedetail ON studentsummativemaster.studentSummativeMasterId=
																			studentsummativedetail.studentSummativeMasterId
									WHERE studentsummativemaster.studentSummativeMasterId = " . $studentSummativeMasterId;
			//ECHO $sql;EXIT;
			$result = $objDB->ExecuteQuery($sql);
			unset($objDB);
		}
		return $result;
	}

	function DeleteSummativeEvaluationSection($sectionMasterId)
	{
		$objDB = new clsDB();

		$sql = "DELETE  FROM summativesectionmaster WHERE summativeSectionMasterId=" . $sectionMasterId;
		//ECHO 'DELETE->'.$sql;EXIT;
		$result = $objDB->ExecuteQuery($sql);
		unset($objDB);
		return $result;
	}
	function DeleteDefaultSummativeEvaluationSection($sectionMasterId)
	{
		$objDB = new clsDB();

		$sql = "DELETE  FROM defaultsummativesectionmaster WHERE summativeSectionMasterId=" . $sectionMasterId;
		//ECHO 'DELETE->'.$sql;EXIT;
		$result = $objDB->ExecuteQuery($sql);
		unset($objDB);
		return $result;
	}

	function GetStudentSummativeDetails($studentSummativeMasterId)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT studentsummativemaster.*,studentsummativedetail.*,
						rotation.title as rotationName, extenal_preceptors.firstName, extenal_preceptors.lastName,clinician.firstName, clinician.lastName AS clinicianName,
						clinician.clinicianId
							FROM  studentsummativemaster 
							LEFT JOIN rotation ON studentsummativemaster.rotationId=rotation.rotationId
							LEFT JOIN extenal_preceptors ON studentsummativemaster.preceptorId = extenal_preceptors.id
							LEFT JOIN studentsummativedetail ON studentsummativedetail.studentSummativeMasterId= 
							studentsummativemaster.`studentSummativeMasterId`
							LEFT JOIN  clinician ON studentsummativemaster.clinicanId =clinician.clinicianId	
							WHERE studentsummativemaster.studentSummativeMasterId=" . $studentSummativeMasterId;
		//    echo $sql;		exit;
		$rows = $objDB->GetDataRow($sql);
		return $rows;
		unset($objDB);
	}

	function GetQuestionId()
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT schoolSummativeQuestionId FROM schoolsummativequestionmaster";
		//echo $sql;		
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetSummativeEvaluationQuestionDetail($currentSchoolId, $questionId)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT * FROM  schoolsummativequestionmaster
                        WHERE schoolId=" . $currentSchoolId . " AND schoolSummativeQuestionId=" . $questionId;
		//echo $sql;		
		$rows = $objDB->GetDataRow($sql);
		return $rows;
		unset($objDB);
	}
	function GetDefaultSummativeEvaluationQuestionDetail($questionId)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT * FROM  summativequestionmaster
                        WHERE summativeQuestionId=" . $questionId;
		//echo $sql;		
		$rows = $objDB->GetDataRow($sql);
		return $rows;
		unset($objDB);
	}


	function SaveSummativeevaluationQuestion($questionId)
	{
		$objDB = new clsDB();
		$sql = '';
		if ($questionId > 0) {

			$sql = "UPDATE schoolsummativequestionmaster SET 						
                                 optionText = '" . addslashes($this->optionText) . "',
                                 schoolSummativeQuestionType = '" . addslashes($this->schoolSummativeQuestionType) . "',
                                 isPosition = '" . addslashes($this->isPosition) . "',
								 sortOrder = '" . addslashes($this->sortOrder) . "'
								Where schoolSummativeQuestionId= " . $questionId;
			//echo 'Insert->'.$sql;exit;
			$objDB->ExecuteQuery($sql);
		} else {
			$sql = "INSERT INTO schoolsummativequestionmaster (optionText,schoolId,schoolSummativeQuestionType,sectionMasterId,isPosition,sortOrder) 
                        VALUES ('" . addslashes($this->optionText) . "',
                                '" . addslashes($this->schoolId) . "',				
                                '" . addslashes($this->schoolSummativeQuestionType) . "',			
                                '" . addslashes($this->sectionMasterId) . "',			
                                '" . addslashes($this->isPosition) . "',
								'" . addslashes($this->sortOrder) . "'		
                                )";
			// echo 'Insert->'.$sql;exit;
			$questionId = $objDB->ExecuteInsertQuery($sql);
		}

		unset($objDB);
		return $questionId;
	}

	function SaveDefaultSummativeevaluationQuestion($questionId)
	{
		$objDB = new clsDB();
		$sql = '';
		if ($questionId > 0) {

			$sql = "UPDATE summativequestionmaster SET 						
                                 optionText = '" . addslashes($this->optionText) . "',
                                 summativeQuestionType = '" . addslashes($this->schoolSummativeQuestionType) . "',
                                 isPosition = '" . addslashes($this->isPosition) . "'
								Where summativeQuestionId= " . $questionId;
			//echo 'Insert->'.$sql;exit;
			$objDB->ExecuteQuery($sql);
		} else {
			$sql = "INSERT INTO summativequestionmaster (optionText,summativeQuestionType,sectionMasterId,isPosition) 
                        VALUES ('" . addslashes($this->optionText) . "',				
                                '" . addslashes($this->schoolSummativeQuestionType) . "',			
                                '" . addslashes($this->sectionMasterId) . "',			
                                '" . addslashes($this->isPosition) . "'				
							)";
			// echo 'Insert->'.$sql;exit;
			$questionId = $objDB->ExecuteInsertQuery($sql);
		}

		unset($objDB);
		return $questionId;
	}

	function GetStudentSummativeDetailsForreport(
		$schoolId,
		$rotationId = 0,
		$studentId,
		$rankId = 0,
		$evaluator = 0,
		$locationId = 0,
		$hospitalSiteId = 0,
		$startdate = '',
		$endtdate = '',
		$ascdesc,
		$sordorder,
		$cbosemester,
		$subcborotation
	) {
		$cbosemester = str_replace(" ", ",", $cbosemester);
		$subcborotation = str_replace(" ", ",", $subcborotation);

		$studentIds = is_array($studentId) ? implode(",", $studentId) : '';
		// $studentIds = $studentId ? implode(',',$studentId) : '';

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT studentsummativemaster.*, student.studentId, student.rankId, 
							student.firstName,student.lastName,rankmaster.rankId,rankmaster.title,
							clinician.clinicianId,clinician.firstName AS clinicianfname,clinician.lastName  
							AS clinicianlname,rotation.rotationId,rotation.title AS rotationname,
							schools.schoolId
							FROM studentsummativemaster
							INNER JOIN schools ON studentsummativemaster.schoolId=schools.schoolId
							INNER JOIN student ON studentsummativemaster.studentId=student.studentId
							INNER JOIN rankmaster ON student.rankId=rankmaster.rankId
							LEFT JOIN clinician ON studentsummativemaster.clinicanId=clinician.clinicianId
							INNER JOIN rotation ON studentsummativemaster.rotationId=rotation.rotationId
							LEFT JOIN courses ON courses.`courseId` = rotation.`courseId`
							LEFT JOIN semestermaster ON courses.`semesterId` = semestermaster.`semesterId`";
		$sql .= " WHERE schools.schoolId= " . $schoolId;

		if ($cbosemester > 0)
			$sql .= " AND semestermaster.semesterId IN ($cbosemester)";
		if ($subcborotation > 0)
			$sql .= " AND (rotation.rotationId IN ($subcborotation) OR rotation.rotationId=" . $rotationId . " )";
		else if ($rotationId > 0)
			$sql .= " AND rotation.rotationId=" . $rotationId;
		// if($rotationId > 0)
		// 	$sql .=" AND rotation.rotationId=" . $rotationId;	 
		// if($subcborotation > 0)
		// 	$sql .=" AND rotation.rotationId IN ($subcborotation)";
		if ($studentIds > 0)
			$sql .= " AND student.studentId IN ($studentIds)";
		if ($rankId > 0)
			$sql .= " AND rankmaster.rankId=" . $rankId;
		if ($startdate != '' || $endtdate != '')
			$sql .= " AND date(studentsummativemaster.createdDate) >= '" . $startdate . "'
									AND date(studentsummativemaster.createdDate) <= '" . $endtdate . "' ";


		if ($ascdesc && $sordorder == 10)
			$sql .= "  ORDER BY rotation.`title` " . $ascdesc;

		else if ($ascdesc && $sordorder == 14)
			$sql .= "  ORDER BY student.firstName " . $ascdesc;

		else if ($ascdesc && $sordorder == 15)
			$sql .= "  ORDER BY student.lastName " . $ascdesc;
		else
			$sql .= " ORDER BY studentsummativemaster.evaluationDate desc";
		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function DeleteSchoolSummative($schoolId)
	{
		$result = "";
		$objDB = new clsDB();
		$sql = "DELETE schoolsummativequestionmaster.*  ,schoolsummativequestiondetail.*
						FROM schoolsummativequestionmaster
						LEFT JOIN schoolsummativequestiondetail ON schoolsummativequestionmaster.schoolSummativeQuestionId=
																 schoolsummativequestiondetail.schoolSummativeQuestionId
						WHERE schoolsummativequestionmaster.schoolId = " . $schoolId;
		//echo $sql;exit;
		$result = $objDB->ExecuteQuery($sql);
		unset($objDB);
		return $result;
	}

	function GetSummativeScore($studentSummativeMasterId)
	{
		$result = "";
		$objDB = new clsDB();
		$sql = "SELECT AVG(SUBSTRING_INDEX(schoolsummativequestiondetail.optionText, '-', 1 )) AS EvaluationScore, studentsummativedetail.studentSummativeDetailId, schoolsummativequestiondetail.schoolSummativeQuestionId 
						FROM studentsummativedetail 
						INNER JOIN studentsummativemaster ON studentsummativedetail.studentSummativeMasterId= studentsummativemaster.studentSummativeMasterId 
						INNER JOIN schoolsummativequestiondetail ON studentsummativedetail.schoolSummativeOptionValue= schoolsummativequestiondetail.schoolSummativeQuestionDetailId 
						AND schoolsummativequestiondetail.schoolSummativeQuestionId= studentsummativedetail.schoolSummativeQuestionId 
						WHERE studentsummativemaster.studentSummativeMasterId = " . $studentSummativeMasterId;
		//echo $sql;exit;
		$result = $objDB->GetDataRow($sql);
		unset($objDB);
		return $result;
	}

	function GetUnsatisfyComments($studentSummativeMasterId)
	{
		$result = "";
		$objDB = new clsDB();
		$sql = "SELECT studentsummativedetail.schoolSummativeOptionAnswerText
				FROM studentsummativemaster 
				INNER JOIN schools ON studentsummativemaster.schoolId=schools.schoolId 
				INNER JOIN studentsummativedetail ON studentsummativedetail.studentSummativeMasterId=studentsummativemaster.studentSummativeMasterId 
				INNER JOIN schoolsummativequestionmaster ON schoolsummativequestionmaster.schoolSummativeQuestionId=studentsummativedetail.schoolSummativeQuestionId
						WHERE studentsummativemaster.studentSummativeMasterId = " . $studentSummativeMasterId . " AND 
						schoolsummativequestionmaster.optionText='Unsatisfactory behavior(s):'";
		//echo $sql;exit;
		$result = $objDB->GetDataRow($sql);
		unset($objDB);
		return $result;
	}

	function GetSuggestionsComments($studentSummativeMasterId)
	{
		$result = "";
		$objDB = new clsDB();
		$sql = "SELECT studentsummativedetail.schoolSummativeOptionAnswerText AS SuggestionsComment
				FROM studentsummativemaster 
				INNER JOIN schools ON studentsummativemaster.schoolId=schools.schoolId 
				INNER JOIN studentsummativedetail ON studentsummativedetail.studentSummativeMasterId=studentsummativemaster.studentSummativeMasterId 
				INNER JOIN schoolsummativequestionmaster ON schoolsummativequestionmaster.schoolSummativeQuestionId=studentsummativedetail.schoolSummativeQuestionId
						WHERE studentsummativemaster.studentSummativeMasterId = " . $studentSummativeMasterId . " AND
						schoolsummativequestionmaster.optionText='Suggestions for improvement:'";
		//echo $sql;exit;
		$result = $objDB->GetDataRow($sql);
		unset($objDB);
		return $result;
	}

	function GetUnsatisfactionQuestions($studentSummativeMasterId)
	{
		$result = "";
		$objDB = new clsDB();
		$sql = "SELECT studentsummativedetail.*,schoolsummativequestiondetail.* , schoolsummativequestionmaster.schoolSummativeQuestionId, 
						GROUP_CONCAT(schoolsummativequestionmaster.optionText) AS optionText 
						FROM studentsummativemaster 
						LEFT JOIN schools ON studentsummativemaster.schoolId=schools.schoolId 
						LEFT JOIN studentsummativedetail ON studentsummativedetail.studentSummativeMasterId=studentsummativemaster.studentSummativeMasterId 
						LEFT JOIN schoolsummativequestionmaster ON schoolsummativequestionmaster.schoolSummativeQuestionId=studentsummativedetail.schoolSummativeQuestionId 
						LEFT JOIN schoolsummativequestiondetail ON schoolsummativequestiondetail.schoolSummativeQuestionDetailId=studentsummativedetail.schoolSummativeOptionValue 	

						WHERE studentsummativemaster.studentSummativeMasterId = " . $studentSummativeMasterId . " AND 
						schoolsummativequestiondetail.schoolOptionValue= 5 ";
		//echo $sql;exit;
		$result = $objDB->GetResultset($sql);
		unset($objDB);
		return $result;
	}

	function GetServerSideStudentSummativeDetailsForreport($currentSchoolId, $studentId, $start, $length, $searchExpression, $orderByColumn, $orderByColumnDir, $courseId, $rotationId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT studentsummativemaster.*, student.studentId, student.rankId, 
							student.firstName,student.lastName,rankmaster.rankId,rankmaster.title,
							clinician.clinicianId,clinician.firstName AS clinicianfname,clinician.lastName  
							AS clinicianlname,rotation.rotationId,rotation.title AS rotationname,
							schools.schoolId
							FROM studentsummativemaster
							INNER JOIN schools ON studentsummativemaster.schoolId=schools.schoolId
							INNER JOIN student ON studentsummativemaster.studentId=student.studentId
							INNER JOIN rankmaster ON student.rankId=rankmaster.rankId
							INNER JOIN clinician ON studentsummativemaster.clinicanId=clinician.clinicianId
							INNER JOIN rotation ON studentsummativemaster.rotationId=rotation.rotationId";
		$sql .= " WHERE schools.schoolId= " . $currentSchoolId;

		if ($studentId > 0)
			$sql .= " AND student.studentId=" . $studentId;
		if ($courseId > 0)
			$sql .= " AND rotation.courseId=" . $courseId;
		if ($rotationId > 0)
			$sql .= " AND rotation.rotationId=" . $rotationId;

		//echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}
	function GetsummavaluationQuestionOptionDetails($questionId)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT schoolsummativequestionmaster. *,schoolsummativequestiondetail.*
						FROM schoolsummativequestionmaster 
						INNER JOIN schoolsummativequestiondetail ON schoolsummativequestionmaster.schoolSummativeQuestionId=
						schoolsummativequestiondetail.schoolSummativeQuestionId
						WHERE schoolsummativequestionmaster.schoolSummativeQuestionId=" . $questionId .
			" ORDER BY schoolsummativequestiondetail.schoolOptionValue";
		//echo $sql;exit;
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetDefaultSummavaluationQuestionOptionDetails($questionId)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT summativequestionmaster. *,summativequestiondetail.*
						FROM summativequestionmaster 
						INNER JOIN summativequestiondetail ON summativequestionmaster.summativeQuestionId=
						summativequestiondetail.summativeQuestionId
						WHERE summativequestionmaster.summativeQuestionId=" . $questionId .
			" ORDER BY summativequestiondetail.summativeOptionValue";
		// echo $sql;exit;
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function SaveSummativeEvaluationQuestionOptions()
	{
		$objDB = new clsDB();
		$sql = '';
		$sql = "INSERT INTO schoolsummativequestiondetail(schoolSummativeQuestionId,optionText,schoolOptionValue) 
						VALUES ('" . addslashes($this->schoolSummativeQuestionId) . "',
								'" . addslashes($this->optionText) . "',					
								'" . addslashes($this->schoolOptionValue) . "'					
								)";
		// echo 'Insert->'.$sql;exit;
		$objDB->ExecuteInsertQuery($sql);
		unset($objDB);
	}

	function SaveDefaultSummativeEvaluationQuestionOptions()
	{
		$objDB = new clsDB();
		$sql = '';
		$sql = "INSERT INTO summativequestiondetail(summativeQuestionId,optionText,summativeOptionValue) 
						VALUES ('" . addslashes($this->schoolSummativeQuestionId) . "',
								'" . addslashes($this->optionText) . "',					
								'" . addslashes($this->schoolOptionValue) . "'					
								)";
		// echo 'Insert->'.$sql;exit;
		$objDB->ExecuteInsertQuery($sql);
		unset($objDB);
	}

	function SetSummativeEvaluationStatus($summativeSectionMasterId, $status)
	{
		if ($summativeSectionMasterId > 0) {
			$objDB = new clsDB();
			$sql = "Update summativesectionmaster set isActive = " . $status . " Where summativeSectionMasterId = " . $summativeSectionMasterId;
			//echo $sql;exit;
			$result = $objDB->ExecuteQuery($sql);
			unset($objDB);
		}
	}

	function GetSummativeQuestionPosition($questionId)
	{
		$objDB = new clsDB();
		$sql = "select  isPosition from schoolsummativequestionmaster   Where schoolSummativeQuestionId = " . $questionId;
		//echo $sql;
		$result = $objDB->GetSingleFieldValue($sql);
		unset($objDB);
		return $result;
	}

	function GetSiteEvaluationSelectedOptionListBySiteEvaluationMasterId($siteEvaluationMasterId)
	{
		$objDB = new clsDB();
		$siteEvaluation = "";
		$sql = "SELECT	schoolsummativequestiondetail.optionText FROM studentsummativedetail
		        INNER JOIN schoolsummativequestiondetail ON 
		            schoolsummativequestiondetail.schoolSummativeQuestionDetailId = studentsummativedetail.schoolSummativeOptionValue";
		$sql .= " WHERE  studentsummativedetail.schoolSummativeOptionValue !=0 AND studentsummativedetail.studentSummativeMasterId=" . $siteEvaluationMasterId;
		//echo $sql;
		$siteEvaluation = $objDB->GetResultset($sql);
		return $siteEvaluation;
		unset($objDB);
	}

	function GetSiteEvaluationOverallRotationEvaluationQuestionId($schoolId)
	{
		$objDB = new clsDB();
		$siteEvaluation = "";
		$sql = "SELECT	schoolSummativeQuestionId FROM schoolsummativequestionmaster
		        WHERE schoolId =" . $schoolId . "  AND optionText LIKE '%Overall Rotation Evaluation%'";
		$siteEvaluation = $objDB->GetSingleFieldValue($sql);
		// echo $sql;
		return $siteEvaluation;
		unset($objDB);
	}

	function GetSiteEvaluationOverallRotationEvaluationQuestion($studentSummativeMasterId, $schoolSummativeQuestionId)
	{
		$objDB = new clsDB();
		$siteEvaluation = "";
		$sql = "SELECT	schoolsummativequestiondetail.optionText FROM studentsummativedetail
		        INNER JOIN schoolsummativequestiondetail ON 
		            studentsummativedetail.schoolSummativeOptionValue = schoolsummativequestiondetail.schoolSummativeQuestionDetailId";
		$sql .= " WHERE  studentsummativedetail.studentSummativeMasterId = " . $studentSummativeMasterId . " 
		        AND studentsummativedetail.schoolSummativeQuestionId = " . $schoolSummativeQuestionId;
		// echo $sql;
		$siteEvaluation = $objDB->GetSingleFieldValue($sql);
		return $siteEvaluation;
		unset($objDB);
	}
	// Get Summative list for app
	function GetAllSummativeForApp($rotationId, $studentId, $isclinician = 0, $limitString, $searchText = '')
	{
		$objDB = new clsDB();
		$sql = "SELECT studentsummativemaster.studentSummativeMasterId,studentsummativemaster.evaluationDate,studentsummativemaster.dateOfStudentSignature,studentsummativemaster.preceptorId,studentsummativemaster.isPreceptorCompletedStatus ,student.studentId,
						rotation.rotationId,rotation.title,student.firstName,student.lastName,
						student.rankId,rankmaster.rankId,rankmaster.title AS Ranktitle,
						rotation.parentRotationId,rotation.locationId as rotationLocationId,courses.locationId,studentsummativemaster.studentSummativeMasterId
						FROM studentsummativemaster
						LEFT JOIN studentsummativedetail ON studentsummativemaster.studentSummativeMasterId=
															studentsummativedetail.studentSummativeMasterId
						LEFT JOIN rotation ON studentsummativemaster.rotationId=rotation.rotationId
						INNER JOIN student ON studentsummativemaster.studentId=student.studentId
						LEFT JOIN  courses ON rotation.courseId =courses.courseId
						LEFT JOIN rankmaster ON student.rankId=rankmaster.rankId";

		if ($isclinician > 0) {
			$sql .=	"  INNER JOIN clinician on clinician.clinicianId = studentsummativemaster.clinicanId ";
		}

		$sql .= " WHERE studentsummativemaster.studentSummativeMasterId !=0 ";
		if ($rotationId > 0) {
			$sql .=	"  AND studentsummativemaster.rotationId=" . $rotationId;
		}
		if ($studentId > 0)
			$sql .= "  AND studentsummativemaster.studentId=" . $studentId;

		if ($isclinician > 0) {

			if ($searchText != "") {
				$sql .= " AND CONCAT(student.firstName, ' ', student.lastName ) LIKE '%" . $searchText . "%'";
			}
		}
		$sql .=	" GROUP BY studentsummativemaster.studentSummativeMasterId";
		$sql .=	" ORDER BY studentsummativemaster.evaluationDate DESC" . $limitString;
		$row = $objDB->GetResultset($sql);
		// echo $sql;
		unset($objDB);
		return $row;
	}

	function GetStudentSummativeTextQuestionDetails($studentSummativeMasterId, $schoolSummativeQuestionId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT studentsummativedetail.studentSummativeDetailId,studentsummativedetail.schoolSummativeQuestionId, schoolsummativequestionmaster.optionText as questionTitle ,  studentsummativedetail.schoolSummativeOptionAnswerText as optionValue, studentsummativemaster.studentSummativeMasterId FROM studentsummativedetail
				INNER JOIN studentsummativemaster ON studentsummativedetail.studentSummativeMasterId= studentsummativemaster.studentSummativeMasterId
				INNER JOIN schoolsummativequestionmaster ON schoolsummativequestionmaster.schoolSummativeQuestionId = studentsummativedetail.schoolSummativeQuestionId
				WHERE studentsummativedetail.schoolSummativeQuestionId =" . $schoolSummativeQuestionId . " AND studentsummativemaster.studentSummativeMasterId =" . $studentSummativeMasterId;

		// echo $sql;	
		$rows = $objDB->GetDataRow($sql);
		return $rows;
		unset($objDB);
	}

	function GetStudentSummativeQuestionDetails($studentSummativeMasterId, $schoolSummativeQuestionId)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT studentsummativedetail.studentSummativeDetailId, schoolsummativequestionmaster.schoolSummativeQuestionId, schoolsummativequestionmaster.optionText AS questionTitle, schoolsummativequestiondetail.schoolSummativeQuestionDetailId, schoolsummativequestiondetail.optionText AS optionValue 
				FROM `studentsummativedetail` 
				INNER JOIN studentsummativemaster ON studentsummativemaster.studentSummativeMasterId = studentsummativedetail.studentSummativeMasterId 
				INNER JOIN schoolsummativequestionmaster ON schoolsummativequestionmaster.schoolSummativeQuestionId = studentsummativedetail.schoolSummativeQuestionId 
				INNER JOIN schoolsummativequestiondetail ON schoolsummativequestiondetail.schoolSummativeQuestionDetailId = studentsummativedetail.schoolSummativeOptionValue 
				WHERE studentsummativemaster.studentSummativeMasterId =" . $studentSummativeMasterId . " AND schoolsummativequestionmaster.schoolSummativeQuestionId =" . $schoolSummativeQuestionId;

		// echo $sql;
		$rows = $objDB->GetDataRow($sql);
		return $rows;
		unset($objDB);
	}

	/**
	 * Logs the summative evaluation audit trail data in the database.
	 *
	 * @param int $summativeMasterId The summative master ID
	 * @param int $retSummativeEvalId The summative evaluation ID
	 * @param int $clinicianId The clinician ID
	 * @param int $userType The user type (1 for admin, 2 for clinician)
	 * @param string $action The action type (ADD, EDIT, DELETE)
	 * @param int $isMobile The is mobile flag (0 for web, 1 for mobile)
	 * @param string $type The type of summative evaluation (section, step)
	 * @param int $isSuperAdmin The is super admin flag (0 for no, 1 for yes)
	 */
	function saveSummativeEvalAuditLog($summativeMasterId, $retSummativeEvalId, $clinicianId, $userType, $action, $isMobile = 0, $type = '', $isSuperAdmin = 0)
	{
		// Instantiate the Logger and Summative classes
		$objLog = new clsLogger();
		$objSummative = new clsSummative();

		// Prepare log data
		[$logData, $rowData, $additionalData] = $objSummative->createSummativeEvalLog($retSummativeEvalId, $action, $clinicianId, $userType, $type, $isSuperAdmin);
		$logData['isMobile'] = $isMobile;

		if ($action == 'Delete') {
			if ($type == 'section') {
				if ($isSuperAdmin) {
					$objSummative->DeleteDefaultSummativeEvaluationSection($retSummativeEvalId);
				} else {
					$objSummative->DeleteSummativeEvaluationSection($retSummativeEvalId);
				}
				// $objSummative->Deletemidsec($id);
			} else if ($type == 'step') {
				if ($isSuperAdmin) {
					// // Initialize database object
					$objDB = new clsDB();

					// Fetch data from `studentmidtermevaldetails` table for the given master ID
					$evaluationDetailsResult = $objDB->GetAllSelectedDataFromTable('summativequestiondetail', '', 'summativeQuestionId', $retSummativeEvalId);
					unset($objDB);

					if ($evaluationDetailsResult) {
						// Convert the result set into an array
						$evaluationDetailsArray = fetchResultAsArray($evaluationDetailsResult);

						// Generate the JSON array (if needed for logs or other purposes)
						$additionalData = $evaluationDetailsArray;

						$objSummative->DeleteDefaultSummativeEvaluationQuestion($retSummativeEvalId);
					}
				} else {
					// // Initialize database object
					$objDB = new clsDB();

					// Fetch data from `studentmidtermevaldetails` table for the given master ID
					$evaluationDetailsResult = $objDB->GetAllSelectedDataFromTable('schoolsummativequestiondetail', '', 'schoolSummativeQuestionId', $retSummativeEvalId);
					unset($objDB);

					if ($evaluationDetailsResult) {
						// Convert the result set into an array
						$evaluationDetailsArray = fetchResultAsArray($evaluationDetailsResult);

						// Generate the JSON array (if needed for logs or other purposes)
						$additionalData = $evaluationDetailsArray;

						$objSummative->DeleteSummativeEvaluationQuestion($retSummativeEvalId);
					}
				}
			} else {
				// // Initialize database object
				$objDB = new clsDB();

				// Fetch data from `studentsummativedetail` table for the given master ID
				$evaluationDetailsResult = $objDB->GetAllSelectedDataFromTable('studentsummativedetail', '', 'studentSummativeMasterId', $retSummativeEvalId);
				unset($objDB);

				if ($evaluationDetailsResult) {
					// Convert the result set into an array
					$evaluationDetailsArray = fetchResultAsArray($evaluationDetailsResult);

					// Generate the JSON array (if needed for logs or other purposes)
					$additionalData = $evaluationDetailsArray;

					$objSummative->DeleteSummative($retSummativeEvalId);
				}
			}
		}
		$moduleName = '';
		if ($type == 'section') {
			$moduleName = " Section";
		} else if ($type == 'step') {
			$moduleName = " Step";
		}
		// Save the log details
		$objLog->saveLogs($logData, $action, $retSummativeEvalId, 'Summative Evaluation' . $moduleName, $rowData, $additionalData);

		// Clean up
		unset($objLog);
		unset($objSummative);

		return true; // Return success or handle further actions as needed
	}

	/**
	 * Creates log data for Summative evaluation logs based on the given ID, action, user ID, user type, and type (section or step)
	 *
	 * @param int $id The ID of the Summative evaluation (section or step) for which the log is being created
	 * @param string $action The action type (Add, Edit, Delete, Active, Inactive, Signoff) for the log
	 * @param int $userId The ID of the user performing the action
	 * @param string $userType The type of user performing the action (Student, Preceptor, Admin, or Super Admin)
	 * @param string $type The type of log (section or step)
	 * @param int $isSuperAdmin The flag indicating whether the user is a super admin or not
	 *
	 * @return array An array containing the log data, the row data from the Summative evaluation table, and any additional data needed for the log
	 */
	function createSummativeEvalLog($id, $action, $userId, $userType, $type = '', $isSuperAdmin = 0)
	{
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$objSummativeEval = new clsSummative(); // Assuming `Summative` class is used for `preparesummativeevalLogData`

		// Get user details based on user type
		$userDetails = getUserDetails($userId, $userType);

		// Retrieve Midterm details for the given midtermeval ID and school ID
		if ($type == 'section') {
			$rowData = $objSummativeEval->GetsummativeEvaluationSectionDetail($id, $isSuperAdmin);
		} else if ($type == 'step') {
			$rowData = $objSummativeEval->GetSummativeStepDetailsForLogs($id, $isSuperAdmin);
		} else {
			$rowData = $objSummativeEval->GetAllSummativeEvalDetailsForLogs($id);
		}
		// echo '<pre>';
		// print_r($rowData);
		// exit;
		if ($rowData != '')
			$logData = $objLog->generateLogData($rowData, $userType);

		$additionalData = '';

		// Populate log data with user details
		$logData['userId'] = $userDetails['userId'];
		$logData['userName'] = $userDetails['userName'];
		$logMessage = '';
		if ($type == 'section') {
			if ($action == 'Add') {
				$logMessage = $logData['userName'] . ' added new section in Summative evaluation.';
			} else if ($action == 'Edit') {
				$logMessage = $logData['userName'] . ' updated the section from Summative evaluation.';
			} else if ($action == 'Delete') {
				$logMessage = $logData['userName'] . ' deleted the section from Summative evaluation.';
			} else if ($action == 'Active') {
				$logMessage = $logData['userName'] . ' activate the section from Summative evaluation.';
			} else if ($action == 'Inactive') {
				$logMessage = $logData['userName'] . ' deactivate the section from Summative evaluation.';
			}
		} else if ($type == "step") {

			if ($action == 'Add') {
				$logMessage = $logData['userName'] . ' added new Step in Summative evaluation section.';
			} else if ($action == 'Edit') {
				$logMessage = $logData['userName'] . ' updated Step from Summative evaluation section.';
			} else if ($action == 'Delete') {
				$logMessage = $logData['userName'] . ' deleted Step from Summative evaluation section.';
			} else if ($action == 'Assign') {
				$logMessage = $logData['userName'] . ' Assign Steps in Summative evaluation section.';
			} else if ($action == 'Unassign') {
				$logMessage = $logData['userName'] . ' Unassign Steps from Summative evaluation section.';
			}
		} else {
			if ($action == 'Add') {
				$logMessage = $logData['userName'] . ' added Summative evaluation for ' . $logData['rotationName'] . ' rotation.';
			} else if ($action == 'Edit') {
				$logMessage = $logData['userName'] . ' updated Summative evaluation from ' . $logData['rotationName'] . ' rotation.';
			} else if ($action == 'Delete') {
				$logMessage = $logData['userName'] . ' deleted Summative evaluation from ' . $logData['rotationName'] . ' rotation.';
			} else if ($action == 'Signoff' && $userType == 'Student') {
				$logMessage = $logData['userName'] . ' signoff Summative evaluation from ' . $logData['rotationName'] . ' rotation.';
			} else if ($action == 'Signoff' && $userType == 'Preceptor') {
				$logMessage = $logData['userName'] . ' signoff Summative evaluation from ' . $logData['rotationName'] . ' rotation.';
			}
		}

		// Construct log message
		$logData['message'] = $logMessage;

		// Return the data
		return [$logData, $rowData, $additionalData];
	}

	/**
	 * This function will return the details of summative evaluation master based on the given $summativeMasterId
	 * If $schoolId is given, then it will return the summative evaluation master details for the given school
	 * @param int $summativeMasterId
	 * @param int $schoolId
	 * @return array
	 */
	function GetAllSummativeEvalDetailsForLogs($summativeMasterId, $schoolId = 0)
	{
		$objDB = new clsDB();

		$sql = "SELECT studentsummativemaster.*, rotation.title as rotationName,studentsummativemaster.studentId as userId,
				CONCAT(student.firstName, ' ', student.lastName) AS userName,hospitalsites.title AS hospitalSiteName, 
				schools.displayName as schoolName
		FROM `studentsummativemaster` 
		INNER JOIN rotation ON studentsummativemaster.`rotationId` = rotation.`rotationId` 
		INNER JOIN student ON student.studentId=studentsummativemaster.studentId
		LEFT JOIN hospitalsites ON hospitalsites.hospitalSiteId = rotation.hospitalSiteId
		INNER JOIN schools ON schools.schoolId = studentsummativemaster.schoolId
		WHERE studentSummativeMasterId =" . $summativeMasterId;

		if ($schoolId) {
			$sql .= " AND rotation.schoolId=" . $schoolId;
		}
		// echo $sql;
		// exit;
		$row = $objDB->GetDataRow($sql);
		unset($objDB);
		return $row;
	}

	/**
	 * This function will return the details of summative section master based on the given $sectionMasterId
	 * If $isSuperAdmin is set to 1, then it will return the default summative section master details
	 * @param int $sectionMasterId
	 * @param int $isSuperAdmin
	 * @return array
	 */
	function GetSummativeSectionDetailsForLogs($sectionMasterId, $isSuperAdmin = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		if ($isSuperAdmin) {
			$sql = "SELECT defaultsummativesectionmaster.* FROM  defaultsummativesectionmaster
				WHERE sectionMasterId=" . $sectionMasterId;
		} else {

			$sql = "SELECT summativesectionmaster.*,schools.displayName as schoolName FROM  summativesectionmaster
				INNER JOIN schools ON schools.schoolId = summativesectionmaster.schoolId
				WHERE summativeSectionMasterId=" . $sectionMasterId;
		}

		$rows = $objDB->GetDataRow($sql);
		return $rows;
		unset($objDB);
	}

	/**
	 * Retrieves Summative evaluation Step details for logs.
	 * 
	 * This function fetches details of a summative evaluation step based on the 
	 * provided question ID. If the user is a super admin, it retrieves details 
	 * from the default summative question master table. Otherwise, it fetches 
	 * data from the school summative question master table and joins with 
	 * question type, section, and school details.
	 *
	 * @param int $questionId Summative evaluation question ID.
	 * @param int $isSuperAdmin Optional. Indicates whether the user is a super admin. Default is 0.
	 *
	 * @return mixed Summative evaluation Step details.
	 */

	function GetSummativeStepDetailsForLogs($questionId, $isSuperAdmin = 0)
	{
		$objDB = new clsDB();
		$row = "";
		if ($isSuperAdmin) {
			$sql = "SELECT summativequestionmaster.* FROM  summativequestionmaster
				WHERE summativeQuestionId=" . $questionId;
		} else {
			$sql = "SELECT schoolsummativequestionmaster.*,questiontypemaster.title as questionType,schools.displayName as schoolName,summativesectionmaster.title AS SectionTitle FROM  schoolsummativequestionmaster
				LEFT JOIN questiontypemaster ON schoolsummativequestionmaster.schoolSummativeQuestionType=questiontypemaster.questionType
				INNER JOIN summativesectionmaster ON summativesectionmaster.summativeSectionMasterId = schoolsummativequestionmaster.sectionMasterId
				INNER JOIN schools ON schools.schoolId = schoolsummativequestionmaster.schoolId
				WHERE  schoolsummativequestionmaster.schoolSummativeQuestionId=" . $questionId;
		}

		// echo $sql;exit;

		$row = $objDB->GetDataRow($sql);
		return $row;
		unset($objDB);
	}
}
