.masonry-grid {
	display: flex;
	flex-wrap: wrap;
	/* justify-content: center; */
	gap: 20px;
}

/* Define CSS Variables for better maintainability and consistency */
:root {
	--primary-color: #22c55e;
	/* Vibrant green for primary elements */
	--secondary-color: #86efac;
	/* Lighter shade of green */
	--text-color: #333;
	/* Dark text for readability */
	--light-text-color: #666;
	/* Slightly lighter text for secondary info */
	--background-color: #f0f2f5;
	/* Light grey for the overall background */
	--card-background: #ffffff;
	/* White for card backgrounds */
	--border-color: #e2e8f0;
	/* Light border color */
	--shadow-light: 0 4px 12px rgba(0, 0, 0, 0.08);
	/* Subtle shadow */
	--border-radius: 8px;
	/* Consistent border radius */
	--spacing-md: 20px;
	--spacing-lg: 30px;
}

/* Card styling for containers */
.graph-card {
	background: var(--card-background);
	border-radius: 25px;
	box-shadow: var(--shadow-light);
	padding: var(--spacing-md);
	/* margin-bottom: var(--spacing-md); */
	width: 100%;
}

.filters {
	width: 30%;
	display: flex;
	flex-direction: column;
	flex-wrap: wrap;
	gap: var(--spacing-md);
	/* align-items: flex-end; */
}

.filters>div {
	/* This ensures each filter group has its own space */
	display: flex;
	flex-direction: column;
	width: 100%;
}

#super-admin-schoolChart {
	min-width: 814px;
	max-width: 814px;
	max-height: 406px;
    min-height: 406px;
}

#schoolChart {
	min-width: 814px;
	max-width: 814px;
	max-height: 406px;
    min-height: 406px;
}

.filters label {
	font-weight: 600;
	/* Slightly bolder for labels */
	color: var(--light-text-color);
	margin-bottom: 5px;
	/* Space between label and input */
}

/* Style for input and select elements */
.filters input[type="date"],
.filters select {
	padding: 10px 12px;
	border: 1px solid var(--border-color);
	border-radius: var(--border-radius);
	font-size: 0.95em;
	color: var(--text-color);
	background-color: var(--card-background);
	transition: all 0.2s ease-in-out;
	min-width: 150px;
	/* Ensure inputs have a decent minimum width */
}

.filters input[type="date"]:focus,
.filters select:focus {
	outline: none;
	border-color: var(--primary-color);
	box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.2);
	/* Green shadow for focus */
}

/* Button Styling */
.filter-buttons {
	display: flex;
	flex-direction: row !important;
	gap: 10px;
	/* Remove margin-top as align-items: flex-end on .filters will handle vertical alignment */
}

.filter-buttons button {
	padding: 10px 18px;
	border: none;
	border-radius: var(--border-radius);
	font-size: 0.95em;
	cursor: pointer;
	transition: background-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
	font-weight: 600;
}

.filter-buttons #saveFilterBtn {
	background-color: var(--primary-color);
	color: white;
}

.filter-buttons #saveFilterBtn:hover {
	background-color: #16a34a;
	/* Darker green on hover */
	box-shadow: 0 2px 8px rgba(34, 197, 94, 0.3);
}

.filter-buttons #resetFilterBtn {
	background-color: #cbd5e1;
	/* Light grey */
	color: var(--text-color);
}

.filter-buttons #resetFilterBtn:hover {
	background-color: #94a3b8;
	/* Darker grey on hover */
	color: white;
}

canvas {
	width: 100% !important;
	/* Ensure canvas takes full width of its container */
	height: auto !important;
	/* Maintain aspect ratio */
}

/* Modal styling */
#modal {
	display: none;
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.6);
	/* Slightly darker overlay */
	align-items: center;
	justify-content: center;
	z-index: 9999;
	opacity: 0;
	/* Start hidden for transition */
	transition: opacity 0.3s ease-out;
	/* Smooth fade-in/out */
}

#modal.is-open {
	/* Class added when modal is open */
	opacity: 1;
	display: flex;
	/* Display after transition */
}

#modalContent {
	background: var(--card-background);
	/* padding: var(--spacing-lg); */
	padding: 25px;
	border-radius: var(--border-radius);
	width: 350px;
	/* Slightly wider modal */
	max-width: 90%;
	box-shadow: var(--shadow-light);
	text-align: left;
	position: relative;
	transform: translateY(-20px);
	/* Start slightly above for animation */
	transition: transform 0.3s ease-out;
	/* Smooth slide-in */
}

#modal.is-open #modalContent {
	transform: translateY(0);
	/* Slide into place */
}

#modalContent h3 {
	/* color: var(--primary-color); */
	margin-top: 0;
	margin-bottom: var(--spacing-md);
	font-size: 1.5em;
}

#modalBody p {
	margin-bottom: 8px;
	font-size: 0.95em;
}

#modalBody strong {
	color: var(--text-color);
}

.closeBtn {
	position: absolute;
	top: 15px;
	right: 20px;
	font-size: 24px;
	cursor: pointer;
	color: var(--light-text-color);
	transition: color 0.2s ease-in-out;
}

.closeBtn:hover {
	color: var(--primary-color);
}

/* Responsive adjustments */
@media (max-width: 768px) {
	body {
		padding: var(--spacing-md);
	}

	.filters {
		flex-direction: column;
		/* Stack filters vertically on small screens */
		align-items: flex-start;
	}

	.filters>div {
		width: 100%;
		/* Make each filter take full width */
	}

	.filters input[type="date"],
	.filters select {
		width: 100%;
		/* Make inputs full width */
		min-width: unset;
	}

	.filter-buttons {
		flex-direction: column;
		/* Stack buttons vertically on small screens */
		width: 100%;
	}

	.filter-buttons button {
		width: 100%;
	}
}

:root {
	--primary-color: #22c55e;
	--secondary-color: #86efac;
	--text-color: #333;
	--light-text-color: #666;
	--background-color: #f0f2f5;
	--card-background: #ffffff;
	--border-color: #e2e8f0;
	--shadow-light: 0 4px 12px rgba(0, 0, 0, 0.08);
	--border-radius: 8px;
	--spacing-md: 20px;
	--spacing-lg: 30px;
}

canvas {
	width: 100% !important;
	height: auto !important;
}

.filters {
	display: flex;
	flex-wrap: wrap;
	gap: 16px;
	/* align-items: flex-end; */
}

.filters div {
	display: flex;
	flex-direction: column;
}

.filters input,
.filters select {
	padding: 8px;
	border-radius: 6px;
	border: 1px solid var(--border-color);
	font-size: 14px;
}

.filters button {
	padding: 8px 16px;
	background-color: var(--primary-color);
	color: white;
	border: none;
	border-radius: 6px;
	cursor: pointer;
}

#super-admin-modal {
	display: none;
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.6);
	align-items: center;
	justify-content: center;
	z-index: 9999;
	opacity: 0;
	transition: opacity 0.3s ease-out;
}

#super-admin-modal.is-open {
	opacity: 1;
	display: flex;
}

#super-admin-modalContent {
	background: var(--card-background);
	/* padding: var(--spacing-lg); */
	padding: 25px !important;
	border-radius: var(--border-radius);
	width: 350px;
	max-width: 90%;
	box-shadow: var(--shadow-light);
	text-align: left;
	position: relative;
	transform: translateY(-20px);
	transition: transform 0.3s ease-out;
}

#super-admin-modal.is-open #super-admin-modalContent {
	transform: translateY(0);
}

#super-admin-modalContent h3 {
	color: var(--primary-color);
	margin-top: 0;
	margin-bottom: var(--spacing-md);
	font-size: 1.5em;
}

#super-admin-modalBody p {
	margin-bottom: 8px;
	font-size: 0.95em;
}

.closeBtn {
	position: absolute;
	top: 15px;
	right: 20px;
	font-size: 24px;
	cursor: pointer;
	color: var(--light-text-color);
	transition: color 0.2s ease-in-out;
}

.closeBtn:hover {
	color: var(--primary-color);
}

.filters>div {
	display: flex;
	flex-direction: column;
	gap: 10px;
}

#super-admin-resetFilterBtn {
	background-color: #cbd5e1;
	color: var(--text-color);
}

#super-admin-resetFilterBtn:hover {
	background-color: #94a3b8;
	color: white;
}

@media (max-width: 768px) {
	body {
		padding: var(--spacing-md);
	}

	.filters {
		flex-direction: column;
		align-items: stretch;
	}
}


/* super admin graph style */

canvas {
	width: 100% !important;
	height: auto !important;
}

.filters {
	display: flex;
	flex-wrap: wrap;
	gap: 16px;
	/* align-items: flex-end; */
}

.filters div {
	display: flex;
	flex-direction: column;
}

.filters input,
.filters select {
	padding: 8px;
	border-radius: 6px;
	border: 1px solid var(--border-color);
	font-size: 14px;
}

.filters button {
	padding: 8px 16px;
	background-color: var(--primary-color);
	color: white;
	border: none;
	border-radius: 6px;
	cursor: pointer;
}

#super-admin-modal {
	display: none;
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.6);
	align-items: center;
	justify-content: center;
	z-index: 9999;
	opacity: 0;
	transition: opacity 0.3s ease-out;
}

#super-admin-modal.is-open {
	opacity: 1;
	display: flex;
}

#super-admin-modalContent {
	background: var(--card-background);
	padding: var(--spacing-lg);
	border-radius: var(--border-radius);
	width: 350px;
	max-width: 90%;
	box-shadow: var(--shadow-light);
	text-align: left;
	position: relative;
	transform: translateY(-20px);
	transition: transform 0.3s ease-out;
}

#super-admin-modal.is-open #super-admin-modalContent {
	transform: translateY(0);
}

#super-admin-modalContent h3 {
	color: var(--primary-color);
	margin-top: 0;
	margin-bottom: var(--spacing-md);
	font-size: 1.5em;
}

#super-admin-modalBody p {
	margin-bottom: 8px;
	font-size: 0.95em;
}

.closeBtn {
	position: absolute;
	top: 15px;
	right: 20px;
	font-size: 24px;
	cursor: pointer;
	color: var(--light-text-color);
	transition: color 0.2s ease-in-out;
}

.closeBtn:hover {
	color: var(--primary-color);
}

.filters>div {
	display: flex;
	flex-direction: column;
	gap: 10px;
}

#super-admin-resetFilterBtn {
	background-color: #cbd5e1;
	color: var(--text-color);
}

#super-admin-resetFilterBtn:hover {
	background-color: #94a3b8;
	color: white;
}

@media (max-width: 768px) {
	body {
		padding: var(--spacing-md);
	}

	.filters {
		flex-direction: column;
		align-items: stretch;
	}
}