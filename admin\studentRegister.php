<?php
// include('includes/validateUserLogin.php'); 
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../setRequest.php');
include_once('../class/clsSchool.php');
include('../class/clsStudent.php');
include('../class/clsLocations.php');
include('../class/clsStudentRankMaster.php');
include('../class/clsSystemUserRoleMaster.php');
include_once('../class/clsCountryStateMaster.php');

$Id = '';
$schoolId = '';
$firstName = '';
$middleName  = '';
$lastName = '';
$address1  = '';
$address2  = '';
$city  = '';
$stateId  = $CurrentSchoolStateId;
$defaultStudentImagePath = '';
$defaultImageName = '';
$zip  = '';
$email  = '';
$phone  = '';
$cellPhone  = '';
$username  = '';
$locationId = '';
$rankId = '';
$studentId  = 0;
$displayName  = '';
$dbStateId = '';
$recordIdNumber = 0;
$schoolId = $currentSchoolId;

$email = isset($_GET['email']) ? DecodeQueryData($_GET['email']) : '';
$isPrivate = isset($_GET['isPrivate']) ? DecodeQueryData($_GET['isPrivate']) : 0;


// if ($email != '' && $isPrivate == 1) { echo 'disabled'; } else { echo 'enabled'; }
//Get School Details
$objSchool = new clsSchool();
$row = $objSchool->GetSchoolDetails($schoolId);
unset($objSchool);

if ($row == '') {
    $objTemplateEngine = new clsTemplateEngine(0, '');
    $loadTemplateName = '../common/template/404.html';
    echo $objTemplateEngine->render($loadTemplateName);
    unset($objTemplateEngine);
    exit;
    exit;
}

$address1  = stripslashes($row['address1']);
$address2  = stripslashes($row['address2']);
$dbStateId  = ($row['stateId']);
$city  = stripslashes($row['city']);
$zip  = stripslashes($row['zip']);
$displayName  = stripslashes($row['displayName']);

$title = "Student Registration Form";

$defaultStudentImagePath = GetStudentImagePath($studentId, $schoolId, $defaultImageName);

//CountryState
$objCountryStateMaster = new clsCountryStateMaster();
$countries = $objCountryStateMaster->GetAllCountry();
$dbCountryId = $objCountryStateMaster->GetParentIdFromChildId($dbStateId);
unset($objCountryStateMaster);

//Locations
$objLocations = new clsLocations();
$locations = $objLocations->GetAlllocation($schoolId);
unset($objLocations);

//StudentRank
$objStudentRankMaster = new clsStudentRankMaster();
$ranks = $objStudentRankMaster->GetAllStudentRankBySchool($schoolId);
unset($objStudentRankMaster);

?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo ($title); ?></title>
    <?php include('includes/headercss.php'); ?>

    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/css/magnific-popup.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
    <style>
        input:read-only {
            background-color: transparent !important;
        }

        .formSubHeading {
            margin-left: 15px !important;
            margin-right: 15px !important;
            width: -webkit-fill-available;
        }

        .student-logo-section {
            display: flex;
            align-items: center;
        }

        @media screen and (max-width: 500px) {
            .formSubHeading {
                font-size: 16px;
                margin-bottom: 12px;
                padding-bottom: 0;
            }

            .breadcrumb-bg {
                margin-bottom: 5px;
            }

            .student-logo-section {
                align-items: start;
                flex-direction: column;
            }
        }
    </style>
</head>

<body>
    <?php include('includes/header.php'); ?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <!-- <li class="active"><?php echo $displayName; ?></li> -->
                    <li class=""><?php echo $title; ?></li>
                </ol>
            </div>

        </div>
    </div>

    <div class="container">

        <div class="pageheading  formSubHeading text-center" style="font-size: 20px;"><?php echo $displayName; ?></div>

        <form id="frmSystemStudent" data-parsley-validate class="form-horizontal" method="POST" action="addstudentsubmit.html?id=<?php echo (EncodeQueryData($studentId)); ?>&schoolId=<?php echo (EncodeQueryData($schoolId)); ?>" enctype="multipart/form-data">
            <div class="row ">

                <div class="formSubHeading">Student Information</div>
                <!-- Text input-->
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="recordIdNumber">Student Id</label>
                        <div class="col-md-12">
                            <input id="recordIdNumber" name="recordIdNumber" value="" type="text" placeholder="" class="form-control input-md validateAlphaNumeric" onchange="validateData('recordId');">
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="txtFirstName">First Name</label>
                        <div class="col-md-12">
                            <input id="txtFirstName" name="txtFirstName" value="<?php echo ($firstName); ?>" required type="text" placeholder="" class="form-control input-md required-input" required="">
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="txtMiddleName">Middle Name</label>
                        <div class="col-md-12">
                            <input id="txtMiddleName" name="txtMiddleName" value="<?php echo ($middleName); ?>" type="text" placeholder="" class="form-control input-md ">
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6"> <!-- Select Basic -->
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="txtLastName">Last Name</label>
                        <div class="col-md-12">
                            <input id="txtLastName" name="txtLastName" value="<?php echo ($lastName); ?>" required type="text" placeholder="" class="form-control input-md required-input" required="">
                        </div>
                    </div>
                </div>
                <div class="col-md-6">


                    <!-- Text input-->
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="txtEmail">Email</label>
                        <div class="col-md-12">
                            <input id="txtEmail" name="txtEmail" value="<?php echo ($email); ?>" required type="email" placeholder="" class="form-control input-md required-input" onblur="updateUsername(this.value)">

                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <!-- Text input-->
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="txtPhone">Preferred Phone</label>
                        <div class="col-md-12">
                            <input id="txtPhone" maxlength="12" data-inputmask-alias="************" name="txtPhone" maxlength="12" type="text" placeholder="" value="<?php echo ($phone); ?>" required class="form-control input-md required-input" onchange="validateData('mobileNo');">
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="txtcellPhone">Alternate Phone</label>
                        <div class="col-md-12">
                            <input id="txtcellPhone" data-inputmask-alias="************" maxlength="12" name="txtcellPhone" type="text" placeholder="" value="<?php echo ($cellPhone); ?>" class="form-control input-md">
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="margin-top-5" style="display: flex; align-items: start;margin-top: 5px;margin-bottom: 10px;">
                        <input type="checkbox" checked id="chkSmsOptIn" name="chkSmsOptIn" value="1" required>
                         <label for="chkSmsOptIn" style="margin-left: 7px; margin-bottom: 0;">By checking this box, I acknowledge I am opting in to receiving SMS messages on behalf of my Clinical Trac™ clinical management tool. I agree to the <a href="https://clinicaltrac.com/privacy.html" target="_blank"> Privacy Policy </a> and <a href="https://clinicaltrac.com/termsandconditions.html" target="_blank"> Terms and Conditions. </a></label>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="formSubHeading">Address Information</div>

                <div class="col-md-6">

                    <!-- Text input-->
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="txtAddress1">Address 1</label>
                        <div class="col-md-12">
                            <input id="txtAddress1" name="txtAddress1" type="text" value="<?php echo ($address1); ?>" class="form-control input-md required-input" required>

                        </div>
                    </div>
                </div>
                <div class="col-md-6">

                    <!-- Text input-->
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="txtAddress2">Address 2</label>
                        <div class="col-md-12">
                            <input id="txtAddress2" name="txtAddress2" type="text" placeholder="" value="<?php echo ($address2); ?>" class="form-control input-md">

                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="cboCountry">Country</label>
                        <div class="col-md-12 flex-col-reverse">
                            <select id="cboCountry" name="cboCountry" class="form-control step1 input-md  select2_single" required>
                                <option value="" selected>Select</option>
                                <?php
                                if ($countries != "") {
                                    while ($row = mysqli_fetch_assoc($countries)) {
                                        $location_id  = $row['location_id'];
                                        $name  = stripslashes($row['name']);

                                ?>
                                        <option value="<?php echo ($location_id); ?>" <?php if ($dbCountryId == $location_id) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
                                <?php

                                    }
                                }
                                ?>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <!-- Select Basic -->
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="txtCity">City</label>
                        <div class="col-md-12">
                            <input id="txtCity" name="txtCity" type="text" required value="<?php echo ($city); ?>" class="form-control input-md required-input">
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">

                    <!-- Select Basic -->
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="cboState">State</label>
                        <div class="col-md-12 flex-col-reverse">
                            <select id="cboState" name="cboState" class="form-control step2 input-md select2_single" required>
                                <option value="" selected>Select</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">

                    <!-- Text input-->
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="txtZipCode">Zip Code</label>
                        <div class="col-md-12">
                            <input id="txtZipCode" name="txtZipCode" type="text" placeholder="" value="<?php echo ($zip); ?>" class="form-control input-md">
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="formSubHeading">Access Information</div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="txtUsername">Username</label>
                        <div class="col-md-12">
                            <input id="txtUsername" name="txtUsername" value="<?php echo ($username); ?>" required type="text" placeholder="" class="form-control input-md required-input" readonly>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <?php if (!isset($_GET['id'])) { ?>
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="txtpassword">Password</label>
                            <div class="col-md-12">
                                <input id="txtpassword" name="txtpassword" value="" required type="password" placeholder="" class="form-control input-md required-input ">
                            </div>
                        </div>
                    <?php } ?>
                </div>
            </div>
            <div class="row">


                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="cborank">Rank</label>
                        <div class="col-md-12">
                            <select id="cborank" name="cborank" class="form-control input-md required-input select2_single" required>
                                <!--option value="" selected>Select</option-->
                                <?php
                                if ($ranks != "") {
                                    while ($row = mysqli_fetch_assoc($ranks)) {
                                        $selrankId  = $row['rankId'];
                                        $name  = stripslashes($row['title']);

                                ?>
                                        <option value="<?php echo ($selrankId); ?>" <?php if ($rankId == $selrankId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
                                <?php

                                    }
                                }
                                ?>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="cbolocation">Location</label>
                        <div class="col-md-12">
                            <select id="cbolocation" name="cbolocation" class="form-control input-md required-input select2_single" required>
                                <!--option value="" selected>Select</option-->
                                <?php
                                if ($locations != "") {
                                    while ($row = mysqli_fetch_assoc($locations)) {
                                        $sellocationId  = $row['locationId'];
                                        $name  = stripslashes($row['title']);

                                ?>
                                        <option value="<?php echo ($sellocationId); ?>" <?php if ($locationId == $sellocationId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
                                <?php

                                    }
                                }
                                ?>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">

                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="fileLogo">Student Photo</label>
                        <div class="col-md-12 student-logo-section">
                            <div>

                                <?php
                                if ($defaultStudentImagePath != '') {
                                ?>
                                    <!-- <br> -->
                                    <a class="image-preview" title="Logo Preview" href="<?php echo ($defaultStudentImagePath . "?randId=" . $randormRefreshId) ?>"><img class="img-thumbnail" style="width:100px;height:100px; margin-right: 20px;" src="<?php echo ($defaultStudentImagePath . "?randId=" . $randormRefreshId) ?>" alt="" border="0" /></a>

                                <?php
                                }
                                ?>
                            </div>
                            <div>
                                <input type="file" id="fileLogo" name="fileLogo" class="imageUploadLimit mb-5">
                                <input type="checkbox" name="chkAutoCrop" id="chkAutoCrop" checked>
                                <label for="chkAutoCrop">Auto Crop</label><br>

                                <div class="small-info">Recommended Width 200px X Height 200px<br />Browse image only: .png, .jpg, .gif</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group hide">
                        <label class="col-md-12 control-label" for="chkemailtopassword">Email Password</label>
                        <div class="col-md-12">
                            <input id="chkemailtopassword" name="chkemailtopassword" value="1" type="checkbox" class="input-md" checked> E-mail password to student.
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <!-- <label class="col-md-2 control-label"></label> -->
                <div class="col-md-12" style="display: flex; justify-content: center; margin: 0 0 20px 0;gap: 15px;">
                    <button id="btnSubmit" name="btnSubmit" class="btn btn-success margin_left_ten">Submit</button>
                    <a type="button" href="javascript:window.location.href=window.location.href" class="btn btn-default margin_left_five">Clear</a>
                </div>
            </div>
        </form>
    </div>

    <?php include('includes/footer.php'); ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/jquery.cascadingdropdown.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/js/jquery.magnific-popup.min.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/custom.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/inputmask/jquery.inputmask.bundle.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/inputmask/inputmask.js"></script>

    <script type="text/javascript">
        $('#inputId').attr('readonly', true);

        $('.addCommentpopup').magnificPopup({
            'type': 'ajax'
        });
        <?php if ($studentId == 0) { ?>
            //  $('#txtFirstName').blur(function(){
            // 	 var autousername = $('#txtFirstName').val()+"<?php echo ($schoolId); ?>";


            // 	 $.ajax({
            // 			type: "POST",			
            // 			url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_check_duplicate_username.html",
            // 			data: {userId:'<?php echo (EncodeQueryData($studentId)); ?>',userName:autousername,type:'student',schoolId:'<?php echo (EncodeQueryData($currentSchoolId)); ?>'},
            // 			success:function(responseData)
            // 			{
            // 				if(responseData==1)
            // 				{
            // 					autousername = $('#txtFirstName').val()+"_"+$('#txtLastName').val()+"_"+"<?php echo ($randormRefreshId); ?>";
            // 					$("#txtUsername").val(autousername);
            // 				}
            // 				else{
            // 					$("#txtUsername").val(autousername);
            // 				}
            // 			}
            // 	}); 


            //  });
        <?php } ?>


        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";

        $(window).load(function() {

            $(".select2_single").select2();
            $('#select2-cboCountry-container').addClass('required-select2');
            $('#select2-cboState-container').addClass('required-select2');
            $('#select2-cbolocation-container').addClass('required-select2');
            $('#select2-cborank-container').addClass('required-select2');

            $('#frmSystemStudent').parsley().on('field:validated', function() {
                    var ok = $('.parsley-error').length === 0;
                })
                .on('form:submit', function() {
                    ShowProgressAnimation();
                    return true; // Don't submit form for this demo
                });

            <?php
            if ($dbCountryId == 0) {
            ?>
                $('#cboCountry').val('224').trigger('change');
            <?php
            }
            ?>

            var email = "<?php echo $email; ?>";
            var isPrivate = "<?php echo $isPrivate; ?>";
            var schoolId = "<?php echo $schoolId; ?>";

            if (email != '' && isPrivate == 1) {
                $('#txtEmail').prop('readonly', true);
                $('#txtEmail').trigger('change');
                $('#txtUsername').val(email);
                checkEmailExists(email, function(respData) {
                    if (respData == 1) {
                        // alert('hh');
                        window.location.href = 'thankyou.html?isAlreadyRegistered=' + btoa(1);
                    }
                });
                // saveSchedule(islast, 0, function(respData) 
            }


            $('#frmSystemStudent').cascadingDropdown({
                selectBoxes: [{
                        selector: '.step1',
                        selected: '<?php echo ($dbCountryId); ?>'
                    },
                    {
                        selector: '.step2',
                        selected: '<?php echo ($dbStateId); ?>',
                        requires: ['.step1'],
                        requireAll: true,
                        source: function(request, response) {

                            $.getJSON('<?php echo ($dynamicOrgUrl); ?>/ajax/getStates.html', request, function(data) {
                                response($.map(data, function(item, index) {
                                    return {
                                        label: item['StateName'],
                                        value: item['StateId']
                                    };
                                }));
                            });
                        }
                    }
                ]

            });


            $("#txtUsername").change(function() {
                var currentUsername = $(this).val();
                $.ajax({
                    type: "POST",

                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_check_duplicate_username.html",
                    data: {
                        userId: '<?php echo (EncodeQueryData($studentId)); ?>',
                        userName: currentUsername,
                        type: 'student',
                        schoolId: '<?php echo (EncodeQueryData($currentSchoolId)); ?>'
                    },
                    success: function(responseData) {
                        if (responseData == 1) {
                            alertify.error("Username available.");
                            $("#txtUsername").val('').focus();
                        }
                    }
                });
            });

            //Check Email Already Exist Or Not
            $("#txtEmail").change(function() {

                var currentEmail = $(this).val();
                checkEmailExists(currentEmail);


            });

            $('.image-preview').magnificPopup({
                type: 'image'
            });

        });

        function checkEmailExists(currentEmail, callback) {
            // alert('hi');
            var schoolId = "<?php echo $schoolId; ?>";
            $.ajax({
                type: "POST",

                url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_check_duplicate_email.html",
                data: {
                    type: 'student',
                    currentEmail: currentEmail,
                    schoolId: schoolId
                },
                success: function(responseData) {
                    // if (responseData == 1) {
                    //     alertify.error("Email Already Exist.");
                    //     $("#txtEmail").val('');
                    // }
                    callback(responseData);
                }
            });
        }

        // Onchange of email get username	  
        function updateUsername(val) {
            document.getElementById('txtUsername').value = val;
        }

        function validateData(type) {
            if (type == 'recordId')
                var value = $('#recordIdNumber').val();
            else
                var value = $('#txtPhone').val();
            var schoolId = '<?php echo $schoolId; ?>';
            var schoolSlug = '<?php echo $schoolSlug; ?>';
            $.ajax({
                url: '<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_validate_student.html',
                type: 'POST',
                data: {
                    value: value,
                    type: type,
                    schoolId: schoolId

                },
                dataType: 'json',
                success: function(response) {
                    var status = response['status'];
                    var userId = response['studentId'];
                    var email = response['email'];
                    console.log(response);
                    if (status == 1) {
                        if (type == 'recordId') {
                            alertify.error("Student ID is already exist");
                            $('#recordIdNumber').val('');
                            return false;
                        } else {
                            // alertify.error("Phone number is already exist");
                            $('#txtPhone').val('');
                            alertify.confirm('Account Already Exist! ', 'Please log in to your Clinical Trac Account associated with ' + email + ' in order to edit your profile.', function() {
                                window.location.href = '<?php echo ($dynamicOrgUrl); ?>/school/<?php echo ($schoolSlug); ?>/student/index.html';

                            }, function() {});
                            return false;
                        }
                    } else {
                        // Username is available
                        // resolve();
                    }
                },
                error: function(xhr) {
                    // Handle error
                    console.log(xhr.responseText);
                    reject();
                }
            });

        }
        $('#myForm').on('submit', function(event) {
            event.preventDefault(); // Prevent default form submission

            // Validate the form using Parsley.js
            if ($('#myForm').parsley().isValid()) {
                // Form is valid, continue with form submission
                // validateData('recordId');
                // validateData('mobileNo');
                $('#myForm')[0].submit();
            }
        });
    </script>

</body>

</html>