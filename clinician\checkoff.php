<?php
@session_start();
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../class/clsDB.php');
include('../includes/commonfun.php');
include('../class/clsStudent.php');
include('../class/clscheckoff.php');
include('../class/clsRotation.php');
include('../class/clsLocations.php');
include('../class/clsStudentRankMaster.php');
include('../class/clsExternalPreceptors.php');
include('../setRequest.php');

$currentstudentId = 0;
$totalCheckOffDetail = '';
$selectedStudentId = '';
$rotationId = '';
$rankId = 0;
$Type = '';
$schoolId = $currentSchoolId;
$studentId = 0;

$isActiveCheckoff = $_SESSION["isActiveCheckoff"];

$TimeZone =  $_SESSION["loggedClinicianSchoolTimeZone"];
$loggedClinicianType = $_SESSION['loggedClinicianType'];
$clinicianId = $_SESSION["loggedClinicianId"];

$objrotation = new clsRotation();
$objStudent = new clsStudent();
$objStudentRankMaster = new clsStudentRankMaster();
if (isset($_GET['rotationId'])) //Edit Mode
{
    $rotationId = DecodeQueryData($_GET['rotationId']);
}

//For Student
if (isset($_GET['studentId'])) {
    $currentstudentId = DecodeQueryData($_GET['studentId']);
    $selectedStudentId = DecodeQueryData($_GET['studentId']);
}

//For Type
if (isset($_GET['Type'])) {
    $Type = ($_GET['Type']);
}

//For Filter By Filter
if (isset($_GET['rankId'])) {
    $rankId = $_GET['rankId'];
    $rankId = DecodeQueryData($rankId);
}
//For Rotation
if (isset($_GET['rotationId'])) {
    $rotationId = DecodeQueryData($_GET['rotationId']);
}

//For Checkoff List
$objcheckoff = new clscheckoff();
if ($loggedClinicianType == 'p' || $loggedClinicianType == 'P' || $loggedClinicianType == 'C' || $loggedClinicianType == 'c') {
    $rowsCheckOffDetail = $objcheckoff->GetCheckOffDetailByClinician($currentstudentId, $currentSchoolId, $rotationId, $clinicianId, $rankId);
    //Get Rotation in Dropdown 
    $rotation = $objrotation->GetCurrentrotationByClinicianForDropdown($schoolId, $clinicianId, $selectedStudentId);
    //For Student in Dropdown
    $Students = $objStudent->GetClinicianStudentsByHospital($clinicianId, $rankId);
} else {
    $rowsCheckOffDetail = $objcheckoff->GetCheckOffDetailForClinician($currentstudentId, $currentSchoolId, $rotationId, $rankId);
    //Get Rotation in Dropdown   
    $rotation = $objrotation->GetActiveRotationBySchool($currentSchoolId, $selectedStudentId);
    //For Student in Dropdown	
    $Students = $objStudent->GetAllSchoolStudents($currentSchoolId);
}
//For Rank  in Dropdown  
$ranks = $objStudentRankMaster->GetAllStudentRankBySchool($currentSchoolId);

$RotationName = $objrotation->GetSingleRotation($currentSchoolId, $rotationId);
$rotationtitle = $RotationName ? $RotationName['title'] : '';

//For Student Name
// $objStudent = new clsStudent();
$rowsStudents = $objStudent->GetSingleStudent($currentSchoolId, $currentstudentId);
$studentfullname = $rowsStudents ? ($rowsStudents['firstName'] . ' ' . $rowsStudents['lastName']) : '';
unset($objStudent);


unset($objStudentRankMaster);
if ($rowsCheckOffDetail != '') {
    $totalCheckOffDetail = mysqli_num_rows($rowsCheckOffDetail);
}
// unset($objcheckoff);

?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title>CheckOff </title>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
    <?php include('includes/headercss.php'); ?>
    <?php include("includes/datatablecss.php") ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/css/magnific-popup.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">

    <style>
        .mt-1 {
            margin-top: 10px;
            padding-left: 55px;
        }

        table.dataTable td {
            word-break: break-word;
        }

        div.dataTables_wrapper div.dataTables_processing {
            top: -19px;
        }

        table th {
            vertical-align: middle !important;
            /* Center the header text vertically */
            text-align: center !important;
            /* Center the header text horizontally */

        }

        table th {
            vertical-align: middle !important;
            /* Center the header text vertically */
            text-align: center !important;
            /* Center the header text horizontally */

        }

        .some-class {
            float: left;
            clear: none;
        }

        .form-group {
            margin-bottom: 10px !important;
        }

        .form-control {
            height: 45px !important;
        }

        select[multiple],
        select[size] {
            height: auto !important;
        }

        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 45px !important;
        }

        .select2-container--default .select2-selection--single {
            border: none !important;
        }

        .select2-container .select2-selection--single {
            height: 45px !important;
        }

        /* Style for the collapsible content */
        .panel-collapse {
            display: none;
            /* Hidden by default */
            /* padding: 10px;
            border: 1px solid #ddd;
            background-color: #f9f9f9; */
        }

        /* Style for the collapsible button */
        .collapsible {
            /* background-color: #3498db;
            color: white;
            padding: 10px;
            border: none; */
            width: 100%;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            /* Align content horizontally */
        }

        .panel-heading {
            width: 100%;
        }

        /* Style for the arrow icons */
        .arrow-icon {
            transition: transform 0.3s;
        }

        .collapsible.expanded .arrow-icon i {
            transform: rotate(180deg);
        }
    </style>
</head>

<body>
    <?php include('includes/header.php'); ?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <?php if ($Type == 'R') { ?>
                        <li><a href="rotations.html"><?php echo ($rotationtitle); ?></a></li>
                        <li class="active">CheckOff</li>

                    <?php } elseif ($Type == 'C' && $currentstudentId > 0) { ?>
                        <li><a href="studentList.html">Student List</a></li>
                        <li class="active"><?php echo $studentfullname; ?> </li>
                        <li class="active">Checkoff</li>

                    <?php    } else { ?>
                        <li><a href="studentList.html">Student List</a></li>

                        <li class="active">Checkoff</li>
                    <?php   } ?>
                </ol>
            </div>
            <?php if ($isActiveCheckoff == 1) { ?>
                <div class="pull-right" style="margin-top:5px">
                    <a href="checkoffdetail.html?rotationId=<?php echo EncodeQueryData($rotationId); ?>&loggedClinicianType=<?php echo EncodeQueryData($loggedClinicianType); ?>&clinicianId=<?php echo EncodeQueryData($clinicianId); ?>&studentId=<?php echo EncodeQueryData($currentstudentId); ?>" class="addCommentpopup hide" data-organizationid="" oncontextmenu="return false">Add</a>
                </div>
            <?php } ?>
        </div>
    </div>
    <div class="custom-container">
        <?php
        if (isset($_GET["status"])) {
            if ($_GET["status"] == "Added") {
        ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> PEF CheckOff added successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Updated") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> PEF CheckOff updated successfully.
                </div>

            <?php
            } else if ($_GET["status"] == "Deleted") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> PEF CheckOff deleted successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Error") {
            ?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Error occurred.
                </div>
        <?php
            }
        }
        ?>
        <div id="divTopLoading">Loading...</div>

        <?php if ($currentstudentId > 0) { ?>
            <div class="formSubHeading">Student Name : <?php echo $studentfullname; ?></div>
        <?php } ?>

        <?php if ($Type == 'C') { ?>

            <div class="col-md-2 pull-right padding_right_zero">
                <select studentId="<?php echo EncodeQueryData($currentstudentId); ?>" id="cborotation" name="cborotation" class="form-control input-md redirectUrl" Type="<?php echo ($Type); ?>">
                    <option value="" selected>Select</option>
                    <?php
                    if ($rotation != "") {
                        while ($row = mysqli_fetch_assoc($rotation)) {
                            $selrotationId  = $row['rotationId'];

                            $name  = stripslashes($row['title']);

                    ?>
                            <option value="<?php echo (EncodeQueryData($selrotationId)); ?>" <?php if ($rotationId == $selrotationId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
                    <?php
                        }
                    }
                    ?>
                </select>
            </div>
            <label class=" control-label  pull-right mt1" for="cborotation" style="margin-top:8px;margin-bottom:20px">Rotation:</label>

            <div class="col-md-2 pull-right padding_right_zero" style="margin-right: 10px;">
                <select id="cboStudent" name="cboStudent" class="form-control input-md  redirectUrl" Type='<?php echo $Type; ?>'>
                    <option value="" selected>Select</option>
                    <?php
                    if ($Students != "") {
                        while ($row = mysqli_fetch_assoc($Students)) {

                            $studentsId  = $row['studentId'];
                            $firstName = $row['firstName'];
                            $lastName = $row['lastName'];
                            $fullName  = $firstName . ' ' . $lastName;

                    ?>
                            <option value="<?php echo EncodeQueryData($studentsId); ?>" <?php if ($selectedStudentId == $studentsId) { ?> selected="true" <?php } ?>><?php echo ($fullName); ?></option>
                    <?php
                        }
                    }
                    ?>
                </select>
            </div>
            <label class=" control-label  pull-right mt1" for="cbostudent" style="margin-top:8px;margin-bottom:20px">Student:</label>
            <br /> <br><br />
        <?php } ?>

        <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
            <thead>
                <tr>
                    <th>Date</th>
                    <?php if ($Type == 'R' && $rotationId > 0) { ?>
                        <th class="select-filter">First Name</th>
                        <th class="select-filter">Last Name</th>
                    <?php } else { ?>
                        <th class="select-filter">Rotation</th>
                    <?php } ?>
                    <th style="text-align:center;">Score</th>
                    <th>Topic</th>
                    <th style="text-align:center">Topic Status</th>
                    <th class="text-center">Student<br>Signoff Date</th>
                    <?php if ($isActiveCheckoff == 1) { ?>
                        <th class="text-center">Preceptor Info</th>
                    <?php } ?>
                    <th style="text-align:center">Comments</th>
                    <th style="text-align:center">Action</th>


                </tr>
            </thead>
            <tbody>
                <?php
                if ($totalCheckOffDetail > 0) {
                    while ($row = mysqli_fetch_array($rowsCheckOffDetail)) {
                        $checkoffDateTime = stripslashes($row['checkoffDateTime']);
                        $courselocationId = $row['locationId'];
                        $rotationId = $row['rotationId'];
                        $parentRotationId = stripslashes($row['parentRotationId']);
                        $rotationLocationId = stripslashes($row['rotationLocationId']);
                        $locationId = 0;

                        if ($rotationLocationId != $courselocationId && $parentRotationId > 0) {
                            if (!$rotationLocationId)
                                $locationId = $objrotation->GetLocationByRotation($rotationId);
                            else
                                $locationId  = $rotationLocationId;
                        } else {
                            $locationId  = $courselocationId;
                        }

                        //Get Time Zone By Rotation 
                        $objLocation = new clsLocations();
                        $TimeZone = $objLocation->GetTimeZoneByLocation($locationId);
                        unset($objLocation);
                        if ($TimeZone == '')
                            $TimeZone = $_SESSION["loggedClinicianSchoolTimeZone"];

                        if ($checkoffDateTime != '' && $checkoffDateTime != '0000-00-00 00:00:00') {
                            // $checkoffDateTime = converFromServerTimeZone($checkoffDateTime, $TimeZone);
                            $checkoffDateTime = date("m/d/Y", strtotime($checkoffDateTime));
                        } else {
                            $checkoffDateTime = '-';
                        }

                        $studentSignoffDate = stripslashes($row['student_evaluationDate']);

                        if ($studentSignoffDate != '' && $studentSignoffDate != '0000-00-00 00:00:00') {
                            $studentSignoffDate = converFromServerTimeZone($studentSignoffDate, $TimeZone);
                            $studentSignoffDate = date("m/d/Y", strtotime($studentSignoffDate));
                        } else {
                            $studentSignoffDate = "-";
                        }

                        $evaluationDate = stripslashes($row['evaluationDate']);

                        if ($evaluationDate != '' && $evaluationDate != '0000-00-00 00:00:00') {
                            $evaluationDate = converFromServerTimeZone($evaluationDate, $TimeZone);
                            $evaluationDate = date("m/d/Y", strtotime($evaluationDate));
                        } else {
                            $evaluationDate = "-";
                        }

                        $studentId = stripslashes($row['studentId']);
                        $checkoffId = stripslashes($row['checkoffId']);
                        $firstName = stripslashes($row['firstName']);
                        $lastName = stripslashes($row['lastName']);
                        $fullname = ($firstName) . '  ' . ($lastName);
                        $schoolTopicId = stripslashes($row['schoolTopicId']);
                        $rotationname  = stripslashes($row['rotationname']);
                        $coursename  = stripslashes($row['coursename']);
                        $schooltitle  = stripslashes($row['schooltitle']);
                        $studentlastname  = stripslashes($row['studentlastname']);
                        $studentfirstname  = stripslashes($row['studentfirstname']);
                        $rankname  = stripslashes($row['rankname']);
                        $Score  = stripslashes($row['calculatedScore']);
                        $studentComment  = stripslashes($row['studentComment']);
                        //Get student note
                        $clinicianNote = $objcheckoff->GetClinicianNote($checkoffId, $currentSchoolId);

                        //For Checkoff Score
                        // $Score=$objcheckoff->GetCHeckoffStudentScore($row["checkoffId"]);
                        $Score = number_format((float)$Score, 2, '.', '');
                        // score percentage
                        $scorePercentage  = stripslashes($row['calculatedScorePercentage']);
                        $checkoffTitleId  = stripslashes($row['checkoffTitleId']);
                        $checkoffTitleId = explode(".", $checkoffTitleId);
                        if ($checkoffTitleId[0] == 'MUL') {
                            if ($scorePercentage >= 90) {
                                $Score = '<p style="color: green;">' . $Score . '</p>';
                            } else {
                                $Score = '<p style="color: red;">' . $Score . '</p>';
                            }
                        }

                        //Get Preceptor Details
                        $objExternalPreceptors = new clsExternalPreceptors();
                        $is1stCompletionStatus  = stripslashes($row['is1stCompletionStatus']);
                        $completion1stPreceptorId  = stripslashes($row['completion1stPreceptorId']);
                        $preceptorFullName = '';
                        $preceptorMobileNum = '';
                        $isCompletedStatus = '';
                        if ($completion1stPreceptorId > 0 && $isActiveCheckoff == 1) {
                            $externalPreceptorDetail = $objExternalPreceptors->GetExternalPreceptorDetail($completion1stPreceptorId);
                            $preceptorFirstName = isset($externalPreceptorDetail['firstName']) ? $externalPreceptorDetail['firstName'] : '';
                            $preceptorLastName = isset($externalPreceptorDetail['lastName']) ? $externalPreceptorDetail['lastName'] : '';
                            $preceptorFullName = $preceptorFirstName . ' ' . $preceptorLastName;
                            $preceptorMobileNum = isset($externalPreceptorDetail['mobile_num']) ? $externalPreceptorDetail['mobile_num'] : '';

                            $isCompletedStatus = $is1stCompletionStatus ? 'Completed' : 'Pending';
                            $preceptorFullName = $is1stCompletionStatus ? $preceptorFullName : '';
                        }

                        $schoolproceduteCountName = $schooltitle;

                        $procedureCountsCode  = stripslashes($row['checkoffTitleId']);
                        $procedureCategoryId  = stripslashes($row['procedureCategoryId']);
                        $objDB = new clsDB();
                        $proceduteCountTopicId = $objDB->GetSingleColumnValueFromTable('procedurecountmaster', 'proceduteCountId', 'procedureCategoryId', $procedureCategoryId, 'procedureCountsCode', $procedureCountsCode);
                        unset($objDB);


                ?>
                        <tr>
                            <td><?php echo ($checkoffDateTime); ?></td>
                            <?php if ($Type == 'R') { ?>
                                <td><?php echo ($studentfirstname); ?> </td>
                                <td> <?php echo ($studentlastname); ?></td>
                            <?php } else { ?>
                                <td><?php echo ($rotationname); ?></td>
                            <?php } ?>
                            <td style="text-align:center;"><?php
                                                            if ($Score > 0) {
                                                                if ($Score < 75) {
                                                                    echo '<p style="color: red;">' . $Score . '</p>';
                                                                } else
                                                                    echo $Score;
                                                            } else {
                                                                if ($checkoffTitleId[0] == 'MUL')
                                                                    echo $Score;
                                                                else
                                                                    echo "-";
                                                            }
                                                            ?>
                            </td>
                            <td title="<?php echo ($schooltitle); ?>">
                                <a href="javascript:void(0);" class="getProcedureSteps" rotationId="<?php echo EncodeQueryData($rotationId) ?>" proceduteCountTopicId="<?php echo EncodeQueryData($proceduteCountTopicId); ?>" procedureCountsCode="<?php echo $procedureCountsCode; ?>"><?php echo $schoolproceduteCountName; ?></a>
                            </td>
                            <td>
                                <a href='checkofftopicstatus.html?studentId=<?php echo EncodeQueryData(($studentId)); ?>&rotationId=<?php echo EncodeQueryData($rotationId); ?>&checkoffId=<?php echo EncodeQueryData($checkoffId); ?>&Type=<?php echo $Type ?>'>View Status</a>
                            </td>
                            <td style="text-align:center;"><?php echo $studentSignoffDate; ?></td>
                            <?php if ($isActiveCheckoff == 1) { ?>
                                <td class="<?php if ($preceptorMobileNum == 0) {
                                                echo 'text-center';
                                            }  ?>">
                                    <?php if ($preceptorFullName || $preceptorMobileNum || $isCompletedStatus) { ?>
                                        Name: <?php echo $preceptorFullName; ?> <br>
                                        Phone: <?php echo $preceptorMobileNum; ?> <br>
                                        Status: <?php echo $isCompletedStatus; ?>
                                    <?php } else { ?>
                                        <span>-</span>
                                    <?php } ?>
                                </td>
                            <?php } ?>
                            <td>
                                <?php
                                if ($studentComment == '')
                                    echo "Student: No";
                                else
                                    echo "Student: Yes";
                                ?> <br />
                                <?php
                                // echo $clinicianNote;
                                if ($clinicianNote == '') {
                                    echo "Clinician: No";
                                } else {
                                    echo "Clinician: Yes";
                                }
                                ?>

                            </td>
                            <td>
                                <?php if ($studentSignoffDate != '-') {
                                    if ($studentId != 0) { ?>
                                        <a href='addcheckoff.html?schoolTopicId=<?php echo EncodeQueryData($schoolTopicId); ?>&checkoffId=<?php echo EncodeQueryData($checkoffId); ?>&studentId=<?php echo EncodeQueryData($studentId); ?>&view=V'>View</a>
                                    <?php } else { ?>
                                        <a href='addcheckoff.html?schoolTopicId=<?php echo EncodeQueryData($schoolTopicId); ?>&checkoffId=<?php echo EncodeQueryData($checkoffId); ?>&rotationId=<?php echo EncodeQueryData($rotationId); ?>&view=V'>View</a>
                                    <?php }
                                } else {
                                    if ($studentId != 0) { ?>
                                        <a href='addcheckoff.html?schoolTopicId=<?php echo EncodeQueryData($schoolTopicId); ?>&checkoffId=<?php echo EncodeQueryData($checkoffId); ?>&studentId=<?php echo EncodeQueryData($studentId); ?>'>Edit</a>
                                    <?php } else { ?>
                                        <a href='addcheckoff.html?schoolTopicId=<?php echo EncodeQueryData($schoolTopicId); ?>&checkoffId=<?php echo EncodeQueryData($checkoffId); ?>&rotationId=<?php echo EncodeQueryData($rotationId); ?>'>Edit</a>
                                <?php    }
                                }    ?>

                                | <a href="procedurecounts.html?rotationId=<?php echo EncodeQueryData(($rotationId)); ?>&checkoffId=<?php echo EncodeQueryData($checkoffId); ?>&studentId=<?php echo EncodeQueryData($studentId); ?>">Procedure Counts</a>

                            </td>
                        </tr>
                <?php
                    }
                }
                unset($objcheckoff);
                unset($objrotation);
                ?>
            </tbody>
        </table>
    </div>

    <?php include('includes/footer.php'); ?>
    <?php include("includes/datatablejs.php") ?>
    <?php include('../includes/procedureStepsModal.php'); ?>

    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/js/jquery.magnific-popup.min.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script type="text/javascript">
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";

        $('.addCommentpopup').magnificPopup({
            'type': 'ajax',
            'closeOnBgClick': false
        });

        $(window).load(function() {
            $("#divTopLoading").addClass('hide');
            $("#cborotation, #cboStudent").select2();

            $(".redirectUrl").change(function() {

                var selectedStudentId = $('#cboStudent').val();
                var rotationId = $('#cborotation').val();
                var Type = "<?php echo $Type ?>";;
                Url = "checkoff.html?Type=" + Type;
                if (selectedStudentId != '') {
                    Url = Url + "&studentId=" + selectedStudentId;
                }

                if (rotationId != '') {
                    Url = Url + "&rotationId=" + rotationId;
                }
                window.location.href = Url;

            });

            $('#evaluationDate').datetimepicker({
                format: 'MM/DD/YYYY',
                maxDate: moment(),
                dropdownParent: $("#myModal")

            });

        });

        $(document).ready(function() {
            $('.hide').removeClass('hide').addClass('show');
        });


        var current_datatable = $("#datatable-responsive").DataTable({
            "pageLength": 50,
            "ordering": true,
            "order": [
                [0, "desc"]
            ],
            "scrollX": true,
            responsive: false,

            <?php if ($currentstudentId != 0) { ?> "aoColumns": [{
                        "sWidth": "10%",
                    },
                    {
                        "sWidth": "10%",
                        "sClass": "alignCenter",
                    },
                    {
                        "sWidth": "10%"
                    },
                    {
                        "sWidth": "10%",
                        "bSortable": false
                    },
                    {
                        "sWidth": "10%"
                    },
                    {
                        "sWidth": "10%"
                    },
                    {
                        "sWidth": "3%"
                    },
                    {
                        "sWidth": "20%"
                    },
                    {
                        "sWidth": "2%",
                        "sClass": "alignCenter",
                        "bSortable": false
                    }
                ]
            <?php } else { ?>

                "aoColumns": [{
                        "sWidth": "10%",
                    },
                    {
                        "sWidth": "10%",
                        "sClass": "alignCenter",
                    },
                    {
                        "sWidth": "10%",
                        "sClass": "alignCenter",
                    },
                    {
                        "sWidth": "10%",
                    },
                    {
                        "sWidth": "5%"
                    },
                    {
                        "sWidth": "3%",
                        "sClass": "alignCenter"
                    },
                    {
                        "sWidth": "3%",
                        "bSortable": false
                    },
                    {
                        "sWidth": "3%",
                        "sClass": "alignCenter",
                    },
                    {
                        "sWidth": "20%"
                    },
                    {
                        "sWidth": "2%",
                        "sClass": "alignCenter",
                        "bSortable": false
                    }
                ]

            <?php } ?>

        });
    </script>
</body>

</html>