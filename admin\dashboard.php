<?php

// phpinfo();
include('includes/validateUserLogin.php');

include("../includes/config.php");
include("../includes/commonfun.php");
include("../class/clsDB.php");
include('../setRequest.php');
include_once("../class/clsSchool.php");
include("../class/clsStudent.php");
include("../class/clsRotation.php");
include("../class/clsLocations.php");
include("../class/clsHospitalSite.php");
include("../class/clsClinician.php");
include("../class/clsImmunization.php");
include("../class/clsAttendance.php");
include("../class/clscheckoff.php");
include("../class/clsInteraction.php");
include("../class/clsClinicianRoleMaster.php");
include("../class/clsStudentRankMaster.php");
include('../class/clsCallOff.php');

$loggedUserId = $_SESSION["loggedUserId"];
$isActiveCheckoff = $_SESSION["isActiveCheckoff"];
$TimeZone = $_SESSION["loggedUserSchoolTimeZone"];
$minDate = '';
//This is only for Primary School
// if($currentSchoolId == 1)
if ($currentSchoolId == 1) {
	//Get School Counts
	$totalSchool = 0;
	$totalActiveSchoolCount = 0;
	$objSchool = new clsSchool();


	//Clinicians
	$Activeclinician = '';
	$TotalActiveclinicianCounts = 0;
	$objClinician = new clsClinician();
	$Activeclinician = $objClinician->GetActiveClinician();

	unset($objClinician);

	if ($Activeclinician != '') {
		$TotalActiveclinicianCounts = mysqli_num_rows($Activeclinician);
	}

	$Activeclinician = '';

	//Student
	$Activestudent = '';
	$TotalActiveStudentCounts = 0;
	$objStudent = new clsStudent();
	$Activestudent = $objStudent->GetActiveStudent();


	if ($Activestudent != '') {
		$TotalActiveStudentCounts = mysqli_num_rows($Activestudent);
	}
	$Activestudent = '';
	$ActivestudentRegisterYear = '';
	$TotalActivestudentRegisterYearCounts = 0;
	$ActivestudentRegisterYear = $objStudent->GetActiveStudentRegisterYear();

	if ($ActivestudentRegisterYear != '') {
		$TotalActivestudentRegisterYearCounts = mysqli_num_rows($ActivestudentRegisterYear);
	}
	$chartYears = array();
	$chartYearsActiveStudents = array();

	if ($TotalActivestudentRegisterYearCounts) {
		while ($row = mysqli_fetch_array($ActivestudentRegisterYear)) {
			$registerDate = stripslashes($row['createdDate']);
			$registerDate = date('Y', strtotime($registerDate));
			$chartYears[] = $registerDate;
			$chartYearsActiveStudents[] = $objStudent->GetActivestudentYearCount($registerDate);
		}
		$chartYearsInString =  "'" . implode("', '", $chartYears) . "'";
		$chartYearsActiveStudentsInString =  "'" . implode("', '", $chartYearsActiveStudents) . "'";
	}
	unset($objStudent);
	$totalUsers = $objSchool->GetUserCounts();
	$totalBlockedUsers = $objSchool->GetBlockedUserCounts();
	$blockedUsersDetails = $objSchool->GetActiveBlockedUserdetails();
	$AdministratorCount = $CoordinatorCount = $DCECount = $PDCount = $PreceptorCount = $StudentCount = $ClinicianCount = $OtherCount = 0;

	$totalBlockedUsersCount = ($blockedUsersDetails != '') ? mysqli_num_rows($blockedUsersDetails) : 0;
	if ($totalBlockedUsersCount > 0) {
		while ($row = mysqli_fetch_array($blockedUsersDetails)) {
			$roleTitle = trim($row['roleTitle']);
			$userCount = $row['users'];

			if ($roleTitle == 'Administrator')
				$AdministratorCount = $userCount;
			else if ($roleTitle == 'Coordinator')
				$CoordinatorCount = $userCount;
			else if ($roleTitle == 'D.C.E.')
				$DCECount = $userCount;
			else if ($roleTitle == 'P.D.')
				$PDCount = $userCount;
			else if ($roleTitle == 'Preceptor')
				$PreceptorCount = $userCount;
			else  if ($roleTitle == 'Clinician')
				$ClinicianCount = $userCount;
			else  if ($roleTitle == 'Student')
				$StudentCount = $userCount;
			else
				$OtherCount = $OtherCount + $userCount;
		}
	}


	$activeAdminDetails = $objSchool->GetActiveAdminDetails();
	$AdministratorUserCount = $CoordinatorUserCount = $DCEUserCount = $PDUserCount = $PreceptorUserCount = $StudentUserCount = $ClinicianUserCount = $OtherUserCount = 0;
	$totalActiveAdminDetailsCount = ($activeAdminDetails != '') ? mysqli_num_rows($activeAdminDetails) : 0;
	if ($totalActiveAdminDetailsCount > 0) {
		while ($rowAdmin = mysqli_fetch_array($activeAdminDetails)) {
			$roleTitle = trim($rowAdmin['roleTitle']);
			$activeUserCount = $rowAdmin['users'];

			if ($roleTitle == 'Administrator')
				$AdministratorUserCount = $activeUserCount;
			else if ($roleTitle == 'Coordinator')
				$CoordinatorUserCount = $activeUserCount;
			else if ($roleTitle == 'D.C.E.')
				$DCEUserCount = $activeUserCount;
			else if ($roleTitle == 'P.D.')
				$PDUserCount = $activeUserCount;
			else if ($roleTitle == 'Preceptor')
				$PreceptorUserCount = $activeUserCount;
			else
				$OtherUserCount = $OtherUserCount + $activeUserCount;
		}
	}

	// Get Clinician Count
	$activeClinicianCounts = $objSchool->GetActiveClinicianCount();
	$totalClinicianCount = $activeClinicianCount = 0;
	if ($activeClinicianCounts != '') {
		$totalClinicianCount = $activeClinicianCounts['totalClinicianCount'];
		$activeClinicianCount = $activeClinicianCounts['activeClinicianCount'];
	}

	// Get Admin Count
	$activeAdminCounts = $objSchool->GetActiveAdminCount();
	$totalAdminCount = $activeAdminCount = 0;
	if ($activeAdminCounts != '') {
		$totalAdminCount = $activeAdminCounts['totalAdminCount'];
		$activeAdminCount = $activeAdminCounts['activeAdminCount'];
	}

	// Get Student Count
	$activeStudentCounts = $objSchool->GetActiveStudentCount();
	$totalStudentCount = $activeStudentCount = 0;
	if ($activeStudentCounts != '') {
		$totalStudentCount = $activeStudentCounts['totalStudentCount'];
		$activeStudentCount = $activeStudentCounts['activeStudentCount'];
	}

	// Get Student Count
	$schoolCounts = $objSchool->GetSchoolCounts();
	$totalSchool = $totalActiveSchoolCount = 0;
	if ($schoolCounts != '') {
		$totalSchool = $schoolCounts['TotalCount'];
		$totalActiveSchoolCount = $schoolCounts['ActiveCount'];
	}


	// Get Graduate Student Count
	$activeGraduateStudentCounts = $objSchool->GetActiveGraduateStudentCount();
	$totalGraduateStudentCount = $activeGraduateStudentCount = 0;
	if ($activeGraduateStudentCounts != '') {
		$totalGraduateStudentCount = $activeGraduateStudentCounts['totalGraduateStudentCount'];
		$activeGraduateStudentCount = $activeGraduateStudentCounts['activeGraduateStudentCount'];
	}


	// Get school count by checkoff type
	$schoolsByCheckoffType = $objSchool->GetSchoolsByCheckoffType();
	$a = $aa = $aaa = $all = 0;
	$totalschoolsByCheckoffTypeCount = ($schoolsByCheckoffType != '') ? mysqli_num_rows($schoolsByCheckoffType) : 0;
	if ($totalschoolsByCheckoffTypeCount > 0) {
		while ($rowCheckoffSchool = mysqli_fetch_array($schoolsByCheckoffType)) {
			$schoolCount = trim($rowCheckoffSchool['schoolCount']);
			$checkoffType = $rowCheckoffSchool['checkoffType'];

			if ($checkoffType == 0) // all types of schools
				$all = $schoolCount;
			else if ($checkoffType == 1) // a types of schools
				$a = $schoolCount;
			else if ($checkoffType == 2) // aa types of schools
				$aa = $schoolCount;
			else if ($checkoffType == 3) // aaa types of schools
				$aaa = $schoolCount;
		}
	}

	// Get schedule school Count
	$scheduleSchoolCounts = $objSchool->GetSchoolsCountBySchedules();
	$rotationSchoolCount = $scheduleSchoolCount = $hopitalsiteSchoolCount = 0;
	if ($scheduleSchoolCounts != '') {
		$scheduleSchoolCount = $scheduleSchoolCounts['scheduleSchoolCount'];
		$hopitalsiteSchoolCount = $scheduleSchoolCounts['hopitalsiteSchoolCount'];
		$rotationSchoolCount = $objSchool->GetSchoolsCountByRotation();
	}

	unset($objSchool);
} else {

	//Locations
	$totalLocations = 0;
	$objLocations = new clsLocations();
	$totalLocations = $objLocations->GetLocationCount($currentSchoolId);
	unset($objLocations);

	//HospitalSite
	$totalHospitalSites = 0;
	$objHospitalSites = new clsHospitalSite();
	$totalHospitalSites = $objHospitalSites->GetHospitalSiteCount($currentSchoolId);
	unset($objHospitalSites);

	//Clinicians
	$totalClinicians = 0;
	$totalActiveCliniciansCount = 0;
	$objClinician = new clsClinician();
	$clinicianCounts = $objClinician->GetClinicianCounts($currentSchoolId);
	unset($objClinician);
	if ($clinicianCounts != '') {
		$totalClinicians = $clinicianCounts['TotalCount'];
		$totalActiveCliniciansCount = $clinicianCounts['ActiveCount'];
	}

	//Students
	$totalstudents = 0;
	$totalActivestudentsCount = 0;
	$objStudent = new clsStudent();
	$StudentCounts = $objStudent->GetStudentsCounts($currentSchoolId);
	unset($objStudent);
	if ($StudentCounts != '') {
		$totalstudents = $StudentCounts['TotalCount'];
		$totalActivestudentsCount = $StudentCounts['ActiveCount'];
	}
	//ROTATION
	$totalrotations = 0;
	$totalActiverotationCount = 0;
	$upcomingrotation = 0;
	$objRotation = new clsRotation();
	$rotationCounts = $objRotation->GetRotationCounts($currentSchoolId);
	if ($rotationCounts != '') {
		$totalrotations = $rotationCounts['TotalCount'];
		$totalActiverotationCount = $rotationCounts['ActiveCount'];
		$upcomingrotation = $rotationCounts['UpcomingCount'];
	}
	//Upcoming Rotation
	$RowUpcomingRotationDetails = $objRotation->GetUpcomingRotation($currentSchoolId);
	$TotalUpcomingRotationDetails = 0;
	if ($RowUpcomingRotationDetails != '') {
		$TotalUpcomingRotationDetails = mysqli_num_rows($RowUpcomingRotationDetails);
	}

	//Immunization	
	$RowImmunizationDetails = '';
	$objImmunization = new clsImmunization();
	$RowImmunizationDetails = $objImmunization->GetUpcomingImmunizations($currentSchoolId);
	$TotalUpcomingImmunizationDetails = 0;
	if ($RowImmunizationDetails != '') {
		$TotalUpcomingImmunizationDetails = mysqli_num_rows($RowImmunizationDetails);
	}
	$TotalClinicianUpcomingImmunizationDetails = 0;
	$RowClinicianImmunizationDetails = $objImmunization->GetClinicianUpcomingImmunizations($currentSchoolId);
	if ($RowClinicianImmunizationDetails != '') {
		$TotalClinicianUpcomingImmunizationDetails = mysqli_num_rows($RowClinicianImmunizationDetails);
	}
	unset($objImmunization);
	//LATEST CLOCKOUT STUDENT
	$objAttendance = new clsAttendance();


	$RowAttendance = $objAttendance->GetAllAttendanceBySchool($currentSchoolId);

	$currentDateTime = date('Y-m-d H:i:s');
	$currentDateTime = converFromServerTimeZone($currentDateTime, $TimeZone);

	// $assignedRotations=$objRotation->GetAllAssignedRotations($currentSchoolId,1);
	// $totalAssignedRotations = ($assignedRotations !='') ? mysqli_num_rows($assignedRotations) : 0;
	$absenceArray = [];
	// if($totalAssignedRotations>0)
	// {
	// 	while($row = mysqli_fetch_array($assignedRotations))
	// 	{
	// 		$rotationId = $row['rotationId'];
	// 		$students = $row['students'];
	// 		$rotationTitle = stripslashes($row['title']);
	// 		$dayOfWeek = stripslashes($row['dayOfWeek']);
	// 		$startDate = date('Y-m-d',strtotime($row['startDate']));
	// 		$endDate = date('Y-m-d',strtotime($row['endDate']));
	// 		// $repeatDays = $objRotation->GetRotationRepeatDaysBYRotation($rotationId);
	// 		$repeatDays = explode(",",$dayOfWeek);

	// 		$repeatDayUpdatedArray = [];
	// 		foreach($repeatDays as $repeatDay)
	// 		{
	// 			if($repeatDay == 0)
	// 				$repeatDayUpdatedArray[] = 6;
	// 			elseif($repeatDay == 1)
	// 				$repeatDayUpdatedArray[] = 0;
	// 			elseif($repeatDay == 2)
	// 				$repeatDayUpdatedArray[] = 1;
	// 			elseif($repeatDay == 3)
	// 				$repeatDayUpdatedArray[] = 2;
	// 			elseif($repeatDay == 4)
	// 				$repeatDayUpdatedArray[] = 3;
	// 			elseif($repeatDay == 5)
	// 				$repeatDayUpdatedArray[] = 4;
	// 			elseif($repeatDay == 6)
	// 				$repeatDayUpdatedArray[] = 5;
	// 		}

	// 		$repeatDayUpdated = implode(",",$repeatDayUpdatedArray);

	// 		$currentDate = date('Y-m-d',strtotime($currentDateTime));
	// 		$startTime = date('H:i:s',strtotime($row['startDate']));

	// 		$rotationDay = $objRotation->GetAllDaysBetweenRotationStartAndEndDate($startDate,$endDate,$repeatDayUpdated,$currentDate);
	// 		$totalRotationDay = ($rotationDay !='') ? mysqli_num_rows($rotationDay) : 0;

	// 		if($totalRotationDay > 0)
	// 		{
	// 			while($rotationRow = mysqli_fetch_array($rotationDay))
	// 			{
	// 				$rotationDate = $rotationRow['Date'];
	// 				if($rotationDate == $currentDate)
	// 				{
	// 					$currentTime = date('H:i:s',strtotime($currentDateTime));
	// 					$currentTime = strtotime($currentTime);
	// 					$startTime = strtotime($startTime);
	// 					if($currentTime < $startTime)
	// 						continue;

	// 				}

	// 				$studentsArray = explode(",",$students);
	// 				foreach($studentsArray as $student)
	// 				{
	// 					$studentArray = explode("-",$student);
	// 					$studentId = isset($studentArray[0]) ? $studentArray[0] : '';
	// 					$firstName = isset($studentArray[1]) ? stripslashes($studentArray[1]) : '';
	// 					$lastName = isset($studentArray[2]) ? stripslashes($studentArray[2]) : '';
	// 					$ranktitle = isset($studentArray[3]) ? stripslashes($studentArray[3]) : '';

	// 					$isStudenAbsenceExist = $objAttendance->GetAttendanceDetail($studentId,$rotationId,$rotationDate);
	// 					if(!$isStudenAbsenceExist)
	// 					{
	// 						$absence = [];
	// 						$absence['rotationDate'] = $rotationDate;
	// 						$absence['firstName'] = $firstName;
	// 						$absence['lastName'] = $lastName;
	// 						$absence['ranktitle'] = $ranktitle;
	// 						$absence['rotationTitle'] = $rotationTitle;
	// 						$absenceArray [] = $absence; 
	// 					}
	// 				}
	// 			}
	// 		}
	// 	}
	// }


	// // Comparison function
	// function date_compare($element1, $element2) {
	// 	$datetime1 = strtotime($element1['rotationDate']);
	// 	$datetime2 = strtotime($element2['rotationDate']);
	// 	return $datetime2 - $datetime1;
	// }

	// // Sort the array 
	// usort($absenceArray, 'date_compare');


	$TotalRowAbsence = 0;
	$RowAbsence = $objAttendance->GetAllAbsenceBySchool($currentSchoolId);
	if ($RowAbsence != '') {
		$TotalRowAbsence = mysqli_num_rows($RowAbsence);
	}
	//Late Clock In
	$lateClockIn = $objAttendance->LateClockInDetails($currentSchoolId);
	$TotalLateClockIn = 0;
	if ($lateClockIn != '') {
		$TotalLateClockIn = mysqli_num_rows($lateClockIn);
	}

	//Early Clock Out
	$earlyClockOut = $objAttendance->EarlyClockOutDetails($currentSchoolId);
	$TotalEarlryClockOut = 0;
	if ($earlyClockOut != '') {
		$TotalEarlryClockOut = mysqli_num_rows($earlyClockOut);
	}


	$objDB = new clsDB();
	$callOff = $objDB->GetSingleColumnValueFromTable('schoolsettings', 'status', 'schoolId', $currentSchoolId, 'type', 'callOff');
	unset($objDB);
	//Get All Call Off
	$objCallOff = new clsCallOff();
	$generateLimitString = GenerateOrderLimitString(1, 10);
	$rowsCallOff = $objCallOff->GetAllCallOffs($currentSchoolId, 0, 0, 0, 0, $generateLimitString);
	$totalCallOff = ($rowsCallOff != '') ? mysqli_num_rows($rowsCallOff) : 0;
	unset($objCallOff);

	$TotalRowClockout = 0;
	$TotalRowAttendance = 0;

	if ($RowAttendance != '') {
		$TotalRowAttendance = mysqli_num_rows($RowAttendance);
	}

	//Chart Data 
	//----------------------------
	$returnClockInArray = $objAttendance->GetClockInBySchool($currentSchoolId);
	$returnClockOutArray = $objAttendance->GetClockOutBySchool($currentSchoolId);
	$checkIndataPoints  = array();
	$checkOutdataPoints = array();
	$chartDates = array();

	//Check In and Checkout Data Points
	for ($i = 0; $i < count($returnClockInArray); $i++) {
		$checkIndataPoints[] = $returnClockInArray[$i]['Count'];
		$checkOutdataPoints[] = $returnClockOutArray[$i]['Count'];
		//if($returnClockInArray[$i]['Count'] >0){
		$chartDates[] = $returnClockInArray[$i]['Date'];
		//}
	}

	// 	OPS Comment date - 14Sept2021	
	$chartDates = array_reverse($chartDates);
	// $minDate = date("Y-m-d", strtotime($chartDates[0])); // changed $chartDates[6] to 0 OPS Comment date - 14Sept2021
	$minDate = date("Y-m-d", strtotime("-1 week", strtotime($currentDateTime))); // Subtract 1 week
	$RowClockout = $objAttendance->GetClockoutDetails($currentSchoolId, $clinicianId = 0, $minDate);
	if ($RowClockout != '') {
		$TotalRowClockout = mysqli_num_rows($RowClockout);
	}

	// 	OPS Comment date - 14Sept2021	
	$checkOutdataPoints = array_reverse($checkOutdataPoints);
	// 	OPS Comment date - 14Sept2021	
	$checkIndataPoints = array_reverse($checkIndataPoints);

	//Make in String
	$chartDatesInString =  "'" . implode("', '", $chartDates) . "'";
	$checkIndataPointsInString = "'" . implode("', '", $checkIndataPoints) . "'";
	$checkOutdataPointsInString = "'" . implode("', '", $checkOutdataPoints) . "'";
	//----------------------------


	//Checkoff
	$objcheckoff = new clscheckoff();
	$Getcheckoff = $objcheckoff->GetCheckOffDetailsBySchool($currentSchoolId);
	$Totalcheckoffcount = 0;
	if ($Getcheckoff != '') {
		$Totalcheckoffcount = mysqli_num_rows($Getcheckoff);
	}
	//unset($objcheckoff);		
	$objInteraction = new clsInteraction();
	$GetInteraction = $objInteraction->GetInteractionDetailsBySchool($currentSchoolId);
	$Totalinteractioncount = 0;
	if ($GetInteraction != '') {
		$Totalinteractioncount = mysqli_num_rows($GetInteraction);
	}
	unset($objInteraction);

	$objHospitalsite = new clsHospitalSite();
	//Upcoming Affiliate Agreement 
	$RowUpcomingAffiliateDetail = $objHospitalsite->GetUpcomingAffiliateAgreement($currentSchoolId);
	$TotalUpcomingAffiliateDetails = 0;
	if ($RowUpcomingAffiliateDetail != '') {
		$TotalUpcomingAffiliateDetails = mysqli_num_rows($RowUpcomingAffiliateDetail);
	}
}

?>
<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
	<title>Dashboard</title>
	<?php include('includes/headercss.php'); ?>
	<?php include("includes/datatablecss.php") ?>
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/css/dashboard.css">
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/css/dashboardgraph.css">
	<!-- <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer" /> -->

	<script type="module" src="https://unpkg.com/ionicons@5.5.2/dist/ionicons/ionicons.esm.js"></script>
	<script nomodule src="https://unpkg.com/ionicons@5.5.2/dist/ionicons/ionicons.js"></script>
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
	<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">

	<style>
		.school-admin-graph {
			width: 570px !important;
			height: 285px !important;
		}

		.toast-check-icon {
			display: none !important;
		}

		.toast-close-icon {
			display: none !important;
		}
	</style>
</head>

<body>
	<?php include('includes/header.php'); ?>
	<div class="row margin_zero breadcrumb-bg">
		<div class="container">
			<ol class="breadcrumb">
				<li><span class="active">Dashboard</span></li>
			</ol>
		</div>
	</div>

	<div class="container margin_bottom_fourty">

		<?php
		if (isset($_GET["status"])) {
			if ($_GET["status"] == "error") { ?>
				<div class="alert alert-danger alert-dismissible fade in" role="alert">
					<button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
					</button> Error occurred.
				</div>
		<?php }
		} ?>



		<?php
		if ($currentSchoolId == 1) { ?>
			<div class="main-body">
				<!-- <h1 style="font-size: 28px; margin: 20px;">Dashboard</h1> -->
				<div style="display: flex; flex-direction: row-reverse; gap: 20px;">
					<div class="graph-card filters">
						<p class="section-title" style="margin-bottom: 0px;">Filter</p>
						<div>
							<label for="super-admin-startDate">Start Date:</label>
							<input type="text" id="super-admin-startDate">
						</div>
						<div>
							<label for="super-admin-endDate">End Date:</label>
							<input type="text" id="super-admin-endDate">
						</div>
						<div>
							<label for="super-admin-quickFilter">Quick Filter:</label>
							<select id="super-admin-quickFilter">
								<option value="">Select</option>
								<option value="last7" selected>Last Week</option>
								<option value="last15">Last 15 Days</option>
								<option value="last30">Last Month</option>
								<option value="last90">Last 3 Months</option>
							</select>
						</div>
						<div class="filter-buttons">
							<button onclick="applyFilters()">Apply</button>
							<button id="super-admin-resetFilterBtn" onclick="resetFilters()">Reset</button>
						</div>
					</div>

					<div class="graph-card">
						<canvas id="super-admin-schoolChart" width="800" height="400"></canvas>
					</div>
				</div>
				<div id="super-admin-modal">
					<div id="super-admin-modalContent">
						<span class="closeBtn" onclick="supercloseModal()">&times;</span>
						<h3>School Details</h3>
						<div id="super-admin-modalBody"></div>
					</div>
				</div>


				<div class="parent-col">
					<div style="width: 55%;">
						<div style="width: 100%;display: flex;">
							<div class="first-col desktop-ml-0 margin-right-0">
								<div class="logged-in-user-number animated fadeInLeft" style="margin-bottom: 20px;">
									<div class="d-flex justify-content-end">
										<div class="logged-in-icon">
											<!-- <i class="fa-solid fa-users"></i> -->
											<!-- <ion-icon name="people-sharp"></ion-icon> -->
											<i class="fa fa-users" aria-hidden="true" style="color: #5cb85c;font-size: 20px;"></i>

										</div>
									</div>
									<div class="count-section">
										<div id="count1" style="color: #5cb85c;"><?php echo $totalUsers; ?></div>

										<span style="font-size: 17px;font-weight: 500;line-height: 1;">
											Active Users
										</span>
									</div>
								</div>

								<div class="logged-in-user-number animated-user-details fadeInLeft" style="min-height: 200px;">
									<div class="d-flex justify-content-between" style="align-items: center;">
										<p class="main-count-subtitle">Admins</p>
										<!-- <div class="logged-in-icon">
                            <i class="fa-solid fa-users"></i>
                        </div> -->
									</div>
									<div style="margin-top: 10px;" class="decoration-none">
										<a href="userDetails.html?type=activeAdmins&role=<?php echo EncodeQueryData('Administrator') ?>">
											<div class="subdetails-parent">
												<p class="subdetails-title">Administrator</p>
												<span class="user-details-number"><?php echo $AdministratorUserCount; ?></span>
											</div>
										</a>
										<hr><a href="userDetails.html?type=activeAdmins&role=<?php echo EncodeQueryData('D.C.E.') ?>">
											<div class="subdetails-parent">
												<p class="subdetails-title">DCE</p>
												<span class="user-details-number"><?php echo $DCEUserCount; ?>
												</span>
											</div>
										</a>
										<hr>
										<a href="userDetails.html?type=activeAdmins&role=<?php echo EncodeQueryData('Coordinator') ?>">
											<div class="subdetails-parent">
												<p class="subdetails-title">Coordinator</p>
												<span class="user-details-number"><?php echo $CoordinatorUserCount; ?></span>
											</div>
										</a>
										<hr>
										<a href="userDetails.html?type=activeAdmins&role=<?php echo EncodeQueryData('P.D.') ?>">
											<div class="subdetails-parent">
												<p class="subdetails-title">Program Directors</p>
												<span class="user-details-number"><?php echo $PDUserCount; ?></span>
											</div>
										</a>
										<hr>
										<a href="userDetails.html?type=activeAdmins&role=<?php echo EncodeQueryData('Preceptor') ?>">
											<div class="subdetails-parent">
												<p class="subdetails-title">Preceptor</p>
												<span class="user-details-number"><?php echo $PreceptorUserCount; ?></span>
											</div>
										</a>
										<hr>
										<a href="userDetails.html?type=activeAdmins&role=<?php echo EncodeQueryData('Others') ?>">
											<div class="subdetails-parent">
												<p class="subdetails-title">Others</p>
												<span class="user-details-number"><?php echo $OtherUserCount; ?></span>
											</div>
										</a>
									</div>
								</div>
							</div>
							<div class="middle-col margin-right-0">

								<div class="logged-in-user-number animated fadeInUp">
									<div class="d-flex justify-content-end" style="position: absolute;right: 25px;">
										<div class="logged-in-icon" style="background-color: #2d68b61c;">
											<!-- <i class="fa-solid fa-users" style="color: #2d69b6;"></i> -->
											<!-- <ion-icon name="people-sharp" style="color: #2d69b6;"></ion-icon> -->
											<i class="fa fa-users" aria-hidden="true" style="color: #2d69b6;font-size: 20px;"></i>

										</div>
									</div>
									<div class="count-section" style="font-size: 52px;font-weight: 500;line-height: 1.2;margin:0;">
										<div id="count2" class="count2"><a class="underline-none" href="userDetails.html?type=lockedUsers"><?php echo $totalBlockedUsers; ?></a></div>
										<span style="font-size: 17px;font-weight: 500;line-height: 1;">
											Accounts Locked
										</span>
									</div>

									<hr style="margin-top: 18px;margin-bottom: 10px;">

									<div class="d-flex justify-content-between" style="align-items: center;">
										<p class="main-count-subtitle">Locked Users</p>
										<!-- <div class="logged-in-icon">
                            <i class="fa-solid fa-users"></i>
                        </div> -->
									</div>
									<div style="margin-top: 10px;" class="decoration-none">
										<a href="userDetails.html?type=lockedUsers&role=<?php echo EncodeQueryData('Administrator') ?>">
											<div class="subdetails-parent">
												<p class="subdetails-title">Administrator</p>
												<span class="user-details-number"><?php echo $AdministratorCount; ?></span>
											</div>
										</a>
										<hr>
										<a href="userDetails.html?type=lockedUsers&role=<?php echo EncodeQueryData('D.C.E.') ?>">
											<div class="subdetails-parent">
												<p class="subdetails-title">DCE</p>
												<span class="user-details-number"><?php echo $DCECount; ?></span>
											</div>
										</a>
										<hr>
										<a href="userDetails.html?type=lockedUsers&role=<?php echo EncodeQueryData('Coordinator') ?>">
											<div class="subdetails-parent">
												<p class="subdetails-title">Coordinator</p>
												<span class="user-details-number"><?php echo $CoordinatorCount; ?></span>
											</div>
										</a>
										<hr>
										<a href="userDetails.html?type=lockedUsers&role=<?php echo EncodeQueryData('P.D.') ?>">
											<div class="subdetails-parent">
												<p class="subdetails-title">Program Directors</p>
												<span class="user-details-number"><?php echo $PDCount; ?></span>
											</div>
										</a>
										<hr>
										<a href="userDetails.html?type=lockedUsers&role=<?php echo EncodeQueryData('Preceptor') ?>">
											<div class="subdetails-parent">
												<p class="subdetails-title">Preceptor</p>
												<span class="user-details-number"><?php echo $PreceptorCount; ?></span>
											</div>
										</a>
										<hr><a href="userDetails.html?type=lockedUsers&role=<?php echo EncodeQueryData('Clinician') ?>">
											<div class="subdetails-parent">
												<p class="subdetails-title">Clinical Instructor</p>
												<span class="user-details-number"><?php echo $ClinicianCount; ?></span>
											</div>
										</a>
										<hr>
										<a href="userDetails.html?type=lockedUsers&role=<?php echo EncodeQueryData('Student') ?>">
											<div class="subdetails-parent">
												<p class="subdetails-title">Students</p>
												<span class="user-details-number"><?php echo $StudentCount; ?></span>
											</div>
										</a>
										<hr>
										<a href="userDetails.html?type=lockedUsers&role=<?php echo EncodeQueryData('Others') ?>">
											<div class="subdetails-parent">
												<p class="subdetails-title">Others</p>
												<span class="user-details-number"><?php echo $OtherCount; ?></span>
											</div>
										</a>
									</div>
								</div>

							</div>
						</div>
						<div class="first-col logged-in-user-number animated-checkoff fadeInLeft margin-right-0" style="width: auto !important;">
							<div class="d-flex justify-content-between" style="align-items: center;">
								<p style="font-size: 24px;margin-bottom: 10px;font-weight: 500;">Schools With Checkoff Type</p>
							</div>

							<div class="masonry-grid animated1 fadeIn">
								<div style="text-decoration: none;" class="checkoff-card decoration-none checkoff-card1">
									<a href="userDetails.html?type=checkoffType&checkoffType=<?php echo EncodeQueryData(1) ?>">
										<div class="d-flex justify-content-end" style="margin-bottom: 10px;">
											<div class="logged-in-icon-small checkoff-icon1">
												<!-- <i class="fa-solid fa-user-tie"></i> -->
												<!-- <ion-icon name="person-add-sharp"></ion-icon> -->
												<i class="fa fa-user-plus" aria-hidden="true" style="color: #519CFF;"></i>
											</div>

										</div>
										<div style="display: flex;flex-direction: column;">
											<p class="checkoff-type-name">A Type</p>
											<span class="checkoff-type-number"><?php echo $a; ?></span>
										</div>
									</a>
								</div>
								<div style="text-decoration: none;" class="checkoff-card decoration-none checkoff-card2">
									<a href="userDetails.html?type=checkoffType&checkoffType=<?php echo EncodeQueryData(2) ?>">
										<div class="d-flex justify-content-end" style="margin-bottom: 10px;">
											<div class="logged-in-icon-small checkoff-icon2">
												<!-- <i class="fa-solid fa-school-circle-check"></i> -->
												<!-- <ion-icon name="business-outline"></ion-icon> -->
												<i class="fa fa-building-o" aria-hidden="true" style="color: #9A81ED;"></i>
											</div>

										</div>
										<div style="display: flex;flex-direction: column;">
											<p class="checkoff-type-name">AA Type</p>
											<span class="checkoff-type-number"><?php echo $aa; ?></span>
										</div>
									</a>
								</div>
								<div style="text-decoration: none;" class="checkoff-card decoration-none checkoff-card3">
									<a href="userDetails.html?type=checkoffType&checkoffType=<?php echo EncodeQueryData(3) ?>">
										<div class="d-flex justify-content-end" style="margin-bottom: 10px;">
											<div class="logged-in-icon-small checkoff-icon3">
												<!-- <i class="fa-solid fa-user-doctor"></i> -->
												<!-- <ion-icon name="fitness"></ion-icon> -->
												<i class="fa fa-heartbeat" aria-hidden="true" style="color: #FFA24C;"></i>
											</div>

										</div>
										<div style="display: flex;flex-direction: column;">
											<p class="checkoff-type-name">AAA Type</p>
											<span class="checkoff-type-number"><?php echo $aaa; ?></span>
										</div>
									</a>
								</div>
								<div style="text-decoration: none;" class="checkoff-card decoration-none checkoff-card4">
									<a href="userDetails.html?type=checkoffType&checkoffType=<?php echo EncodeQueryData(0) ?>">
										<div class="d-flex justify-content-end" style="margin-bottom: 10px;">
											<div class="logged-in-icon-small checkoff-icon4">
												<!-- <i class="fa-solid fa-graduation-cap"></i> -->
												<!-- <ion-icon name="school"></ion-icon> -->
												<i class="fa fa-graduation-cap" aria-hidden="true" style="color: #FF746A;"></i>
											</div>

										</div>
										<div style="display: flex;flex-direction: column;">
											<p class="checkoff-type-name">A & AA</p>
											<span class="checkoff-type-number"><?php echo $all; ?></span>
										</div>
									</a>
								</div>

							</div>
						</div>
						<div class="first-col logged-in-user-number animated-stat fadeInLeft margin-right-0" style="width: auto !important;">
							<div class="d-flex justify-content-between" style="align-items: center;">
								<p class="section-title">Annual Student count</p>
								<!-- <div class="logged-in-icon">
										<i class="fa-solid fa-users"></i>
									</div> -->
							</div>

							<div class="col-md-12 margin_bottom_ten">
								<center>
									<!-- <canvas id="myBarChart" style="width:100%;"></canvas> -->
									<canvas id="myChart" width="300" height="300"></canvas>
								</center>
							</div>


							<!-- <div class="col-md-6 col-md-offset-2">
									<div class="row d-flex margin_zero mb-4 chart-box justify-content-end text-left ct-chart">
										<canvas id="divDashboardChat"></canvas>
									</div>
								</div> -->

						</div>



					</div>
					<div style="width: 43%;">
						<div class="last-col margin-right-0">

							<div class="logged-in-user-number animated fadeInRight" style="margin-bottom: 20px;">
								<div class="d-flex justify-content-between" style="align-items: center;">
									<p class="section-title">Active Details</p>
									<!-- <div class="logged-in-icon">
                           		 <i class="fa-solid fa-users"></i>
                        		</div> -->
								</div>

								<div class="masonry-grid animated1 fadeIn">
									<div class="card" style="background: #ECE2FD;">
										<div class="d-flex justify-content-end" style="margin-bottom: 10px;">
											<div class="logged-in-icon-small" style="background: #FFFFFF;">
												<!-- <i class="fa-solid fa-school-circle-check"></i> -->
												<!-- <ion-icon name="business-outline" style="color: #9A81ED;"></ion-icon> -->

												<i class="fa fa-building-o" aria-hidden="true" style="color: #9A81ED;"></i>

											</div>

										</div>
										<div style="display: flex;flex-direction: column;">
											<p class="details-title">Schools</p>
											<span class="details-count"><a class="active-school-count" href="schools.html?active=<?php echo EncodeQueryData(1); ?>"><?php echo $totalActiveSchoolCount; ?></a><span style="font-size: 20px;font-weight: 500;">/<a class="active-school-count" href="schools.html?active=<?php echo EncodeQueryData(2); ?>"><?php echo $totalSchool; ?></a></span></span>
										</div>
									</div>
									<div class="card card1">
										<div class="d-flex justify-content-end" style="margin-bottom: 10px;">
											<div class="logged-in-icon-small">
												<!-- <i class="fa-solid fa-user-tie" style="color: #519CFF;"></i> -->
												<!-- <ion-icon name="person-add-sharp" style="color: #519CFF;"></ion-icon> -->
												<i class="fa fa-user-plus" aria-hidden="true" style="color: #519CFF;"></i>

											</div>

										</div>
										<div style="display: flex;flex-direction: column;">
											<p class="details-title">Admin</p>
											<span class="details-count"><?php echo $activeAdminCount; ?><span style="font-size: 20px;font-weight: 500;">/<?php echo $totalAdminCount; ?></span></span>
										</div>
									</div>

									<div class="card" style="background: #FFF3DA;">
										<div class="d-flex justify-content-end" style="margin-bottom: 10px;">
											<div class="logged-in-icon-small" style="background: #FFFFFF;">
												<!-- <i class="fa-solid fa-user-doctor" style="color: #FFA24C;"></i> -->
												<!-- <ion-icon name="fitness" style="color: #FFA24C;"></ion-icon> -->
												<i class="fa fa-heartbeat" aria-hidden="true" style="color: #FFA24C;"></i>


											</div>

										</div>
										<div style="display: flex;flex-direction: column;">
											<p class="details-title">Clinician</p>
											<span class="details-count"><?php echo $activeClinicianCount; ?><span style="font-size: 20px;font-weight: 500;">/<?php echo $totalClinicianCount; ?></span></span>
										</div>
									</div>
									<div class="card" style="background: #F6DADA;">
										<div class="d-flex justify-content-end" style="margin-bottom: 10px;">
											<div class="logged-in-icon-small" style="background: #FFFFFF;">
												<!-- <i class="fa-solid fa-graduation-cap" style="color: #FF746A;"></i> -->
												<!-- <ion-icon name="school" style="color: #FF746A;"></ion-icon> -->
												<i class="fa fa-graduation-cap" aria-hidden="true" style="color: #FF746A;"></i>


											</div>

										</div>
										<div style="display: flex;flex-direction: column;">
											<p class="details-title">Students</p>
											<span class="details-count"><?php echo $activeStudentCount; ?><span style="font-size: 20px;font-weight: 500;">/<?php echo $totalStudentCount; ?></span></span>
										</div>
									</div>
									<div class="card" style="background: #F6DADA;">
										<div class="d-flex justify-content-end" style="margin-bottom: 10px;">
											<div class="logged-in-icon-small" style="background: #FFFFFF;">
												<!-- <i class="fa-solid fa-graduation-cap" style="color: #FF746A;"></i> -->
												<!-- <ion-icon name="school" style="color: #FF746A;"></ion-icon> -->
												<i class="fa fa-graduation-cap" aria-hidden="true" style="color: #FF746A;"></i>


											</div>

										</div>
										<div style="display: flex;flex-direction: column;">
											<p class="details-title"><a href="userDetails.html?type=selfUnlocked" style="color: black;">Self Unlocked Students</a></p>
											<!-- <span class="details-count"><?php echo $activeStudentCount; ?><span style="font-size: 20px;font-weight: 500;">/<?php echo $totalStudentCount; ?></span></span> -->
										</div>
									</div>

									<div class="card" style="background: #ECE2FD;;">
										<div class="d-flex justify-content-end" style="margin-bottom: 10px;">
											<div class="logged-in-icon-small" style="background: #FFFFFF;">
												<!-- <i class="fa-solid fa-graduation-cap" style="color: #FF746A;"></i> -->
												<!-- <ion-icon name="school" style="color: #FF746A;"></ion-icon> -->
												<i class="fa fa-graduation-cap" aria-hidden="true" style="color: #9A81ED;"></i>

											</div>

										</div>
										<div style="display: flex;flex-direction: column;">
											<p class="details-title">Graduates</p>
											<span class="details-count"><a class="active-school-count" href="graduatestudentlist.html?active=<?php echo EncodeQueryData(1); ?>"><?php echo $activeGraduateStudentCount; ?><span style="font-size: 20px;font-weight: 500;">/<a class="active-school-count" href="graduatestudentlist.html?active=<?php echo EncodeQueryData(2); ?>"><?php echo $totalGraduateStudentCount; ?></span></span>

										</div>
									</div>

								</div>
							</div>


							<div class="logged-in-user-number animated-rotation fadeInRight" style="margin-bottom: 20px;">
								<div class="d-flex justify-content-between" style="align-items: center;">
									<p class="section-title">Schools With Rotation Type</p>
									<!-- <div class="logged-in-icon">
                           		 <i class="fa-solid fa-users"></i>
                        		</div> -->
								</div>

								<div class="masonry-grid animated1 fadeIn">
									<div class="card decoration-none card1">
										<a href="userDetails.html?type=rotationsSchool&rotationType=<?php echo EncodeQueryData(1) ?>">
											<div class="d-flex justify-content-end" style="margin-bottom: 10px;">
												<div class="logged-in-icon-small">
													<!-- <i class="fa-solid fa-user-tie" style="color: #519CFF;"></i> -->
													<!-- <ion-icon name="person-add-sharp" style="color: #519CFF;"></ion-icon> -->
													<i class="fa fa-user-plus" aria-hidden="true" style="color: #519CFF;"></i>

												</div>

											</div>
											<div style="display: flex;flex-direction: column;">
												<p class="details-title" class="details-title">Rotation</p>
												<span class="details-count"><?php echo $rotationSchoolCount; ?></span>
											</div>
										</a>
									</div>
									<div class="card decoration-none" style="background: #FFF3DA;">
										<a href="userDetails.html?type=rotationsSchool&rotationType=<?php echo EncodeQueryData(2) ?>">
											<div class="d-flex justify-content-end" style="margin-bottom: 10px;">
												<div class="logged-in-icon-small" style="background: #FFFFFF;">
													<!-- <i class="fa-solid fa-school-circle-check" style="color: #9A81ED;"></i> -->
													<!-- <ion-icon name="fitness" style="color: #FFA24C;"></ion-icon> -->
													<i class="fa fa-heartbeat" aria-hidden="true" style="color: #FFA24C;"></i>


												</div>

											</div>
											<div style="display: flex;flex-direction: column;">
												<p class="details-title">Hospital Sites</p>
												<span class="details-count"><?php echo $hopitalsiteSchoolCount; ?></span>
											</div>
										</a>
									</div>

									<div class="card decoration-none" style="background: #ECE2FD;">
										<a href="userDetails.html?type=rotationsSchool&rotationType=<?php echo EncodeQueryData(3) ?>">
											<div class="d-flex justify-content-end" style="margin-bottom: 10px;">
												<div class="logged-in-icon-small" style="background: #FFFFFF;">
													<!-- <i class="fa-solid fa-school-circle-check" style="color: #9A81ED;"></i> -->
													<!-- <ion-icon name="calendar-outline" style="color: #9A81ED;"></ion-icon> -->
													<i class="fa fa-calendar" aria-hidden="true" style="color: #9A81ED;"></i>


												</div>

											</div>
											<div style="display: flex;flex-direction: column;">
												<p class="details-title">Schedule</p>
												<span class="details-count"><?php echo $scheduleSchoolCount; ?></span>
											</div>
										</a>
									</div>



								</div>
							</div>

							<div class="download-mobile-app">
								<div class="logged-in-user-number animated-download-mobile fadeInRight" style="margin-bottom: 20px;padding: 35px;display: flex;flex-direction: row;">



									<div>
										<p class="section-title" style="margin-bottom: 50px;">Download our Mobile App</p>
										<!-- <div class="logged-in-icon">
										<i class="fa-solid fa-users"></i>
										</div> -->
										<div class="app-download-links">
											<button type="button" class="app-store-btn" style="position: relative;">
												<img class="android-download-img" src="<?php echo BASE_PATH ?>/assets/images/app-store-new1.png" alt="Download APK" style="margin: 0;width: 100%;height:100%;" width="40" height="40">
												<a href="#" class="clipboard-icon" data-tooltip="Copy URL" style="border-top-right-radius: 5px;">
													<i style="color: #5cb85c;" class="fa fa-clipboard" aria-hidden="true"></i>
												</a>
											</button>

											<button type="button" class="play-store-btn" style="position: relative;">
												<img class="android-download-img" src="<?php echo BASE_PATH ?>/assets/images/play-store-new1.png" alt="Download APK" style="margin: 0;width: 100%;height:100%;" width="40" height="40">
												<a href="https://play.google.com/store/apps/details?id=com.clinicaltrac.app&pli=1" class="clipboard-icon" data-tooltip="Copy URL" style="border-top-right-radius: 5px;">
													<i style="color: #5cb85c;" class="fa fa-clipboard" aria-hidden="true"></i>
												</a>
											</button>
										</div>

									</div>
									<img src="<?php echo BASE_PATH ?>/assets/images/download-mobile-app-image-1.svg" alt="" style="width: 40%; height: 100%;margin: auto;">
								</div>
							</div>

						</div>
					</div>


				</div>

			</div>
		<?php } ?>
		<?php
		// if ($currentSchoolId == 1) { 
		?>
		<!-- <div class="col-md-6 margin_bottom_ten">
					<canvas id="myChart" style="width:100%;max-width:600px"></canvas>
				</div> -->
		<?php //} 
		?>
		<?php if ($currentSchoolId == 1) { ?>
			<!-- <div class="col-md-12">
					<div class="panel-group" id="accordion">
						<div class="panel panel-default">
							<div class="panel-heading">
								<h4 class="panel-title">
									<a data-toggle="collapse" data-parent="#accordion" href="#activeClinician"> Active Clinicians
										<span class="glyphicon glyphicon-plus pull-right"></span>
									</a><span>- <?php echo ($TotalActiveclinicianCounts); ?></span>
								</h4>
							</div>
							<div id="activeClinician1" class="panel-collapse collapse">
								<div class="panel-body">
									<table id="activeCliniciaDatatableResponsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
										<thead>
											<tr>
												<th>School Name</th>
												<th>Clinician Name</th>
												<th>Role</th>
												<th>Contact Info</th>
												<th>Created Date</th>
											</tr>
										</thead>
										<tbody>
											<?php
											if ($TotalActiveclinicianCounts > 0) {
												while ($row = mysqli_fetch_array($Activeclinician)) {
													$Fullname = stripslashes($row['Fullname']);;
													$createdDate = stripslashes($row['createdDate']);
													$clinicianRoleId = stripslashes($row['clinicianRoleId']);
													$email = stripslashes($row['email']);
													$phone = stripslashes($row['phone']);
													$cellPhone = stripslashes($row['cellPhone']);
													$schoolId = stripslashes($row['schoolId']);
													$createdDate = converFromServerTimeZone($createdDate, $TimeZone);
													$startDateTimestamp = strtotime($createdDate);

													$objSchool = new clsSchool();
													$objClinicianRoleMaster = new clsClinicianRoleMaster();
													$displayName = $objSchool->GetSchoolNamesSingle($schoolId);
													$ClinicianRoleName = $objClinicianRoleMaster->GetRoleNameSingle($clinicianRoleId);


											?>
													<tr>
														<td><?php echo ($displayName); ?></td>
														<td><?php echo ($Fullname); ?></td>
														<td><?php echo ($ClinicianRoleName); ?></td>
														<td>
															Email: <a href="mailto:<?php echo ($email); ?>">
																<?php echo ($email); ?>
															</a><br>
															Phone: <a href="tel:<?php echo ($phone); ?>">
																<?php echo ($phone); ?>
															</a>
															<?php if ($cellPhone) { ?>
																<br>Cell Phone: <a href="tel:<?php echo ($cellPhone); ?>">
																<?php echo ($cellPhone);
															} ?>
														</td>
														<td><?php echo (date('m/d/Y', $startDateTimestamp)); ?></td>
													</tr>
												<?php
												}
												unset($objSchool);
												unset($objClinicianRoleMaster);
											} else { ?>
												<tr>
													<td colspan="4" align="center">No record(s) available</td>
												<tr>
												<?php } ?>
										</tbody>
									</table>
								</div>
							</div>
						</div>
						<div class="panel panel-default">
							<div class="panel-heading">
								<h4 class="panel-title">
									<a data-toggle="collapse" data-parent="#accordion" href="#Activestudent"> Active Students
										<span class="glyphicon glyphicon-plus pull-right"></span></a><span>- <?php echo ($TotalActiveStudentCounts); ?></span>
								</h4>
							</div>
							<div id="Activestudent1" class="panel-collapse collapse">
								<div class="panel-body">
									<table id="ActivestudentDatatableResponsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
										<thead>
											<tr>
												<th>School Name</th>
												<th>Student Name</th>
												<th>Rank</th>
												<th>Contact Info</th>
												<th>Created Date</th>
											</tr>
										</thead>
										<tbody>

											<?php

											if ($TotalActiveStudentCounts > 0) {
												while ($row = mysqli_fetch_array($Activestudent)) {
													$Fullname = stripslashes($row['Fullname']);;
													$createdDate = stripslashes($row['createdDate']);
													$rankId = stripslashes($row['rankId']);
													$email = stripslashes($row['email']);
													$phone = stripslashes($row['phone']);
													$cellPhone = stripslashes($row['cellPhone']);
													$schoolId = stripslashes($row['schoolId']);
													$createdDate = converFromServerTimeZone($createdDate, $TimeZone);
													$startDateTimestamp = strtotime($createdDate);

													$objSchool = new clsSchool();
													$objStudentRankMaster = new clsStudentRankMaster();
													$displayName = $objSchool->GetSchoolNamesSingle($schoolId);
													$RankName = $objStudentRankMaster->GetRankbyId($rankId);

											?>
													<tr>
														<td><?php echo ($displayName); ?></td>
														<td><?php echo ($Fullname); ?></td>
														<td><?php echo ($RankName); ?></td>
														<td>
															Email: <a href="mailto:<?php echo ($email); ?>">
																<?php echo ($email); ?>
															</a><br>
															Phone: <a href="tel:<?php echo ($phone); ?>">
																<?php echo ($phone); ?>
															</a>
															<?php if ($cellPhone) { ?>
																<br>Cell Phone: <a href="tel:<?php echo ($cellPhone); ?>">
																<?php echo ($cellPhone);
															} ?>
														</td>
														<td><?php echo (date('m/d/Y', $startDateTimestamp)); ?></td>
													</tr>
												<?php }
												unset($objSchool);
												unset($objStudentRankMaster);
											} else { ?>
												<tr>
													<td colspan="5" align="center">No record(s) available</td>
												<tr>
												<?php } ?>


										</tbody>
									</table>
								</div>
							</div>
						</div>

					</div>
					<div class="col-md-12 margin_bottom_ten">
						<center><canvas id="myBarChart" style="width:100%;max-width:600px"></canvas></center>
					</div>

				</div> -->
			<!-- school Admin -->
		<?php } else { ?>
			<div class="formSubHeading">Summary</div>
			<div class="col-md-12 margin_top_ten" style="margin-bottom: 20px;">
				<div class="col-md-4">
					<table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
						<thead>
							<tr>
								<th>Title</th>
								<th style="text-align:right;">Total</th>
							</tr>
						</thead>
						<tbody>
							<?php
							if ($currentSchoolId == 1) { ?>

							<?php } else { ?>
								<tr>
								<tr>
									<td>Students</td>
									<td style="text-align:right;"><?php echo ($totalstudents) ?></td>
								</tr>

								<tr>
									<td>Active Students</td>
									<td style="text-align:right;"><?php echo ($totalActivestudentsCount) ?></td>
								</tr>
								<tr>
									<td>Clinicians</td>
									<td style="text-align:right;"><?php echo ($totalClinicians) ?></td>
								</tr>

								<tr>
									<td>Active Clinicians</td>
									<td style="text-align:right;"><?php echo ($totalActiveCliniciansCount) ?></td>
								</tr>



								<tr>
									<td>Location</td>
									<td style="text-align:right;"><?php echo ($totalLocations); ?></td>
								</tr>


								<tr>

								<tr>
									<td>Hospital Sites</td>
									<td style="text-align:right;"><?php echo ($totalHospitalSites); ?></td>
								</tr>
								<tr>


									<td>Rotations</td>
									<td style="text-align:right;"><?php echo ($totalrotations) ?></td>
								</tr>

								<tr>
									<td>Active Rotations</td>
									<td style="text-align:right;"><?php echo ($totalActiverotationCount) ?></td>
								</tr>

								<tr>
									<td>Upcoming Rotations</td>
									<td style="text-align:right;"><?php echo ($upcomingrotation) ?></td>
								</tr>
								<tr>
									<td>Absence</td>
									<td style="text-align:right;" id="absenceTotalCount"></td>
								</tr>
								<tr>
									<td>Checkoff</td>
									<td style="text-align:right;"><?php echo ($Totalcheckoffcount) ?></td>
								</tr>



							<?php } ?>


						</tbody>
					</table>
				</div>
				<div class="col-md-6 col-md-offset-2">
					<div class="row d-flex margin_zero mb-4 chart-box justify-content-end text-left ct-chart">
						<canvas id="divDashboardChat" class="school-admin-graph"></canvas>
					</div>
				</div>

			</div>

			<div class="col-md-12">
				<div style="display: flex; flex-direction: row-reverse; gap: 20px;">
					<div class="graph-card filters">
						<p class="section-title" style="margin-bottom: 0px;">Filter</p>
						<div>
							<label for="startDate">Start Date:</label>
							<input type="text" id="startDate">
							<!-- <small id="startDateFormatted"></small> -->
							<!-- <small id="startDateDisplay" style="display: block; color: gray;"></small> -->
						</div>
						<div>
							<label for="endDate">End Date:</label>
							<input type="text" id="endDate">
							<!-- <small id="endDateFormatted"></small> -->
							<!-- <small id="endDateDisplay" style="display: block; color: gray;"></small> -->

						</div>
						<div>
							<label for="quickFilter">Quick Filter:</label>
							<select id="quickFilter">
								<option value="">Select</option>
								<!-- <option value="0">Today</option> -->
								<option value="7" selected>Last Week</option>
								<option value="15">Last 15 Days</option>
								<option value="30">Last Month</option>
								<option value="90">Last 3 Months</option>
							</select>
						</div>
						<div class="filter-buttons">
							<button id="saveFilterBtn">Apply</button>
							<button id="resetFilterBtn">Reset</button>
						</div>
					</div>

					<div class="graph-card">
						<canvas id="schoolChart" width="800" height="400"></canvas>
					</div>
				</div>
			</div>
			<div id="modal">
				<div id="modalContent">
					<span class="closeBtn" onclick="closeModal()">&times;</span>
					<h3>Drill Down Detail</h3>
					<div id="modalBody"></div>
				</div>
			</div>

			<div class="col-md-12">

				<div class="panel-group" id="accordion" style="margin-top: 30px;">
					<div class="panel panel-default">
						<div class="panel-heading">
							<h4 class="panel-title">
								<a data-toggle="collapse" data-parent="#accordion" href="#upcommingRotation"> Upcoming Rotations
									<span class="glyphicon glyphicon-plus pull-right"></span>
								</a>
							</h4>
						</div>
						<div id="upcommingRotation" class="panel-collapse collapse">
							<div class="panel-body">
								<table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
									<thead>
										<tr>
											<th>Rotation</th>
											<th>Start Date</th>
											<th>End Date</th>
										</tr>
									</thead>


									<tbody>
										<?php if ($TotalUpcomingRotationDetails > 0) {
											while ($row = mysqli_fetch_array($RowUpcomingRotationDetails)) {

												$title = stripslashes($row['title']);;
												$startDate = stripslashes($row['startDate']);
												$startDate = converFromServerTimeZone($startDate, $TimeZone);
												$endDate = stripslashes($row['endDate']);
												$endDate = converFromServerTimeZone($endDate, $TimeZone);
												$startDateTimestamp = strtotime($startDate);
												$endDateTimestamp = strtotime($endDate);

										?>
												<tr>
													<td><?php echo ($title); ?></td>
													<td><?php echo (date('m/d/Y', $startDateTimestamp)); ?></td>
													<td><?php echo (date('m/d/Y', $endDateTimestamp)); ?></td>
												</tr>
											<?php  		 }
										} else { ?>
											<tr>
												<td colspan="4" align="center">No record(s) available</td>
											<tr>
											<?php } ?>
									</tbody>
								</table>
							</div>
						</div>
					</div>


					<div class="panel panel-default">
						<div class="panel-heading">
							<h4 class="panel-title">
								<a data-toggle="collapse" data-parent="#accordion" href="#upcommingImmunization"> Upcoming Student Immunizations Expire
									<span class="glyphicon glyphicon-plus pull-right"></span></a>
							</h4>
						</div>
						<div id="upcommingImmunization" class="panel-collapse collapse">
							<div class="panel-body">
								<table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
									<thead>
										<tr>
											<th>Student </th>
											<th>Rank</th>
											<th>Immunization</th>
											<th>Expiry Date</th>
										</tr>

									</thead>
									<?php if ($TotalUpcomingImmunizationDetails > 0) {
										while ($row = mysqli_fetch_array($RowImmunizationDetails)) {
											$firstName = stripslashes($row['firstName']);
											$lastName = stripslashes($row['lastName']);
											$fullName = $firstName . ' ' . $lastName;
											$rank = stripslashes($row['title']);
											$immunizationName = stripslashes($row['shortName']);
											$expiryDate = stripslashes($row['expiryDate']);
											$expiryDate = converFromServerTimeZone($expiryDate, $TimeZone);
											$expiryDate = date('m/d/Y', strtotime($expiryDate));

									?>
											<tr>
												<td><?php echo ($fullName); ?></td>
												<td><?php echo ($rank); ?></td>
												<td><?php echo ($immunizationName); ?></td>
												<td><?php echo ($expiryDate); ?></td>
											</tr>
										<?php  		 }
									} else { ?>
										<tr>
											<td colspan="4" align="center">No record(s) available</td>
										<tr>
										<?php } ?>
										<tbody>
										</tbody>
								</table>
							</div>
						</div>
					</div>

					<div class="panel panel-default">
						<div class="panel-heading">
							<h4 class="panel-title">
								<a data-toggle="collapse" data-parent="#accordion" href="#upcommingClinicianImmunization"> Upcoming Clinician Immunizations Expire
									<span class="glyphicon glyphicon-plus pull-right"></span></a>
							</h4>
						</div>
						<div id="upcommingClinicianImmunization" class="panel-collapse collapse">
							<div class="panel-body">
								<table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
									<thead>
										<tr>
											<th>Clinician</th>
											<th>Immunization</th>
											<th>Expiry Date</th>
										</tr>

									</thead>
									<?php if ($TotalClinicianUpcomingImmunizationDetails > 0) {
										while ($row = mysqli_fetch_array($RowClinicianImmunizationDetails)) {
											$firstName = stripslashes($row['firstName']);
											$lastName = stripslashes($row['lastName']);
											$fullname = $firstName . ' ' . $lastName;
											$immunizationName = stripslashes($row['shortName']);
											$expiryDate = stripslashes($row['expiryDate']);
											$expiryDate = converFromServerTimeZone($expiryDate, $TimeZone);
											$expiryDate = date('m/d/Y', strtotime($expiryDate));

									?>
											<tr>
												<td><?php echo ($fullname); ?></td>
												<td><?php echo ($immunizationName); ?></td>
												<td><?php echo ($expiryDate); ?></td>
											</tr>
										<?php  		 }
									} else { ?>
										<tr>
											<td colspan="3" align="center">No record(s) available</td>
										<tr>
										<?php } ?>
										<tbody>
										</tbody>
								</table>
							</div>
						</div>
					</div>

					<div class="panel panel-default">
						<div class="panel-heading">
							<h4 class="panel-title">
								<a data-toggle="collapse" data-parent="#accordion" href="#upcomingAffiliateAgreementExpire"> Upcoming Affiliate Agreement Expire
									<span class="glyphicon glyphicon-plus pull-right"></span>
								</a>
							</h4>
						</div>
						<div id="upcomingAffiliateAgreementExpire" class="panel-collapse collapse">
							<div class="panel-body">
								<table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
									<thead>
										<tr>
											<th>Hopital Site</th>
											<th>Initial Date</th>
											<th>Expire Date</th>
										</tr>
									</thead>


									<tbody>
										<?php if ($TotalUpcomingAffiliateDetails > 0) {
											while ($row = mysqli_fetch_array($RowUpcomingAffiliateDetail)) {

												$title = stripslashes($row['title']);;
												$initialDate = stripslashes($row['initialDate']);
												$initialDate = converFromServerTimeZone($initialDate, $TimeZone);
												$expireDate = stripslashes($row['expireDate']);
												$expireDate = converFromServerTimeZone($expireDate, $TimeZone);
												$initialDateTimestamp = strtotime($initialDate);
												$expireDateTimestamp = strtotime($expireDate);

										?>
												<tr>
													<td><?php echo ($title); ?></td>
													<td><?php echo (date('m/d/Y', $initialDateTimestamp)); ?></td>
													<td><?php echo (date('m/d/Y', $expireDateTimestamp)); ?></td>
												</tr>
											<?php  		 }
										} else { ?>
											<tr>
												<td colspan="4" align="center">No record(s) available</td>
											<tr>
											<?php } ?>
									</tbody>
								</table>
							</div>
						</div>
					</div>


					<div class="panel panel-default">
						<div class="panel-heading">
							<h4 class="panel-title">
								<a data-toggle="collapse" data-parent="#accordion" href="#latestClockInStudent"> Latest Clock In Students
									<span class="glyphicon glyphicon-plus pull-right"></span></a>
							</h4>
						</div>
						<div id="latestClockInStudent" class="panel-collapse collapse">
							<div class="panel-body">
								<table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
									<thead>
										<tr>
											<th>Student </th>
											<th>Rank</th>
											<th>Rotation</th>
											<th>Date</th>
										</tr>

									</thead>
									<tbody>
										<?php if ($TotalRowClockout > 0) {
											while ($row = mysqli_fetch_array($RowClockout)) {
												$firstName = stripslashes($row['firstName']);
												$lastName = stripslashes($row['lastName']);
												$fullname = $firstName . ' ' . $lastName;
												$rank = stripslashes($row['ranktitle']);
												$title = stripslashes($row['title']);
												$clockInDateTime = stripslashes($row['clockInDateTime']);
												$clockInDateTime = converFromServerTimeZone($clockInDateTime, $TimeZone);

										?>
												<tr>
													<td><?php echo ($fullname); ?></td>
													<td><?php echo ($rank); ?></td>
													<td><?php echo ($title); ?></td>
													<td><?php echo ($clockInDateTime); ?></td>
												</tr>
											<?php  		 }
										} else { ?>
											<tr>
												<td colspan="4" align="center">No record(s) available</td>
											<tr>
											<?php } ?>

									</tbody>
								</table>
							</div>
						</div>
					</div>

					<div class="panel panel-default">
						<div class="panel-heading">
							<h4 class="panel-title">
								<a data-toggle="collapse" data-parent="#accordion" href="#attendance"> Attendance
									<span class="glyphicon glyphicon-plus pull-right"></span></a>
							</h4>
						</div>
						<div id="attendance" class="panel-collapse collapse">
							<div class="panel-body">
								<table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
									<thead>
										<tr>
											<th>Student</th>
											<th>Rotation</th>
											<th style="text-align:center">Total Approved Hours</th>
										</tr>
									</thead>
									<tbody>
										<?php if ($TotalRowAttendance > 0) {
											while ($row = mysqli_fetch_array($RowAttendance)) {
												$firstName = stripslashes($row['firstName']);
												$lastName = stripslashes($row['lastName']);
												$fullname = $firstName . ' ' . $lastName;
												$Rotationtitle = stripslashes($row['Rotationtitle']);
												$approvedhours = stripslashes($row['approvedhours']);
												if ($approvedhours != '') {
													$approvedTotalhours = explode(":", $approvedhours);
													$approvedhours = $approvedTotalhours[0] . ":" . $approvedTotalhours[1];
												}
										?>
												<tr>
													<td><?php echo $fullname; ?></td>
													<td><?php echo $Rotationtitle; ?></td>
													<td align="center"><?php echo $approvedhours; ?></td>
												</tr>
											<?php       }
										} else { ?>
											<tr>
												<td colspan="4" align="center">No record(s) available</td>
											<tr>
											<?php } ?>
									</tbody>
								</table>
							</div>
						</div>
					</div>

					<div class="panel panel-default">
						<div class="panel-heading">
							<h4 class="panel-title">
								<a data-toggle="collapse" data-parent="#accordion" href="#Absence"> Absence
									<span class="glyphicon glyphicon-plus pull-right"></span></a>
							</h4>
						</div>
						<div id="Absence" class="panel-collapse collapse">
							<div class="panel-body">
								<table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
									<thead>
										<tr>
											<th>Date</th>
											<th>First Name</th>
											<th>Last Name</th>
											<th>Rank</th>
											<th>Rotation</th>


										</tr>
									</thead>
									<tbody id="absesnceTableBody">

									</tbody>
								</table>
							</div>
						</div>
					</div>

					<div class="panel panel-default">
						<div class="panel-heading">
							<h4 class="panel-title">
								<a data-toggle="collapse" data-parent="#accordion" href="#checkoff"> Checkoff
									<span class="glyphicon glyphicon-plus pull-right"></span></a>
							</h4>
						</div>
						<div id="checkoff" class="panel-collapse collapse">
							<div class="panel-body">
								<table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
									<thead>
										<tr>
											<th>Student</th>
											<th>Rotation</th>

											<?php if ($isActiveCheckoff == 1) { ?>
												<th>Ranking</th>
												<th style="text-align:center;">Done</th>
												<th style="text-align:center;">Left</th>
												<th style="text-align:center;">Score</th>
											<?php } else if ($isActiveCheckoff == 2) { ?>
												<th style="text-align:center;">Score</th>
											<?php } else { ?>
												<th style="text-align:center;">Student Per.</th>
												<th style="text-align:center;">Preceptor Per.</th>
												<th style="text-align:center;">Lab</th>
												<th style="text-align:center;">Clinical</th>
											<?php } ?>

										</tr>
									</thead>
									<tbody>
										<?php if ($Totalcheckoffcount > 0) {
											while ($row = mysqli_fetch_array($Getcheckoff)) {
												$checkoffId = ($row['checkoffId']);
												$rotationId = ($row['rotationId']);
												$studentId = ($row['studentId']);
												$courseId = ($row['courseId']);
												$firstName = stripslashes($row['firstName']);
												$lastName = stripslashes($row['lastName']);
												$fullname = $firstName . ' ' . $lastName;
												$Rotationtitle = stripslashes($row['rotationname']);
												$Ranktitle = stripslashes($row['Ranktitle']);
												$GetDoneCheckoff = $objcheckoff->GetDoneCheckoffs($studentId, $rotationId, $courseId);
												$done = $GetDoneCheckoff['Submitedtopic'];
												$GetLeftCheckoff = $objcheckoff->GetLeftCheckoffs($studentId, $rotationId, $courseId);
												$left = $GetLeftCheckoff['NotSubmitedtopic'];

												$Score = stripslashes($row['calculatedScore']);

												//For Checkoff Score
												$usafScore = stripslashes($row['calculatedUsafScore']);


												//For Selected Preceptor Questions
												$selectedPreceptor = stripslashes($row['calculatedSelectedPreceptor']);
												if ($selectedPreceptor > 0)
													$selectedPreceptor = ($selectedPreceptor * 20) . '%';
												else
													$selectedPreceptor = '-';

												//For Lab Selected Questions
												$selectedLab = stripslashes($row['calculatedSelectedLab']);

												if ($selectedLab > 0)
													$selectedLab = 1;
												else
													$selectedLab = 0;

												//For Clinical Selected Questions
												$selectedClinical = stripslashes($row['calculatedSelectedClinical']);
												if ($selectedClinical > 0)
													$selectedClinical = 1;
												else
													$selectedClinical = 0;

												//For Student Selected Questions
												$totalStudentQuestions = stripslashes($row['calculatedSelectedStudentQuestions']);

												if ($totalStudentQuestions > 0)
													$totalStudentQuestions = ($totalStudentQuestions * 20) . '%';
												else
													$totalStudentQuestions = '-';

										?>
												<tr>
													<td><?php echo $fullname; ?></td>
													<td><?php echo $Rotationtitle; ?></td>
													<?php if ($isActiveCheckoff == 1) { ?>
														<td><?php echo $Ranktitle; ?></td>
														<td style="text-align:center;"><?php echo $done; ?></td>
														<td style="text-align:center;"><?php echo $left; ?></td>
														<td style="text-align:center;"><?php echo number_format((float)$Score, 2, '.', ''); ?></td>
													<?php } else if ($isActiveCheckoff == 2) { ?>
														<td style="text-align:center;"><?php if ($usafScore > 0) echo $usafScore; ?></td>
													<?php } else { ?>
														<td style="text-align:center;"><?php echo $totalStudentQuestions; ?></td>
														<td style="text-align:center;"><?php echo $selectedPreceptor; ?></td>
														<td style="text-align:center;"><?php echo $selectedLab; ?></td>
														<td style="text-align:center;"><?php echo $selectedClinical; ?></td>
													<?php } ?>

												</tr>
											<?php  }
										} else { ?>
											<tr>
												<td colspan="6" align="center">No record(s) available</td>
											<tr>
											<?php } ?>
									</tbody>
								</table>
							</div>
						</div>
					</div>

					<div class="panel panel-default">
						<div class="panel-heading">
							<h4 class="panel-title">
								<a data-toggle="collapse" data-parent="#accordion" href="#drinteraction"> Dr.Interaction
									<span class="glyphicon glyphicon-plus pull-right"></span></a>
							</h4>
						</div>
						<div id="drinteraction" class="panel-collapse collapse">
							<div class="panel-body">
								<table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
									<thead>
										<tr>
											<th>Interaction Date</th>
											<th>Student</th>
											<th>Rotation</th>
											<th style="text-align:center">Points</th>
											<th style="text-align:center">Minutes</th>
										</tr>
									</thead>
									<tbody>
										<?php if ($Totalinteractioncount > 0) {
											while ($row = mysqli_fetch_array($GetInteraction)) {
												$firstName = stripslashes($row['firstName']);
												$lastName = stripslashes($row['lastName']);

												$fullname = $firstName . ' ' . $lastName;
												$interactionDate = stripslashes($row['interactionDate']);
												$interactionDate = converFromServerTimeZone($interactionDate, $TimeZone);
												$interactionDate = date('m/d/Y', strtotime($interactionDate));
												$Rotationtitle = stripslashes($row['title']);
												$pointsAwarded = stripslashes($row['pointsAwarded']);
												$timeSpent = stripslashes($row['timeSpent']);


										?>
												<tr>
													<td><?php echo $interactionDate; ?></td>
													<td><?php echo $fullname; ?></td>
													<td><?php echo $Rotationtitle; ?></td>
													<td align="center"><?php echo $pointsAwarded; ?></td>
													<td align="center"><?php echo $timeSpent; ?></td>
												</tr>
											<?php 	}
										} else { ?>
											<tr>
												<td colspan="4" align="center">No record(s) available</td>
											</tr>
										<?php } ?>
									</tbody>
								</table>
							</div>
						</div>
					</div>

					<div class="panel panel-default">
						<div class="panel-heading">
							<h4 class="panel-title">
								<a data-toggle="collapse" data-parent="#accordion" href="#lateClockIn"> Late Clock In
									<span class="glyphicon glyphicon-plus pull-right"></span></a>
							</h4>
						</div>
						<div id="lateClockIn" class="panel-collapse collapse">
							<div class="panel-body">
								<table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
									<thead>
										<tr>
											<th>Date</th>
											<th>Clock In</th>
											<th>Student In </th>
											<th>Rotation</th>
											<th>Student</th>
										</tr>
									</thead>
									<tbody>
										<?php

										if ($TotalLateClockIn > 0) {
											while ($row = mysqli_fetch_array($lateClockIn)) {
												$firstName = stripslashes($row['firstName']);
												$lastName = stripslashes($row['lastName']);
												$fullname = $firstName . ' ' . $lastName;
												$title = stripslashes($row['title']);

												$startDate = stripslashes($row['startDate']);
												$startDate = converFromServerTimeZone($startDate, $TimeZone);
												$rotationStartDate = date('m/d/Y', strtotime($startDate));
												$rotationStartTime = date('h:i A', strtotime($startDate));

												$clockInDateTime = stripslashes($row['clockInDateTime']);
												$clockInDateTime = converFromServerTimeZone($clockInDateTime, $TimeZone);
												$clockInDate = date('m/d/Y', strtotime($clockInDateTime));
												$clockInTime = date('h:i A', strtotime($clockInDateTime));

										?>

												<tr>

													<td><?php echo ($clockInDate); ?></td>
													<td><?php echo ($rotationStartTime); ?></td>
													<td><?php echo ($clockInTime); ?></td>
													<td><?php echo ($title); ?></td>
													<td><?php echo ($fullname); ?></td>

												</tr>
											<?php 	}
										} else { ?>
											<tr>
												<td colspan="5" align="center">No record(s) available</td>
											<tr>
											<?php } ?>



									</tbody>
								</table>
							</div>
						</div>
					</div>

					<div class="panel panel-default">
						<div class="panel-heading">
							<h4 class="panel-title">
								<a data-toggle="collapse" data-parent="#accordion" href="#earlyClockOut"> Early Clock Out
									<span class="glyphicon glyphicon-plus pull-right"></span></a>
							</h4>
						</div>
						<div id="earlyClockOut" class="panel-collapse collapse">
							<div class="panel-body">
								<table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
									<thead>
										<tr>
											<th>Date</th>
											<th>Scheduled Clock Out</th>
											<th>Student Out </th>
											<th>Rotation</th>
											<th>Student</th>
										</tr>
									</thead>
									<tbody>

										<?php
										if ($TotalEarlryClockOut > 0) {
											while ($row = mysqli_fetch_array($earlyClockOut)) {
												$firstName = stripslashes($row['firstName']);
												$lastName = stripslashes($row['lastName']);
												$fullname = $firstName . ' ' . $lastName;
												$title = stripslashes($row['title']);

												$endDate = stripslashes($row['endDate']);
												$endDate = converFromServerTimeZone($endDate, $TimeZone);
												$rotationEndDate = date('m/d/Y', strtotime($endDate));
												$rotationEndTime = date('h:i A', strtotime($endDate));

												$clockOutDateTime = stripslashes($row['clockOutDateTime']);
												$clockOutDateTime = converFromServerTimeZone($clockOutDateTime, $TimeZone);
												$clockOutDate = date('m/d/Y', strtotime($clockOutDateTime));
												$clockOutTime = date('h:i A', strtotime($clockOutDateTime));
										?>
												<tr><?php
													if ($clockOutDate != '' && $clockOutDate != '0000-00-00' && $clockOutDate != '01/01/1970') {

													?>
														<td>
															<?php

															echo ($clockOutDate);

															?></td>
														<td><?php echo ($rotationEndTime); ?></td>
														<td><?php echo ($clockOutTime); ?></td>
														<td><?php echo ($title); ?></td>
														<td><?php echo ($fullname); ?></td>
													<?php } ?>
												</tr>
											<?php }
										} else { ?>
											<tr>
												<td colspan="5" align="center">No record(s) available</td>
											<tr>
											<?php } ?>


									</tbody>
								</table>
							</div>
						</div>
					</div>
					<?php if ($callOff) { ?>

						<div class="panel panel-default">
							<div class="panel-heading">
								<h4 class="panel-title">
									<a data-toggle="collapse" data-parent="#accordion" href="#callOff"> Call Off
										<span class="glyphicon glyphicon-plus pull-right"></span></a>
								</h4>
							</div>
							<div id="callOff" class="panel-collapse collapse">
								<div class="panel-body">
									<table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
										<thead>
											<tr>
												<th> Date </th>
												<th>Student Name</th>
												<th>Rotation</th>
												<th>Hospital Site</th>
											</tr>
										</thead>
										<tbody>

											<?php
											if ($totalCallOff > 0) {
												while ($row = mysqli_fetch_array($rowsCallOff)) {
													$callOffId = $row['id'];
													$callOffDate = $row['date'];
													if ($callOffDate != '' && $callOffDate != '0000-00-00 00:00:00') {
														$callOffDate = converFromServerTimeZone($callOffDate, $TimeZone);
														$callOffDate = date("m/d/Y", strtotime($callOffDate));
													} else {
														$callOffDate = "-";
													}

													$studentFullName = $row['studentFullName'];
													$rotationName = $row['rotationName'];
													$hospitalSiteName = $row['hospitalSiteName'];
											?>
													<tr>

														<td> <?php echo ($callOffDate); ?></td>
														<td><?php echo ($studentFullName); ?></td>
														<td><?php echo ($rotationName); ?></td>
														<td><?php echo ($hospitalSiteName); ?></td>
													</tr>
												<?php
												}
											} else { ?>
												<tr>
													<td colspan="4" align="center">No record(s) available</td>
												<tr>
												<?php } ?>


										</tbody>
									</table>
								</div>
							</div>
						</div>
					<?php } ?>
					<!---- end----------->


				</div>


			<?php }
		unset($objRotation);
		unset($objAttendance);
			?>
			</div>

			<div id="toast" style="
	position: fixed;
	top: 20px;
	right: 20px;
	background-color: #c25757;
	color: white;
	padding: 10px 20px;
	border-radius: 6px;
	font-size: 14px;
	opacity: 0;
	pointer-events: none;
	transition: opacity 0.3s ease;
	z-index: 9999;
"></div>
	</div>
	<?php include('includes/footer.php'); ?>

	<?php include("includes/datatablejs.php") ?>
	<!-- <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/Charts/Chart.js"></script> -->
	<!-- <script src="https://code.jquery.com/jquery-3.3.1.slim.min.js" integrity="sha384-q8i/X+965DzO0rT7abK41JStQIAqVgRVzpbzo5smXKp4YfRvH+8abtTE1Pi6jizo" crossorigin="anonymous"> </script> -->
	<script src="https://cdn.jsdelivr.net/npm/popper.js@1.14.7/dist/umd/popper.min.js" integrity="sha384-UO2eT0CpHqdSJQ6hJty5KVphtPhzWj9WO1clHTMGa3JDZwrnQq4sF86dIHNDz0W1" crossorigin="anonymous"></script>
	<!-- <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.3.1/dist/js/bootstrap.min.js" integrity="sha384-JjSmVgyd0p3pXB1rRibZUAYoIIy6OrQ6VrjIEaFf/nJGzIxFDsf4x0xIM+B07jRM" crossorigin="anonymous"></script> -->

	<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
	<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>

	<script>
		function showToast(message) {
			const toast = document.getElementById('toast');
			toast.textContent = message;
			toast.style.opacity = 1;
			toast.style.pointerEvents = 'auto';

			setTimeout(() => {
				toast.style.opacity = 0;
				toast.style.pointerEvents = 'none';
			}, 4000); // Hide after 3 seconds
		}
	</script>

	<?php

	if ($currentSchoolId == 1) {
	?>
		<!-- super admin graph -->
		<!-- <script>
			// $(window).on('load', function() {
			// 	loadSuperadminSchoolData();

			// });
			$(window).on('load', function() {
				const today = new Date();
				const pastDate = new Date();
				pastDate.setDate(today.getDate() - 7);

				// Format date to yyyy-mm-dd for input[type=date]
				const formatForInput = (date) => {
					const d = new Date(date);
					const month = ('0' + (d.getMonth() + 1)).slice(-2);
					const day = ('0' + d.getDate()).slice(-2);
					const year = d.getFullYear();
					return `${year}-${month}-${day}`;
				};

				const startDate = formatForInput(pastDate);
				const endDate = formatForInput(today);

				// Set max attribute to today's date to disable future dates
				$('#super-admin-startDate').attr('max', endDate);
				$('#super-admin-endDate').attr('max', endDate);

				// Set values and apply filter
				$('#super-admin-quickFilter').val('last7');
				$('#super-admin-startDate').val(startDate);
				$('#super-admin-endDate').val(endDate);

				applyFilters();
			});

			let superfullData = [];

			function loadSuperadminSchoolData(startdate = '', enddate = '') {

				const url = "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_dashboard_graph_details.html?isAdmin=0&currentSchoolId=<?php echo ($currentSchoolId); ?>" +
					(startdate ? `&startDate=${encodeURIComponent(startdate)}` : '') +
					(enddate ? `&endDate=${encodeURIComponent(enddate)}` : '');
				$.ajax({ //create an ajax request 
					type: "GET",
					url: url,
					dataType: "json",
					success: function(responseData) {

						superfullData = responseData;

						//  Render chart with updated data
						renderChart(superfullData);
					}

				});
			}

			const superctx = document.getElementById('super-admin-schoolChart').getContext('2d');
			let superchart;

			const superchartGreenColor = '#22c55e';

			function supercreateGradient(superctx) {
				const gradient = superctx.createLinearGradient(0, 0, 0, 400);
				gradient.addColorStop(0, `${superchartGreenColor}40`);
				gradient.addColorStop(1, `${superchartGreenColor}00`);
				return gradient;
			}

			function renderChart(dataToDisplay) {

				const labels = dataToDisplay.map(dp => dp.schoolname);
				const values = dataToDisplay.map(dp => dp.totalstudents);

				if (superchart) superchart.destroy();

				superchart = new Chart(superctx, {
					type: 'line',
					data: {
						labels,
						datasets: [{
							label: 'No. of Students',
							data: values,
							fill: true,
							borderColor: superchartGreenColor,
							backgroundColor: supercreateGradient(superctx),
							tension: 0.4,
							pointRadius: 6,
							pointHoverRadius: 8
						}]
					},
					options: {
						responsive: true,
						plugins: {
							tooltip: {
								enabled: true,
								callbacks: {
									backgroundColor: '#ffffff',
									titleColor: '#000000',
									bodyColor: '#000000',
									borderColor: '#000000',
									borderWidth: '1px',
									// title: (context) => context[0].label,
									title: (context) => {
										const i = context[0].dataIndex;
										return dataToDisplay[i].schoolname; // Full name in tooltip
									},
									label: (context) => {
										const i = context.dataIndex;
										const dp = dataToDisplay[i];
										return [
											`Total Students: ${dp.totalstudents}`,
											`Active Students: ${dp.activestudents}`,
											// `Rank: ${dp.rankcount}`
										];
									}
								}
							},
							legend: {
								display: false
							}
						},
						onClick: (e) => {
							const points = superchart.getElementsAtEventForMode(e, 'nearest', {
								intersect: true
							}, true);
							if (points.length) {
								const i = points[0].index;
								supershowModal(dataToDisplay[i]);
							}
						},
						scales: {
							x: {
								title: {
									display: true,
									text: 'Schools',
									color: 'var(--light-text-color)'
								},
								ticks: {
									autoSkip: false,
									callback: function(value, index, ticks) {
										const fullLabel = this.getLabelForValue(value);
										const initials = fullLabel
											.split(' ')
											.map(word => word[0])
											.join('')
											.toUpperCase();
										return initials;
									}
								}
							},
							y: {
								title: {
									display: true,
									text: 'No. of Students',
									color: 'var(--light-text-color)'
								},
								beginAtZero: true
							}
						}
					}
				});
			}

			function supershowModal(dp) {
				// const schoolLink = `<a href="schools.html" target="_blank">${dp.schoolname}</a>`;
				document.getElementById('super-admin-modalBody').innerHTML = `
							<p><strong>School:</strong> ${dp.schoolname}</p>
							<p><strong>Total Students:</strong> ${dp.totalstudents}</p>
							<p><strong>Active Students:</strong> ${dp.activestudents}</p>
							<p><strong>Rank:</strong> ${dp.rankcount}</p>
						`;
				const modal = document.getElementById('super-admin-modal');
				modal.style.display = 'flex';
				setTimeout(() => modal.classList.add('is-open'), 10);
			}

			function supercloseModal() {
				const modal = document.getElementById('super-admin-modal');
				modal.classList.remove('is-open');
				setTimeout(() => modal.style.display = 'none', 300);
			}

			function applyFilters() {
				const quickFilter = $('#super-admin-quickFilter').val();
				let startDate = $('#super-admin-startDate').val();
				let endDate = $('#super-admin-endDate').val();
				// let filteredData = [...superfullData];

				// Reset quick filter if manual date selected
				$('#super-admin-startDate, #super-admin-endDate').on('change', function() {
					$('#super-admin-quickFilter').val('');
				});


				// $('#startDate, #endDate').on('change', updateFormattedDateDisplays);


				// Apply quick filter
				if (quickFilter) {
					const today = new Date();
					let daysAgo = 0;

					switch (quickFilter) {
						case "today":
							startDate = formatDate(today);
							endDate = formatDate(today);
							break;
						case "last7":
							daysAgo = 7;
							break;
						case "last15":
							daysAgo = 15;
							break;
						case "last30":
							daysAgo = 30;
							break;
						case "last90":
							daysAgo = 90;
							break;
					}

					if (quickFilter !== "today") {
						const pastDate = new Date(today);
						pastDate.setDate(today.getDate() - daysAgo);

						startDate = formatDate(pastDate);
						endDate = formatDate(today);
					}

					// Set in inputs for consistency
					$('#super-admin-startDate').val(startDate);
					$('#super-admin-endDate').val(endDate);
				}


				// Apply date range filter if dates are selected
				// if (startDate || endDate) {
				// 	filteredData = filteredData.filter(item => {
				// 		const itemDate = new Date(item.date);
				// 		const from = startDate ? new Date(startDate) : null;
				// 		const to = endDate ? new Date(endDate) : null;

				// 		if (from && itemDate < from) return false;
				// 		if (to && itemDate > to) return false;
				// 		return true;
				// 	});
				// }

				// ✅ Validation 1: Start date should not be in future
				if (startDate) {
					const start = new Date(startDate);
					const today = new Date();
					today.setHours(0, 0, 0, 0);
					start.setHours(0, 0, 0, 0);

					// ✅ Future start date not allowed
					if (start.getTime() > today.getTime()) {
						showToast('Start date cannot be in the future.');
						return;
					}

					// ✅ Prevent more than 3 months ago
					const threeMonthsAgo = new Date(today);
					threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);
					if (start.getTime() < threeMonthsAgo.getTime()) {
						showToast('Please select a date within the last 3 months');
						return;
					}
				}


				// ✅ Validation 4: End date should not be in future (but allow today)
				if (endDate) {
					const end = new Date(endDate);
					const today = new Date();
					today.setHours(0, 0, 0, 0);
					end.setHours(0, 0, 0, 0);

					// ✅ Allow if end date is today or before
					if (end.getTime() > today.getTime()) {
						showToast('End date cannot be in the future.');
						return;
					}
				}

				// ✅ Validation 2: End date should not be less than start date
				if (startDate && endDate) {
					const start = new Date(startDate);
					const end = new Date(endDate);
					if (end < start) {
						showToast('End date must be greater than start date.');
						return;
					}
				}


				loadSuperadminSchoolData(startDate, endDate);
				// renderChart(filteredData);
			}

			function formatDate(date) {
				const yyyy = date.getFullYear();
				const mm = String(date.getMonth() + 1).padStart(2, '0');
				const dd = String(date.getDate()).padStart(2, '0');
				return `${yyyy}-${mm}-${dd}`;
			}

			function resetFilters() {
				const today = new Date();
				const pastDate = new Date();
				pastDate.setDate(today.getDate() - 7);

				const startDate = formatDate(pastDate);
				const endDate = formatDate(today);

				// Set quick filter and dates in input fields
				$('#super-admin-quickFilter').val('last7');
				$('#super-admin-startDate').val(startDate);
				$('#super-admin-endDate').val(endDate);

				applyFilters(); // Apply the filter with updated dates
			}


			// renderChart(superfullData);
		</script> -->

		<!-- <script>
			// Initialize Flatpickr on the input fields
			flatpickr("#super-admin-startDate", {
				dateFormat: "m-d-Y", // Set the date format to mm-dd-yyyy
				maxDate: "today",
				onClose: function(selectedDates, dateStr, instance) {
					applyFilters();
				}
			});

			flatpickr("#super-admin-endDate", {
				dateFormat: "m-d-Y", // Set the date format to mm-dd-yyyy
				maxDate: "today",
				onClose: function(selectedDates, dateStr, instance) {
					applyFilters();
				}
			});

			// Your existing JavaScript code
			$(window).on('load', function() {
				const today = new Date();
				const pastDate = new Date();
				pastDate.setDate(today.getDate() - 7);

				// Format date to mm-dd-yyyy for input[type=text]
				const formatForInput = (date) => {
					const d = new Date(date);
					const month = ('0' + (d.getMonth() + 1)).slice(-2);
					const day = ('0' + d.getDate()).slice(-2);
					const year = d.getFullYear();
					return `${month}-${day}-${year}`;
				};

				const startDate = formatForInput(pastDate);
				const endDate = formatForInput(today);

				// Set values and apply filter
				$('#super-admin-quickFilter').val('last7');
				$('#super-admin-startDate').val(startDate);
				$('#super-admin-endDate').val(endDate);

				applyFilters();
			});

			let superfullData = [];

			function loadSuperadminSchoolData(startdate = '', enddate = '') {
				const url = "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_dashboard_graph_details.html?isAdmin=0&currentSchoolId=<?php echo ($currentSchoolId); ?>" +
					(startdate ? `&startDate=${encodeURIComponent(startdate)}` : '') +
					(enddate ? `&endDate=${encodeURIComponent(enddate)}` : '');
				$.ajax({
					type: "GET",
					url: url,
					dataType: "json",
					success: function(responseData) {
						superfullData = responseData;
						renderChart(superfullData);
					}
				});
			}

			const superctx = document.getElementById('super-admin-schoolChart').getContext('2d');
			let superchart;

			const superchartGreenColor = '#22c55e';

			function supercreateGradient(superctx) {
				const gradient = superctx.createLinearGradient(0, 0, 0, 400);
				gradient.addColorStop(0, `${superchartGreenColor}40`);
				gradient.addColorStop(1, `${superchartGreenColor}00`);
				return gradient;
			}

			function renderChart(dataToDisplay) {
				const labels = dataToDisplay.map(dp => dp.schoolname);
				const values = dataToDisplay.map(dp => dp.totalstudents);

				if (superchart) superchart.destroy();

				superchart = new Chart(superctx, {
					type: 'line',
					data: {
						labels,
						datasets: [{
							label: 'No. of Students',
							data: values,
							fill: true,
							borderColor: superchartGreenColor,
							backgroundColor: supercreateGradient(superctx),
							tension: 0.4,
							pointRadius: 6,
							pointHoverRadius: 8
						}]
					},
					options: {
						responsive: true,
						plugins: {
							tooltip: {
								enabled: true,
								callbacks: {
									backgroundColor: '#ffffff',
									titleColor: '#000000',
									bodyColor: '#000000',
									borderColor: '#000000',
									borderWidth: '1px',
									title: (context) => {
										const i = context[0].dataIndex;
										return dataToDisplay[i].schoolname; // Full name in tooltip
									},
									label: (context) => {
										const i = context.dataIndex;
										const dp = dataToDisplay[i];
										return [
											`Total Students: ${dp.totalstudents}`,
											`Active Students: ${dp.activestudents}`,
										];
									}
								}
							},
							legend: {
								display: false
							}
						},
						onClick: (e) => {
							const points = superchart.getElementsAtEventForMode(e, 'nearest', {
								intersect: true
							}, true);
							if (points.length) {
								const i = points[0].index;
								supershowModal(dataToDisplay[i]);
							}
						},
						scales: {
							x: {
								title: {
									display: true,
									text: 'Schools',
									color: 'var(--light-text-color)'
								},
								ticks: {
									autoSkip: false,
									callback: function(value, index, ticks) {
										const fullLabel = this.getLabelForValue(value);
										const initials = fullLabel
											.split(' ')
											.map(word => word[0])
											.join('')
											.toUpperCase();
										return initials;
									}
								}
							},
							y: {
								title: {
									display: true,
									text: 'No. of Students',
									color: 'var(--light-text-color)'
								},
								beginAtZero: true
							}
						}
					}
				});
			}

			function supershowModal(dp) {
				document.getElementById('super-admin-modalBody').innerHTML = `
				<p><strong>School:</strong> ${dp.schoolname}</p>
				<p><strong>Total Students:</strong> ${dp.totalstudents}</p>
				<p><strong>Active Students:</strong> ${dp.activestudents}</p>
			`;
				const modal = document.getElementById('super-admin-modal');
				modal.style.display = 'flex';
				setTimeout(() => modal.classList.add('is-open'), 10);
			}

			function supercloseModal() {
				const modal = document.getElementById('super-admin-modal');
				modal.classList.remove('is-open');
				setTimeout(() => modal.style.display = 'none', 300);
			}

			function applyFilters() {
				const quickFilter = $('#super-admin-quickFilter').val();
				let startDate = $('#super-admin-startDate').val();
				let endDate = $('#super-admin-endDate').val();

				// Reset quick filter if manual date selected
				$('#super-admin-startDate, #super-admin-endDate').on('change', function() {
					$('#super-admin-quickFilter').val('');
				});

				// Apply quick filter
				if (quickFilter) {
					const today = new Date();
					let daysAgo = 0;

					switch (quickFilter) {
						case "today":
							startDate = formatDate(today);
							endDate = formatDate(today);
							break;
						case "last7":
							daysAgo = 7;
							break;
						case "last15":
							daysAgo = 15;
							break;
						case "last30":
							daysAgo = 30;
							break;
						case "last90":
							daysAgo = 90;
							break;
					}

					if (quickFilter !== "today") {
						const pastDate = new Date(today);
						pastDate.setDate(today.getDate() - daysAgo);

						startDate = formatDate(pastDate);
						endDate = formatDate(today);
					}

					// Set in inputs for consistency
					$('#super-admin-startDate').val(startDate);
					$('#super-admin-endDate').val(endDate);
				}

				// Validation 1: Start date should not be in future
				if (startDate) {
					const start = parseDate(startDate);
					const today = new Date();
					today.setHours(0, 0, 0, 0);
					start.setHours(0, 0, 0, 0);

					if (start.getTime() > today.getTime()) {
						showToast('Start date cannot be in the future.');
						return;
					}

					const threeMonthsAgo = new Date(today);
					threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);
					if (start.getTime() < threeMonthsAgo.getTime()) {
						showToast('Please select a date within the last 3 months');
						return;
					}
				}

				// Validation 4: End date should not be in future (but allow today)
				if (endDate) {
					const end = parseDate(endDate);
					const today = new Date();
					today.setHours(0, 0, 0, 0);
					end.setHours(0, 0, 0, 0);

					if (end.getTime() > today.getTime()) {
						showToast('End date cannot be in the future.');
						return;
					}
				}

				// Validation 2: End date should not be less than start date
				if (startDate && endDate) {
					const start = parseDate(startDate);
					const end = parseDate(endDate);
					if (end < start) {
						showToast('End date must be greater than start date.');
						return;
					}
				}

				loadSuperadminSchoolData(startDate, endDate);
			}

			function formatDate(date) {
				const mm = String(date.getMonth() + 1).padStart(2, '0');
				const dd = String(date.getDate()).padStart(2, '0');
				const yyyy = date.getFullYear();
				return `${yyyy}-${mm}-${dd}`;
			}

			// Helper function to parse date in mm-dd-yyyy format
			function parseDate(dateStr) {
				const [mm, dd, yyyy] = dateStr.split('-');
				return new Date(`${mm}-${dd}-${yyyy}`);
			}

			function resetFilters() {
				const today = new Date();
				const pastDate = new Date();
				pastDate.setDate(today.getDate() - 7);

				const startDate = formatDate(pastDate);
				const endDate = formatDate(today);

				// Set quick filter and dates in input fields
				$('#super-admin-quickFilter').val('last7');
				$('#super-admin-startDate').val(startDate);
				$('#super-admin-endDate').val(endDate);

				applyFilters(); // Apply the filter with updated dates
			}
		</script> -->

		<script>
			const flatpickrInstances = {};

			// Initialize Flatpickr on the input fields
			flatpickrInstances.start = flatpickr("#super-admin-startDate", {
				dateFormat: "m-d-Y",
				maxDate: "today",
				onClose: function(selectedDates, dateStr, instance) {
					applyFilters();
				}
			});

			flatpickrInstances.end = flatpickr("#super-admin-endDate", {
				dateFormat: "m-d-Y",
				maxDate: "today",
				onClose: function(selectedDates, dateStr, instance) {
					applyFilters();
				}
			});


			// Your existing JavaScript code
			$(window).on('load', function() {
				const today = new Date();
				const pastDate = new Date();
				pastDate.setDate(today.getDate() - 7);

				const defaultFilter = $('#super-admin-quickFilter').val('last7');
				if (defaultFilter) {
					applyFilters(defaultFilter);
				}

				// Format date to mm-dd-yyyy for input[type=text]
				const formatForInput = (date) => {
					const d = new Date(date);
					const month = ('0' + (d.getMonth() + 1)).slice(-2);
					const day = ('0' + d.getDate()).slice(-2);
					const year = d.getFullYear();
					return `${month}-${day}-${year}`;
				};

				// Format date to yyyy-mm-dd for API calls
				const formatForAPI = (date) => {
					const d = new Date(date);
					const month = ('0' + (d.getMonth() + 1)).slice(-2);
					const day = ('0' + d.getDate()).slice(-2);
					const year = d.getFullYear();
					return `${year}-${month}-${day}`;
				};

				const startDate = formatForInput(pastDate);
				const endDate = formatForInput(today);

				// Set values and apply filter
				$('#super-admin-quickFilter').val('last7');
				flatpickrInstances.start.setDate(parseDate(startDate), true);
				flatpickrInstances.end.setDate(parseDate(endDate), true);

				applyFilters();
			});

			let superfullData = [];

			function loadSuperadminSchoolData(startdate = '', enddate = '') {
				// Format dates for API call
				const formatDateForAPI = (dateStr) => {
					const [month, day, year] = dateStr.split('-');
					return `${year}-${month}-${day}`;
				};

				const apiStartDate = startdate ? formatDateForAPI(startdate) : '';
				const apiEndDate = enddate ? formatDateForAPI(enddate) : '';

				const url = "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_dashboard_graph_details.html?isAdmin=0&currentSchoolId=<?php echo ($currentSchoolId); ?>" +
					(apiStartDate ? `&startDate=${encodeURIComponent(apiStartDate)}` : '') +
					(apiEndDate ? `&endDate=${encodeURIComponent(apiEndDate)}` : '');
				$.ajax({
					type: "GET",
					url: url,
					dataType: "json",
					success: function(responseData) {
						superfullData = responseData;
						renderChart(superfullData);
					}
				});
			}

			const superctx = document.getElementById('super-admin-schoolChart').getContext('2d');
			let superchart;

			const superchartGreenColor = '#22c55e';

			function supercreateGradient(superctx) {
				const gradient = superctx.createLinearGradient(0, 0, 0, 400);
				gradient.addColorStop(0, `${superchartGreenColor}40`);
				gradient.addColorStop(1, `${superchartGreenColor}00`);
				return gradient;
			}

			function renderChart(dataToDisplay) {
				const labels = dataToDisplay.map(dp => dp.schoolname);
				const values = dataToDisplay.map(dp => dp.totalstudents);

				if (superchart) superchart.destroy();

				superchart = new Chart(superctx, {
					type: 'line',
					data: {
						labels,
						datasets: [{
							label: 'No. of Students',
							data: values,
							fill: true,
							borderColor: superchartGreenColor,
							backgroundColor: supercreateGradient(superctx),
							tension: 0.4,
							pointRadius: 6,
							pointHoverRadius: 8
						}]
					},
					options: {
						responsive: true,
						plugins: {
							tooltip: {
								enabled: true,
								callbacks: {
									backgroundColor: '#ffffff',
									titleColor: '#000000',
									bodyColor: '#000000',
									borderColor: '#000000',
									borderWidth: '1px',
									title: (context) => {
										const i = context[0].dataIndex;
										return dataToDisplay[i].schoolname; // Full name in tooltip
									},
									label: (context) => {
										const i = context.dataIndex;
										const dp = dataToDisplay[i];
										return [
											`Total Students: ${dp.totalstudents}`,
											`Active Students: ${dp.activestudents}`,
										];
									}
								}
							},
							legend: {
								display: false
							}
						},
						onClick: (e) => {
							const points = superchart.getElementsAtEventForMode(e, 'nearest', {
								intersect: true
							}, true);
							if (points.length) {
								const i = points[0].index;
								supershowModal(dataToDisplay[i]);
							}
						},
						scales: {
							x: {
								title: {
									display: true,
									text: 'Schools',
									color: 'var(--light-text-color)'
								},
								ticks: {
									autoSkip: false,
									callback: function(value, index, ticks) {
										const fullLabel = this.getLabelForValue(value);
										const initials = fullLabel
											.split(' ')
											.map(word => word[0])
											.join('')
											.toUpperCase();
										return initials;
									}
								}
							},
							y: {
								title: {
									display: true,
									text: 'No. of Students',
									color: 'var(--light-text-color)'
								},
								beginAtZero: true
							}
						}
					}
				});
			}

			function supershowModal(dp) {
				document.getElementById('super-admin-modalBody').innerHTML = `
            <p><strong>School:</strong> ${dp.schoolname}</p>
            <p><strong>Total Students:</strong> ${dp.totalstudents}</p>
            <p><strong>Active Students:</strong> ${dp.activestudents}</p>
        	`;
				const modal = document.getElementById('super-admin-modal');
				modal.style.display = 'flex';
				setTimeout(() => modal.classList.add('is-open'), 10);
			}

			function supercloseModal() {
				const modal = document.getElementById('super-admin-modal');
				modal.classList.remove('is-open');
				setTimeout(() => modal.style.display = 'none', 300);
			}

			function applyFilters(quickFilter = '') {
				let startDate = $('#super-admin-startDate').val();
				let endDate = $('#super-admin-endDate').val();

				if (quickFilter) {
					const today = new Date();
					let daysAgo = 0;

					switch (quickFilter) {
						case "today":
							startDate = formatForInput(today);
							endDate = formatForInput(today);
							break;
						case "last7":
							daysAgo = 7;
							break;
						case "last15":
							daysAgo = 15;
							break;
						case "last30":
							daysAgo = 30;
							break;
						case "last90":
							daysAgo = 90;
							break;
					}

					if (quickFilter !== "today") {
						const pastDate = new Date(today);
						pastDate.setDate(today.getDate() - daysAgo);

						startDate = formatForInput(pastDate);
						endDate = formatForInput(today);
					}

					// ✅ Update flatpickr values
					flatpickrInstances.start.setDate(parseDate(startDate), true);
					flatpickrInstances.end.setDate(parseDate(endDate), true);

					// ✅ Update dropdown value
					$('#super-admin-quickFilter').val(quickFilter);

					// ✅ Trigger data fetch with updated values
					loadSuperadminSchoolData(startDate, endDate);
					return; // skip below manual validation block
				}

				// ✅ Manual validation if user enters custom dates
				if (startDate) {
					const start = parseDate(startDate);
					const today = new Date();
					today.setHours(0, 0, 0, 0);
					start.setHours(0, 0, 0, 0);

					if (start.getTime() > today.getTime()) {
						showToast('Start date cannot be in the future.');
						return;
					}

					const threeMonthsAgo = new Date(today);
					threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);
					if (start.getTime() < threeMonthsAgo.getTime()) {
						showToast('Please select a date within the last 3 months');
						return;
					}
				}

				if (endDate) {
					const end = parseDate(endDate);
					const today = new Date();
					today.setHours(0, 0, 0, 0);
					end.setHours(0, 0, 0, 0);

					if (end.getTime() > today.getTime()) {
						showToast('End date cannot be in the future.');
						return;
					}
				}

				if (startDate && endDate) {
					const start = parseDate(startDate);
					const end = parseDate(endDate);
					if (end < start) {
						showToast('End date must be greater than start date.');
						return;
					}
				}

				// ✅ Trigger data fetch with manually selected dates
				loadSuperadminSchoolData(startDate, endDate);
			}




			// Format date to mm-dd-yyyy for display
			function formatForInput(date) {
				const d = new Date(date);
				const month = ('0' + (d.getMonth() + 1)).slice(-2);
				const day = ('0' + d.getDate()).slice(-2);
				const year = d.getFullYear();
				return `${month}-${day}-${year}`;
			}

			// Helper function to parse date in mm-dd-yyyy format
			function parseDate(dateStr) {
				const [month, day, year] = dateStr.split('-');
				return new Date(year, month - 1, day);
			}

			function resetFilters() {
				const today = new Date();
				const pastDate = new Date();
				pastDate.setDate(today.getDate() - 7);

				const startDate = formatForInput(pastDate);
				const endDate = formatForInput(today);

				// Set quick filter and dates in input fields
				$('#super-admin-quickFilter').val('last7');
				flatpickrInstances.start.setDate(parseDate(startDate), true);
				flatpickrInstances.end.setDate(parseDate(endDate), true);

				// Disable the reset button
				$('#super-admin-resetFilterBtn').prop('disabled', true);

				applyFilters(); // Apply the filter with updated dates
			}

			// Event Listener for Quick Filter Dropdown
			$('#super-admin-quickFilter').on('change', function() {
				const selectedValue = $(this).val();
				if (selectedValue === "last7") {
					$('#super-admin-resetFilterBtn').prop('disabled', true);
				} else {
					$('#super-admin-resetFilterBtn').prop('disabled', false);
				}
				applyFilters();
			});

			$('#super-admin-quickFilter').on('change', function() {
				const selected = $(this).val();
				applyFilters(selected);
			});

			let manualChange = false;

			// Enable the flag only after page load
			$(window).on('load', function() {
				const defaultFilter = $('#super-admin-quickFilter').val();
				if (defaultFilter) {
					applyFilters(defaultFilter);
				}
				manualChange = true; // ✅ Now user changes will be tracked
			});

			// Clear quick filter when manual date is picked
			$('#super-admin-startDate, #super-admin-endDate').on('change', function() {
				if (manualChange) {
					$('#super-admin-quickFilter').val('');
					$('#super-admin-resetFilterBtn').prop('disabled', false);
				}
			});



			// Event Listener for Save Filter Button
			$('#super-admin-saveFilterBtn').on('click', function() {
				const selectedValue = $('#super-admin-quickFilter').val();
				if (selectedValue === "last7") {
					$('#super-admin-resetFilterBtn').prop('disabled', true);
				} else {
					$('#super-admin-resetFilterBtn').prop('disabled', false);
				}
			});

			// Event Listener for Reset Filter Button
			$('#super-admin-resetFilterBtn').on('click', function() {
				$('#super-admin-quickFilter').val('last7');
				$('#super-admin-resetFilterBtn').prop('disabled', true);
				resetFilters();
			});

			// Initial check to disable the reset button if "Last Week" is selected by default
			if ($('#super-admin-quickFilter').val() === "last7") {
				$('#super-admin-resetFilterBtn').prop('disabled', true);
			}
		</script>

		<script>
			var totalSchool = '<?php echo ($totalSchool); ?>';
			var totalActiveSchoolCount = '<?php echo ($totalActiveSchoolCount); ?>';
			var totalInActiveSchoolCount = totalSchool - totalActiveSchoolCount;
			var xValues = ["Active Schools", "Inactive Schools"];
			var yValues = [totalActiveSchoolCount, totalInActiveSchoolCount];
			var barColors = ["#2d69b6", "#666666"];

			document.addEventListener("DOMContentLoaded", () => {
				function counter(id, start, end, duration) {
					let obj = document.getElementById(id),
						current = start,
						range = end - start,
						increment = end > start ? 1 : -1,
						step = Math.abs(Math.floor(duration / range)),
						timer = setInterval(() => {
							current += increment;
							obj.textContent = current;
							if (current == end) {
								clearInterval(timer);
							}
						}, step);
				}
				// var totalUsers = '<?php //echo $totalUsers 
										?>';
				var totalBlockedUsers = '<?php echo $totalBlockedUsers; ?>';
				// counter("count1", 0, totalUsers, 1);
				// counter("count2", 0, totalBlockedUsers, 1500);
			});

			var ctx = document.getElementById('myChart').getContext('2d');

			// Assuming Utils.months is not defined or causing issues, replacing it with static labels for demonstration
			var labels = [<?php echo ($chartYearsInString); ?>];
			var data = {
				labels: labels,
				datasets: [{
					label: 'Active Students',
					data: [<?php echo ($chartYearsActiveStudentsInString); ?>],
					backgroundColor: [
						'rgb(54, 162, 235)',
						'rgb(54, 162, 235)',
						'rgb(54, 162, 235)',
						'rgb(54, 162, 235)',
						'rgb(54, 162, 235)',
						'rgb(153, 102, 255)',
						'rgb(201, 203, 207)'
					],
					borderColor: [
						'rgb(54, 162, 235)',
						'rgb(54, 162, 235)',
						'rgb(54, 162, 235)',
						'rgb(54, 162, 235)',
						'rgb(54, 162, 235)',
						'rgb(153, 102, 255)',
						'rgb(201, 203, 207)'
					],
					borderWidth: 1,
					barThickness: 30 // Set bar width to 30px
				}]
			};

			var config = {
				type: 'bar',
				data: data,
				options: {
					responsive: true, // Ensures canvas size is responsive
					maintainAspectRatio: false, // Allows custom aspect ratio
					plugins: {
						title: {
							display: true,
							text: 'Annual Student count' // Specify your chart title
						},
						legend: {
							display: true, // Show the legend
							onClick: (e) => e.stopPropagation() // Stop the show/hide functionality
						}
					},
					scales: {
						y: {
							beginAtZero: true
						}
					}
				}
			}

			var myChart = new Chart(ctx, config);
		</script>
	<?php
	} else {
	?>

		<!-- school admin graph -->
		<script>
			$(document).ready(function() {
				let fullData = [];

				// Initialize Flatpickr with MM-DD-YYYY format
				flatpickr("#startDate", {
					dateFormat: "m-d-Y",
					defaultDate: new Date(new Date().setDate(new Date().getDate() - 7)),
					maxDate: "today",
					onChange: function(selectedDates, dateStr) {
						$('#startDateFormatted').text(`(${dateStr})`);
					}
				});

				flatpickr("#endDate", {
					dateFormat: "m-d-Y",
					defaultDate: new Date(),
					maxDate: "today",
					onChange: function(selectedDates, dateStr) {
						$('#endDateFormatted').text(`(${dateStr})`);
					}
				});

				// Event Listener for Quick Filter Dropdown
				$('#quickFilter').on('change', function() {
					const selectedValue = $(this).val();
					if (selectedValue === "7") {
						$('#resetFilterBtn').prop('disabled', true);
					} else {
						$('#resetFilterBtn').prop('disabled', false);
					}
					applyQuickFilter(parseInt(selectedValue));
				});

				// Event Listener for Save Filter Button
				$('#saveFilterBtn').on('click', function() {
					const selectedValue = $('#quickFilter').val();
					if (selectedValue === "7") {
						$('#resetFilterBtn').prop('disabled', true);
					} else {
						$('#resetFilterBtn').prop('disabled', false);
					}
				});

				// Event Listener for Reset Filter Button
				$('#resetFilterBtn').on('click', function() {
					$('#quickFilter').val('7');
					$('#resetFilterBtn').prop('disabled', true);
					applyQuickFilter(7);
				});

				// Initial check to disable the reset button if "Last Week" is selected by default
				if ($('#quickFilter').val() === "7") {
					$('#resetFilterBtn').prop('disabled', true);
				}

				// Disable future dates in both date inputs
				const todayStr = new Date().toISOString().split('T')[0];
				$('#startDate, #endDate').attr('max', todayStr);

				// Helper Function to format YYYY-MM-DD to MM-DD-YYYY
				function convertToMMDDYYYY(dateStr) {
					if (!dateStr) return '';
					const parts = dateStr.split('-');
					if (parts.length === 3) {
						const [year, month, day] = parts;
						return `${month.padStart(2, '0')}-${day.padStart(2, '0')}-${year}`;
					}
					return dateStr;
				}

				// Helper Function to format MM-DD-YYYY to YYYY-MM-DD
				function convertToYYYYMMDD(dateStr) {
					if (!dateStr) return '';
					const parts = dateStr.split('-');
					if (parts.length === 3) {
						const [month, day, year] = parts;
						return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
					}
					return dateStr;
				}

				$('#startDate, #endDate').on('input', function() {
					const startVal = $('#startDate').val();
					const endVal = $('#endDate').val();
					$('#startDateFormatted').text(startVal ? `(${startVal})` : '');
					$('#endDateFormatted').text(endVal ? `(${endVal})` : '');
				});

				// Main Data Loading Function
				function loadAdminSchoolData(startdate = '', enddate = '') {
					const url = "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_dashboard_graph_details.html?isAdmin=1&currentSchoolId=<?php echo ($currentSchoolId); ?>" +
						(startdate ? `&startDate=${encodeURIComponent(startdate)}` : '') +
						(enddate ? `&endDate=${encodeURIComponent(enddate)}` : '');

					$.ajax({
						type: "GET",
						url: url,
						dataType: "json",
						success: function(responseData) {
							fullData = responseData.map(dp => {
								let dateStringForDisplay = dp.dates;
								if (dateStringForDisplay && dateStringForDisplay.includes('T')) {
									dateStringForDisplay = dateStringForDisplay.split('T')[0];
								}
								return {
									...dp,
									dates: dateStringForDisplay
								};
							});
							updateChart(fullData);
						},
						error: function(jqXHR, textStatus, errorThrown) {
							console.error("AJAX Error:", textStatus, errorThrown, jqXHR.responseText);
							showToast('Error loading data. Please try again.');
						}
					});
				}

				// Chart Setup
				const ctx1 = $('#schoolChart')[0].getContext('2d');
				let chart;
				const chartGreenColor = '#22c55e';

				function createGradient(ctx) {
					const gradient = ctx.createLinearGradient(0, 0, 0, 400);
					gradient.addColorStop(0, `${chartGreenColor}40`);
					gradient.addColorStop(1, `${chartGreenColor}00`);
					return gradient;
				}

				function updateChart(filteredData) {
					const labels = filteredData.map(dp => convertToMMDDYYYY(dp.dates));
					const values = filteredData.map(dp => dp.studentCount);

					if (chart) chart.destroy();

					chart = new Chart(ctx1, {
						type: 'line',
						data: {
							labels,
							datasets: [{
								label: 'No. of Students',
								data: values,
								fill: true,
								borderColor: chartGreenColor,
								backgroundColor: createGradient(ctx1),
								tension: 0.4,
								pointRadius: 6,
								pointHoverRadius: 8
							}]
						},
						options: {
							responsive: true,
							plugins: {
								tooltip: {
									backgroundColor: '#ffffff',
									titleColor: '#000000',
									bodyColor: '#000000',
									borderColor: '#000000',
									borderWidth: '1px',
									enabled: true,
									callbacks: {
										label: function(context) {
											const i = context.dataIndex;
											const dp = filteredData[i];
											return [
												`Date: ${convertToMMDDYYYY(dp.dates)}`,
												`Students: ${dp.studentCount}`,
												`Checkoffs: ${dp.checkoffCount}`,
												`Daily Journal: ${dp.journalCount}`,
												`Dr. Interactions: ${dp.interactionCount}`,
												`Click for details`
											];
										}
									}
								},
								legend: {
									display: false
								}
							},
							onClick: function(e) {
								const points = chart.getElementsAtEventForMode(e, 'nearest', {
									intersect: true
								}, true);
								if (points.length) {
									const i = points[0].index;
									showModal(filteredData[i]);
								}
							},
							scales: {
								x: {
									title: {
										display: true,
										text: 'Date',
										color: 'var(--light-text-color)'
									}
								},
								y: {
									title: {
										display: true,
										text: 'No. of Students',
										color: 'var(--light-text-color)'
									},
									beginAtZero: true
								}
							}
						}
					});
				}

				// Modal Display Function
				function showModal(dp) {
					$('#modalBody').html(`
						<p><strong>Date:</strong> ${convertToMMDDYYYY(dp.dates)}</p>
						<p><strong>Students:</strong> ${dp.studentCount}</p>
						<p><strong>Checkoffs:</strong> ${dp.checkoffCount}</p>
						<p><strong>Daily Journal:</strong> ${dp.journalCount}</p>
						<p><strong>Dr. Interactions:</strong> ${dp.interactionCount}</p>
						`);
					const $modal = $('#modal');
					$modal.css('display', 'flex');
					setTimeout(() => $modal.addClass('is-open'), 10);
				}

				window.closeModal = function() {
					const $modal = $('#modal');
					$modal.removeClass('is-open');
					setTimeout(() => $modal.css('display', 'none'), 300);
				}

				// Filter Application Functions
				function applyQuickFilter(days) {
					const today = new Date();
					const start = new Date(today);
					start.setDate(start.getDate() - days);

					const formattedStart = start.toISOString().split('T')[0];
					const formattedEnd = today.toISOString().split('T')[0];

					$('#startDate').val(convertToMMDDYYYY(formattedStart));
					$('#endDate').val(convertToMMDDYYYY(formattedEnd));

					loadAdminSchoolData(formattedStart, formattedEnd);
				}

				// Event Listeners
				$('#quickFilter').on('change', function() {
					applyQuickFilter(parseInt($(this).val()));
				});

				$('#saveFilterBtn').on('click', function() {
					const start = convertToYYYYMMDD($('#startDate').val());
					const end = convertToYYYYMMDD($('#endDate').val());

					if (!start || !end) {
						showToast('Please select both a start and end date.');
						return;
					}

					const startDateObj = new Date(start + 'T00:00:00');
					const endDateObj = new Date(end + 'T00:00:00');
					const today = new Date(new Date().toISOString().split('T')[0] + 'T00:00:00');

					if (startDateObj > today) {
						showToast('Start date cannot be in the future.');
						return;
					}
					if (endDateObj > today) {
						showToast('End date cannot be in the future.');
						return;
					}
					if (startDateObj > endDateObj) {
						showToast('End date must be greater than or equal to start date.');
						return;
					}

					const threeMonthsAgo = new Date(today);
					threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);
					threeMonthsAgo.setHours(0, 0, 0, 0);

					if (startDateObj < threeMonthsAgo) {
						showToast('Please select a start date within the last 3 months.');
						return;
					}

					const maxRangeEnd = new Date(startDateObj);
					maxRangeEnd.setMonth(maxRangeEnd.getMonth() + 3);

					if (endDateObj.getTime() > maxRangeEnd.getTime()) {
						showToast('You can only filter data within a maximum range of 3 months.');
						return;
					}

					loadAdminSchoolData(start, end);
				});

				$('#resetFilterBtn').on('click', function() {
					$('#quickFilter').val('7');
					applyQuickFilter(7);
				});

				$('#startDate, #endDate').on('input', function() {
					$('#quickFilter').val('');
				});

				// Initial chart load when the page loads
				applyQuickFilter(7);
			});



			// function convertToMMDDYYYY(dateStr) {
			// 	if (!dateStr) return ''; // Handle empty or null input
			// 	const parts = dateStr.split('-');
			// 	if (parts.length === 3) {
			// 		const [year, month, day] = parts;
			// 		const date = new Date(month - 1, day, year); // Month is 0-indexed
			// 		const formattedMonth = (date.getMonth() + 1).toString().padStart(2, '0');
			// 		const formattedDay = date.getDate().toString().padStart(2, '0');
			// 		const formattedYear = date.getFullYear();
			// 		return `${formattedMonth}-${formattedDay}-${formattedYear}`;
			// 	}
			// 	return dateStr;
			// }
		</script>


		<script>
			$(window).on('load', function() {
				loadAbsenceData();

			});

			function loadAbsenceData() {
				$.ajax({ //create an ajax request to display.php
					type: "GET",
					url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_get_student_absence_in_dashboard.html?isAdmin=1&currentSchoolId=<?php echo ($currentSchoolId); ?>",
					dataType: "html", //expect html to be returned                
					success: function(responseData) {
						var obj = jQuery.parseJSON(responseData);
						$("#absesnceTableBody").html(obj.tableHtml);
						$("#absenceTotalCount").html(obj.tableCount);
						// alert(response);
					}

				});
			}

			$(document).ready(function() {
				// Add minus icon for collapse element which is open by default
				$(".collapse.in").each(function() {
					$(this).siblings(".panel-heading").find(".glyphicon").addClass("glyphicon-minus").removeClass("glyphicon-plus");
				});

				// Toggle plus minus icon on show hide of collapse element
				$(".collapse").on('show.bs.collapse', function() {
					$(this).parent().find(".glyphicon").removeClass("glyphicon-plus").addClass("glyphicon-minus");
				}).on('hide.bs.collapse', function() {
					$(this).parent().find(".glyphicon").removeClass("glyphicon-minus").addClass("glyphicon-plus");
				});
			});


			var ctx = document.getElementById('divDashboardChat').getContext('2d');

			// Assuming Utils.months is not defined or causing issues, replacing it with static labels for demonstration
			var data = {

				labels: [<?php echo ($chartDatesInString); ?>],
				datasets: [{
						label: 'Clock In',
						data: [<?php echo ($checkIndataPointsInString); ?>],
						borderWidth: 1,
						backgroundColor: 'green'
					},
					{
						label: 'Clock Out',
						data: [<?php echo ($checkOutdataPointsInString); ?>],
						borderWidth: 1,
						backgroundColor: 'gray'
					}
				]


			};

			var config = {
				type: 'bar',
				data: data,
				options: {
					responsive: true, // Ensures canvas size is responsive
					maintainAspectRatio: false, // Allows custom aspect ratio
					plugins: {
						title: {
							display: true,
							text: '7 Day\'s Clock In/Out' // Specify your chart title
						},
						legend: {
							display: true, // Show the legend
							onClick: (e) => e.stopPropagation() // Stop the show/hide functionality
						}
					},
					scales: {
						y: {
							beginAtZero: true,
							barPercentage: 0.5
							// ticks: {
							// 	stepSize: 0.5,
							// 	callback: function(value, index, values) {
							// 		return value.toFixed(1); // Formats ticks to one decimal place
							// 	}
							// }
						}
					}
				}
			}

			var myChart = new Chart(ctx, config);
		</script>


	<?php
	}
	?>

	<script>
		document.querySelectorAll('.app-store-btn').forEach(button => {
			button.addEventListener('click', (event) => {
				if (!event.target.closest('.clipboard-icon')) {
					window.open("https://apps.apple.com/in/app/clinical-trac-rt/id6450888634", "_blank");
				}
			});
		});

		document.querySelectorAll('.play-store-btn').forEach(button => {
			button.addEventListener('click', (event) => {
				if (!event.target.closest('.clipboard-icon')) {
					window.open("https://play.google.com/store/apps/details?id=com.clinicaltrac.app&pli=1", "_blank");
				}
			});
		});

		document.querySelectorAll('.clipboard-icon').forEach(clipboardIcon => {
			clipboardIcon.addEventListener('click', (event) => {
				event.preventDefault();

				let urlToCopy;

				if (clipboardIcon.closest('.play-store-btn')) {
					urlToCopy = "https://play.google.com/store/apps/details?id=com.clinicaltrac.app&pli=1";
				} else {
					urlToCopy = "https://apps.apple.com/in/app/clinical-trac-rt/id6450888634";
				}

				navigator.clipboard.writeText(urlToCopy)
					.then(() => {
						console.log('URL copied to clipboard!');

						const toast = document.createElement('div');
						toast.classList.add('toast');
						toast.textContent = 'Copied!';
						document.body.appendChild(toast);

						setTimeout(() => {
							toast.remove();
						}, 2000);
					})
					.catch((err) => {
						console.error('Failed to copy URL:', err);
					});
			});
		});
	</script>



</body>

</html>