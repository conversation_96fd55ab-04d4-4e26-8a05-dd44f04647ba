<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsSchool.php');
include('../class/clsSystemUser.php');
include('../class/clsStudent.php');
include('../class/clsSummative.php');
include('../class/clsRotation.php');
include('../class/clsLocations.php');
include('../setRequest.php');
include('../class/clsHospitalSite.php');
include('../class/clsExternalPreceptors.php');

$summativerotationid = 0;
$schoolId = 0;
$currentstudentId = 0;
$schoolId = $currentSchoolId;
$transchooldisplayName = '';
$currentstudentId = $_SESSION["loggedStudentId"];
$TimeZone = '';
$TimeZone = $_SESSION["loggedStudentSchoolTimeZone"];

//Get Email
$objDB = new clsDB();
$loggedStudentEmailId = $objDB->GetSingleColumnValueFromTable('student', 'email', 'studentId', $currentstudentId);
unset($objDB);

if (isset($_GET['summativerotationid'])) //Edit Mode
{
    $summativerotationid = $_GET['summativerotationid'];
    $summativerotationid = DecodeQueryData($summativerotationid);
} else {
    $schoolId = $currentSchoolId;
    $transchooldisplayName = $currenschoolDisplayname;
}

$title = "Summative Evalution " . $transchooldisplayName;

$objSummative = new clsSummative();
$getsummativedetails = $objSummative->GetAllSummative($summativerotationid, $currentstudentId);
$totalSummativeCount = 0;
if ($getsummativedetails != '') {
    $totalSummativeCount = mysqli_num_rows($getsummativedetails);
}


$objStudent = new clsStudent();
unset($objStudent);
$objRotation = new clsRotation();
$RotationName = $objRotation->GetrotationDetails($summativerotationid, $schoolId);

$rotationtitle = $RotationName ? $RotationName['title'] :'';

//Get All Active Hospital Sites
$rotationId = $summativerotationid;
$objHospitalSite = new clsHospitalSite();
$activHospitalSites = $objHospitalSite->GetAllActiveSMSHospitalSite($currentstudentId, $rotationId);
$activHospitalSitesCount = 0;
if ($activHospitalSites != '')
    $activHospitalSitesCount = mysqli_num_rows($activHospitalSites);
unset($activHospitalSites);

$status = isset($_GET["status"]) ? $_GET["status"] : '';
$rotationStatus = checkRotationStatus($rotationId);

?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo ($title); ?></title>
    <?php include('includes/headercss.php'); ?>
    <?php include("includes/datatablecss.php") ?>

    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/css/magnific-popup.css">

</head>

<body>
    <?php include('includes/header.php'); ?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <li><a href="rotations.html">Rotation</a></li>
                    <li class="active"><a href="rotations.html"><?php echo ($rotationtitle); ?></a></li>
                    <li class="active">Summative Evaluation</li>

                </ol>
            </div>

            <?php if ($activHospitalSitesCount > 0 && $rotationStatus == 0) { ?>
                <div class="pull-right">
                    <?php
                    $hrefLink = 'assignEvaluationToProceptor.html?rotationId=' . EncodeQueryData($rotationId) . '&isEvalType=summative';
                    ?>
                    <a class="btn btn-link addCommentpopup" href="<?php echo $hrefLink; ?>"> Add </a>
                    <?php
                    ?>
                </div>
            <?php } ?>

        </div>
    </div>

    <div class="container">

        <?php
        if (isset($_GET["status"])) {
            if ($_GET["status"] == "added") {
        ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Summative Evaluation added successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "updated") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Summative Evaluation updated successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Deleted") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Summative Evaluation deleted successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Error") {
            ?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Error occurred.
                </div>
        <?php
            }
        }
        ?>


        <div id="divTopLoading">Loading...</div>
        <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
            <thead>
                <tr>
                    <th>First Name</th>
                    <th>Last Name</th>
                    <th style="text-align: center">Evaluation <br> Date</th>
                    <th style="text-align: center">Signature <br> Date</th>
                    <th style="text-align: center">Preceptor <br> Info</th>
                    <th style="text-align: center">Eval Total/Avg</th>
                    <th style="text-align: center">Overall Rating</th>
                    <th style="text-align: center">Action</th>
                </tr>
            </thead>
            <tbody>
                <?php
                if ($totalSummativeCount > 0) {
                    while ($row = mysqli_fetch_array($getsummativedetails)) {
                        $title = $row['title'];
                        $firstName = $row['firstName'];
                        $lastName = $row['lastName'];
                        $studentName = $firstName . ' ' . $lastName;
                        $studentSummativeMasterId = $row['studentSummativeMasterId'];
                        $evaluationDate = stripslashes($row['evaluationDate']);

                        $courselocationId = $row['locationId'];
                        $parentRotationId = stripslashes($row['parentRotationId']);
                        $rotationLocationId = stripslashes($row['rotationLocationId']);
                        $rotationId = stripslashes($row['rotationId']);

                        $locationId = 0;
                        if ($rotationLocationId != $courselocationId && $parentRotationId > 0) {
                            if ($parentRotationId > 0) {
                                if (!$rotationLocationId)
                                    $locationId = $objRotation->GetLocationByRotation($rotationId);
                                else
                                    $locationId  = $rotationLocationId;
                            }
                        } else {
                            $locationId  = $courselocationId;
                        }

                        //Get Time Zone By Rotation 
                        $objLocation = new clsLocations();
                        $TimeZone = $objLocation->GetTimeZoneByLocation($locationId);
                        unset($objLocation);
                        if ($TimeZone == '')
                            $TimeZone = $_SESSION["loggedStudentSchoolTimeZone"];

                        $evaluationDate = converFromServerTimeZone($evaluationDate, $TimeZone);
                        $evaluationDate = date("m/d/Y", strtotime($evaluationDate));
                        $dateOfStudentSignature = stripslashes($row['dateOfStudentSignature']);
                        $dateOfStudentSignature = converFromServerTimeZone($dateOfStudentSignature, $TimeZone);
                        $dateOfStudentSignature = date("m/d/Y", strtotime($dateOfStudentSignature));
                        $totalUserCount = 0;

                        // Preceptor info

                        $preceptorId = $row['preceptorId'];
                        $isPreceptorCompletedStatus = $row['isPreceptorCompletedStatus'];
                        if ($preceptorId > 0) {
                            $objExternalPreceptors = new clsExternalPreceptors();
                            $externalPreceptorDetail = $objExternalPreceptors->GetExternalPreceptorDetail($preceptorId);
                            $preceptorFirstName = isset($externalPreceptorDetail['firstName']) ? $externalPreceptorDetail['firstName'] : '';
                            $preceptorLastName = isset($externalPreceptorDetail['lastName']) ? $externalPreceptorDetail['lastName'] : '';
                            $preceptorNum = isset($externalPreceptorDetail['mobile_num']) ? $externalPreceptorDetail['mobile_num'] : '';
                            $preceptorFullName = $preceptorFirstName . ' ' . $preceptorLastName;
                            $maskedPreceptorNum = maskNumber($preceptorNum);
                            $preceptorInfo = 'Name: ' . $preceptorFullName . '</br>Phone: ' . $maskedPreceptorNum;
                            if ($isPreceptorCompletedStatus)
                                $preceptorInfo .= '</br>Status: Completed';
                            else
                                $preceptorInfo .= '</br>Status: Pending';
                        } else {
                            $preceptorInfo = "-";
                        }
                        //Total Eval And Avg
                        $getSelectedOptionList = $objSummative->GetSiteEvaluationSelectedOptionListBySiteEvaluationMasterId($studentSummativeMasterId);
                        $totalSelectedOptionCount = $getSelectedOptionList ? mysqli_num_rows($getSelectedOptionList) : '';
                        $selectedOptionText = [];
                        if ($totalSelectedOptionCount) {
                            while ($optionRow = mysqli_fetch_array($getSelectedOptionList)) {
                                $optionText = $optionRow['optionText'];
                                $optionText = (int) filter_var($optionText, FILTER_SANITIZE_NUMBER_INT);
                                $selectedOptionText[] = $optionText;
                            }
                        }
                        $evalTotal = count($selectedOptionText) ? array_sum($selectedOptionText) : 0;
                        $evalAvg = $evalTotal ? $evalTotal / 24 : 0;
                        $evalAvg = $evalAvg ? (number_format((float)$evalAvg, 1, '.', '')) : 0;

                        //Overall Rating
                        $overallQuestionId = $objSummative->GetSiteEvaluationOverallRotationEvaluationQuestionId($schoolId);
                        $overAllSelectedQuestion = $objSummative->GetSiteEvaluationOverallRotationEvaluationQuestion($studentSummativeMasterId, $overallQuestionId);
                        $evalTotals = $evalTotal . " | " . $evalAvg;
                        if ($preceptorId) {
                            if ($isPreceptorCompletedStatus) {
                                $evalTotals = $evalTotals;
                            } else {
                                $evalTotals = '';
                            }
                        }
                ?>
                        <tr>
                            <td><?php echo ($firstName); ?></td>
                            <td><?php echo ($lastName); ?></td>
                            <td style="text-align: center"><?php echo ($evaluationDate); ?></td>
                            <td style="text-align: center"><?php
                                                            if ($dateOfStudentSignature != '' && $dateOfStudentSignature != '01/01/1970' && $dateOfStudentSignature != '12/31/1969') {
                                                                echo ($dateOfStudentSignature);
                                                            } else {
                                                                echo "-";
                                                            }

                                                            ?>
                            </td>
                            <td><?php echo ($preceptorInfo);
                            if ($preceptorId > 0 && $isPreceptorCompletedStatus == 0) { ?>
                                <br> <a href="javascript:void(0);" class="reSendrequest" data-toggle="modal" data-target="#resendModal" preceptorId="<?php echo $preceptorId ?>" preceptorNum="<?php echo $preceptorNum ?>" evaluationId="<?php echo $studentSummativeMasterId ?>" rotationId="<?php echo $rotationId ?>" onclick="ShowEvaluationDetails(this)">Resend SMS</a>
                              |  <a href="javascript:void(0);" class="copyLink" preceptorId="<?php echo EncodeQueryData($preceptorId); ?>" preceptorNum="<?php echo ($preceptorNum); ?>" evaluationId="<?php echo ($studentSummativeMasterId); ?>" rotationId="<?php echo ($rotationId); ?>" evaluationType='summative' onclick="copyLinkUrl(this)">Click to Copy URL</a>
                              <br> <a href="javascript:void(0);" class="reSendEmailrequest" data-toggle="modal" preceptorId="<?php echo EncodeQueryData($preceptorId); ?>" preceptorNum="<?php echo $preceptorNum ?>" evaluationId="<?php echo $studentSummativeMasterId ?>" rotationId="<?php echo $rotationId ?>" data-target="#resendEmailModal" email="<?php echo $loggedStudentEmailId; ?>" evaluationType='summative' onclick="ShowEvaluationDetailsForEmail(this)">Send URL to Email</a>

                                <?php } ?>
                            </td>
                            <td style="text-align: center"><?php echo $evalTotals; ?></td>
                            <td style="text-align: center"><?php echo $overAllSelectedQuestion; ?></td>
                            <?php
                                $rotationStatus = checkRotationStatus($rotationId);
                            
                            if ($rotationStatus) {   ?>
                                <td style="text-align: center">
                                    <a href="summative.html?studentSummativeMasterId=<?php echo (EncodeQueryData($studentSummativeMasterId)); ?>
									&summativerotationid=<?php echo (EncodeQueryData($summativerotationid)); ?>&view=1&isPreceptor=<?php echo (EncodeQueryData($preceptorId)); ?>">View</a>
                                </td>
                            <?php } else if ($dateOfStudentSignature != '' && $dateOfStudentSignature != '01/01/1970' && $dateOfStudentSignature != '12/31/1969') {   ?>
                                <td style="text-align: center">
                                    <a href="summative.html?studentSummativeMasterId=<?php echo (EncodeQueryData($studentSummativeMasterId)); ?>
									&summativerotationid=<?php echo (EncodeQueryData($summativerotationid)); ?>&view=1&isPreceptor=<?php echo (EncodeQueryData($preceptorId)); ?>">View</a>
                                </td>
                            <?php } else if ($preceptorId > 0 && $isPreceptorCompletedStatus == 0) {   ?>
                                <td style="text-align: center">
                                    <a href="summative.html?studentSummativeMasterId=<?php echo (EncodeQueryData($studentSummativeMasterId)); ?>
									&summativerotationid=<?php echo (EncodeQueryData($summativerotationid)); ?>&view=1&isPreceptor=<?php echo (EncodeQueryData($preceptorId)); ?>">Pending</a>
                                </td>
                            <?php } else { ?>
                                <td style="text-align: center">
                                    <a href="summative.html?studentSummativeMasterId=<?php echo (EncodeQueryData($studentSummativeMasterId)); ?>
									&summativerotationid=<?php echo (EncodeQueryData($summativerotationid)); ?>&isPreceptor=<?php echo (EncodeQueryData($preceptorId)); ?>">Signoff</a>
                                </td>
                            <?php } ?>
                        </tr>
                <?php
                    }
                }
                unset($objRotation);
                unset($objSummative);
                ?>
            </tbody>
        </table>
    </div>

    <?php include('includes/footer.php'); ?>
    <?php include('includes/resendSms.php'); ?>
    <?php include('includes/resendLinkToEmail.php'); ?>
    <?php include("includes/datatablejs.php") ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/js/jquery.magnific-popup.min.js"></script>

    <script type="text/javascript">
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "summ-control";
        // for magnificPopup
        $('.addCommentpopup').magnificPopup({
            'type': 'ajax',
            'closeOnBgClick': false
        });

        $(window).load(function() {
            $("#divTopLoading").addClass('hide');

            var status = '<?php echo $status; ?>';
            if (status == 'added') {
                alertify.success('Message Sent Successfully.');
            }
        });

        $(document).on('click', '.btnSendSms', function() {
            var data = $('#resendForm').serialize();
            var preNum = $('#preceptorNum').val();
            if (preNum != '') {
                $.ajax({
                    type: "POST",
                    url: "../ajax/send_evaluation_sms_to_preceptor.html",
                    data: {
                        data
                    },
                    success: function(data) {
                        if (data != '') {
                            alertify.error('Error Occured');
                        } else {
                            alertify.success('Sent');
                            window.location.reload();
                            // history.go(0);
                        }
                    }
                });
            }



        });

        function ShowEvaluationDetails(eleObj) {
            var preceptorId = $(eleObj).attr('preceptorId');
            var preceptorNum = $(eleObj).attr('preceptorNum');
            var evaluationId = $(eleObj).attr('evaluationId');
            var rotationId = $(eleObj).attr('rotationId');

            $('#preceptorId').val(preceptorId);
            $('#preceptorNum').val(preceptorNum);
            $('#evaluationId').val(evaluationId);
            $('#rotationId').val(rotationId);
            $('#evaluationType').val('summative');
        }

        var current_datatable = $("#datatable-responsive").DataTable({
            "ordering": true,
            "order": [
                [2, "desc"]
            ],

            "aoColumns": [{
                    "sWidth": "15%"
                }, {
                    "sWidth": "15%"
                }, {
                    "sWidth": "15%"
                }, {
                    "sWidth": "25%",
                    "sClass": "alignCenter",

                },
                {
                    "sWidth": "25%",
                    "sClass": "alignCenter",

                },
                {
                    "sWidth": "25%",
                    "sClass": "alignCenter",

                }, {
                    "sWidth": "20%",
                    "sClass": "alignCenter",

                }, {
                    "sWidth": "10%",
                    "sClass": "alignCenter",
                    "bSortable": false
                }
            ]
        });

        // ajax call sum deleteAjaxRow

        // ajax call sum delete
        function ShowDeleteMessage() {
            alertify.alert('Warning', 'This is the Primary User. You can\'t delete this.');
        }

        function ShowCountMessage() {
            alertify.alert('Warning', 'This role is already in used. You can\'t delete this.');
        }


        $(document).on('click', '.deleteAjaxRow', function() {

            var current_datatable_row = current_datatable.row($(this).parents('tr'));
            var studentSummativeMasterId = $(this).attr('studentSummativeMasterId');
            var title = $(this).attr('studentName');

            alertify.confirm('Student Summative: ' + title, 'Continue with delete?', function() {
                $.ajax({
                    type: "GET",
                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                    data: {
                        id: studentSummativeMasterId,
                        type: 'studentSummative'
                    },
                    success: function() {
                        current_datatable.row(current_datatable_row).remove().draw(false);
                        alertify.success('Deleted');
                    }
                });
            }, function() {});

        });

       
    </script>


</body>

</html>