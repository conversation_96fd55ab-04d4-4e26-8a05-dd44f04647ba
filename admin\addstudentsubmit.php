<?php
session_start();
$loggedstudentId = isset($_SESSION["loggedstudentId"]) ? $_SESSION["loggedstudentId"] : 0;
$loggedUserId = isset($_SESSION['loggedUserId']) ? $_SESSION['loggedUserId'] : 0;

if ($loggedUserId)
	include('includes/validateUserLogin.php');

include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../setRequest.php');
include('../class/clsStudent.php');
include('../class/clsNotification.php');
include('../class/clsSendEmails.php');
include('../class/clsSMTPSettings.php');
include('../class/PasswordHash.php');
include('../class/Zebra_Image.php');
include("../class/class.phpmailer.php");
include('../class/clsCanvas.php');
include('../class/clsChatApp.php');
// include("../class/class.smtp.php");

// throw new Exception("Division by zero");
// echo '<pre>';
// print_r($_POST);exit;
if ($_SERVER['REQUEST_METHOD'] == "POST") {
	$type = isset($_POST['hiddenType']) ? ($_POST['hiddenType']) : 0;
	$studentId = isset($_GET['id']) ? DecodeQueryData($_GET['id']) : 0;
	$status = ($studentId > 0) ? 'updated' : 'added';
	//Get inputs	
	$recordIdNumber  = isset($_POST['recordIdNumber']) ? $_POST['recordIdNumber'] : 0;
	$firstName  = trim($_POST['txtFirstName']);
	$middleName  = trim($_POST['txtMiddleName']);
	$lastName  = trim($_POST['txtLastName']);
	$email  = trim($_POST['txtEmail']);
	$phone  = trim($_POST['txtPhone']);
	$cellPhone  = trim($_POST['txtcellPhone']);
	$address1  = trim($_POST['txtAddress1']);
	$address2  = trim($_POST['txtAddress2']);
	$cboCountry  = trim($_POST['cboCountry']);
	$city  = trim($_POST['txtCity']);
	$state  = trim($_POST['cboState']);
	$zipCode  = trim($_POST['txtZipCode']);
	$userName = trim($_POST['txtUsername']);
	$password = '';
	if (isset($_POST['txtpassword'])) {
		$password = trim($_POST['txtpassword']);
	}
	$cbolocation  = trim($_POST['cbolocation']);
	$cborank  = trim($_POST['cborank']);
	$emailtopassword  = isset($_POST['chkemailtopassword']) ? trim($_POST['chkemailtopassword']) : 0;


	//save data
	$objStudent = new clsStudent();
	$objStudent->locationId = $cbolocation;
	$objStudent->rankId = $cborank;
	$objStudent->recordIdNumber = $recordIdNumber;
	$objStudent->firstName = ucfirst($firstName);
	$objStudent->middleName = ucfirst($middleName);
	$objStudent->lastName = ucfirst($lastName);
	$objStudent->email = $email;
	$objStudent->phone = $phone;
	$objStudent->cellPhone = $cellPhone;
	$objStudent->address1 = $address1;
	$objStudent->address2 = $address2;
	$objStudent->cboCountry = $cboCountry;
	$objStudent->city = $city;
	$objStudent->stateId = $state;
	$objStudent->zip = $zipCode;
	$objStudent->isActive = '1';
	$objStudent->username = $userName;
	$objStudent->schoolId = $currentSchoolId;
	$objStudent->createdBy = isset($_SESSION['loggedUserId']) ? $_SESSION['loggedUserId'] : 0;
	$objStudent->isEmailPassword = $emailtopassword;

	//Generate password
	if ($studentId == 0)
		$objStudent->passwordHash = PasswordHash::hash($password);

	$retstudentId = $objStudent->SaveStudent($studentId);

	if ($retstudentId > 0) {


		//Update Student ID Number
		if ($recordIdNumber) {
			$objDB = new clsDB();
			$objDB->UpdateSingleColumnValueToTable('accreditation', 'recordNo', $recordIdNumber, 'studentId', $retstudentId);
			unset($objDB);
		}

		if (!$studentId) {
			//save data
			$objNotification = new clsNotification();

			$notification = 'Student added.';
			$modul = 'Student';
			$userType = 'SA';
			$created_at = date("Y-m-d");
			$objNotification->schoolId = $currentSchoolId;
			$objNotification->notification = $notification;
			$objNotification->modul = $modul;
			$objNotification->userType = $userType;
			$objNotification->referenceId = $retstudentId;
			$objNotification->created_at = $created_at;
			$objNotification->beforeDays = 0;
			$objNotification->afterDays = 0;
			$objNotification->beforemessage = 0;
			$objNotification->aftermessage = 0;
			$objNotification->isActiveNotification = 0;
			$notificationId = $objNotification->SaveWebNotification();
		}
		//print_r($_FILES['fileLogo']);exit;
		if (isset($_FILES['fileLogo'])) {

			$Image = $_FILES['fileLogo']['name'];
			if ($Image) {
				$ext = strtolower(pathinfo($_FILES['fileLogo']['name'], PATHINFO_EXTENSION));
				if ($ext != "png" && $ext != "jpg" && $ext != "jpeg" && $ext != "gif") {
					header('location:editprofile.html?status=InvalidFile');
					exit();
				}

				//File Name
				$bigName = 'logo.' . $ext;
				$logoSmallName = 'logo_small.' . $ext;

				//Check User Directory
				$uploaddir = "../upload/schools/" . $currentSchoolId;
				if (!file_exists($uploaddir)) {
					mkdir($uploaddir);
				}

				$uploaddir = "../upload/schools/" . $currentSchoolId . "/student/";
				if (!file_exists($uploaddir)) {
					mkdir($uploaddir);
				}

				$uploaddir = "../upload/schools/" . $currentSchoolId . "/student/" . $retstudentId . '/';
				if (!file_exists($uploaddir)) {
					mkdir($uploaddir);
				}

				//Save File Path
				$UploadFilePath = $uploaddir . $bigName;

				//Copy File
				copy($_FILES['fileLogo']['tmp_name'], $UploadFilePath);
				$UploadFilePathSmall = $uploaddir . $logoSmallName;
				//Copy File
				copy($_FILES['fileLogo']['tmp_name'], $UploadFilePathSmall);


				if (isset($_POST['chkAutoCrop'])) {

					$image = new Zebra_Image();
					$image->source_path = $UploadFilePath;
					$image->target_path = $UploadFilePath;
					$image->jpeg_quality = 85;
					$image->preserve_aspect_ratio = true;
					$image->enlarge_smaller_images = true;
					$image->preserve_time = true;
					$image->resize(200, 200, ZEBRA_IMAGE_CROP_CENTER, '-1');

					//Crop and Resize Image
					$image->source_path = $UploadFilePathSmall;
					$image->target_path = $UploadFilePathSmall;
					$image->jpeg_quality = 85;
					$image->preserve_aspect_ratio = true;
					$image->enlarge_smaller_images = true;
					$image->preserve_time = true;
					$image->resize(70, 70, ZEBRA_IMAGE_CROP_CENTER, '-1');
					unset($image);
				}
				//-----------------------------------
				//Update File Name to DB
				//-----------------------------------
				$objStudent->UpdateStudentImageName($retstudentId, $bigName, $logoSmallName);
				//-----------------------------------
			}
		}

		//------------------------------------
		if ($retstudentId == $loggedstudentId) {
			//Get user Details
			$row = $objStudent->GetStudentDetails($retstudentId, $currentOrganizationId);

			//Get Image Path
			$StudentImagePath = GetStudentImagePath($retstudentId, $currentOrganizationId, $row['imageName']);
			$_SESSION["loggedStudentName"] = $row['userName'];
			$_SESSION["loggedStudentFirstName"] =  $row['firstName'];
			$_SESSION["loggedStudentLastName"] = $row['lastName'];
			$_SESSION["loggedStudentImage"] = $StudentImagePath;
		}

		unset($objStudent);

		if ($studentId == 0 && $emailtopassword == 1) {
			$objSendEmails = new clsSendEmails($currentSchoolId);
			// $objSendEmails->SendPasswordToStudent($password,$retstudentId,$email,$currenSchoolLogoImagePath,$currenschoolDisplayname);
			$objSendEmails->SendStudentLoginDetails($retstudentId, $password);
			unset($objSendEmails);
		}

		//Create User in canvas and enroll to course. 
		if ($isActiveCanvas && $studentId == 0) {
			$objCanvas = new clsCanvas();
			$objSchool = new clsSchool();
			$objDB = new clsDB();

			//Check already exist or not
			$schoolDetails = $objSchool->GetSchoolDetails($currentSchoolId);
			$canvasToken = isset($schoolDetails['canvasAcessToken']) ? $schoolDetails['canvasAcessToken'] : '';
			$canvasCourseId = isset($schoolDetails['canvasCourseId']) ? $schoolDetails['canvasCourseId'] : '';

			if ($canvasToken && $canvasCourseId) {
				$requests['studentFullName'] =  ucfirst($firstName) . ' ' . ucfirst($lastName);
				$requests['emailId'] = $email;

				//Create User
				$canvasUser = $objCanvas->createUser($canvasToken, '102391', $requests);
				if ($canvasUser) {
					$canvasUser = json_decode($canvasUser);
					$canvasUserId = isset($canvasUser->id) ? $canvasUser->id : 0;

					if ($canvasUserId) {
						//Update Canvas UserId in DB
						$objDB->UpdateSingleColumnValueToTable('student', 'canvasUserId', $canvasUserId, 'studentId', $retstudentId);

						//Enroll User to Course
						$requests['userId'] = $canvasUserId;
						$objCanvas->enrollUserToCourse($canvasToken, $canvasCourseId, $requests);
					}
				}
			}
		}

		unset($objDB);
		unset($objSchool);
		unset($objCanvas);
		
		//Audit Log Start
		// Instantiate the Logger class
		$objLog = new clsLogger();

		// Determine the action type (EDIT or ADD) based on the presence of a journal ID
		$action = ($studentId > 0) ? $objLog::EDIT : $objLog::ADD;
		$userType = $objLog::ADMIN; // User type is set to ADMIN
		$IsMobile = 0;

		$objStudent = new clsStudent();
		$objStudent->saveStudentAuditLog($retstudentId, $loggedUserId, $userType, $action, $IsMobile);
		unset($objStudent);

		unset($objLog);
		//Audit Log Ends
		// Initialize Chat and Database objects
		$objChatApp = new clsChatApp();
		$objDB = new clsDB();

		// Define role ID for the user
		$role_id = 4;

		// Fetch user role management ID and profile image name from the database
		$userRoleManagementId = $objDB->GetSingleColumnValueFromTable('userrolemanagement', 'id', 'userId', $retstudentId, 'role_Id', $role_id);
		$profileImageName = $objDB->GetSingleColumnValueFromTable('student', 'profilePic', 'studentId', $retstudentId);

		// Release database object after use
		unset($objDB);

		// Set default values if necessary
		$userRoleManagementId = $userRoleManagementId ?: 0;
		$profileImagePath = '';

		// Check if profile image exists, then set its path
		if (!empty($profileImageName)) {
			$profileImagePath = BASE_PATH . '/upload/schools/' . $currentSchoolId . '/student/' . $retstudentId . '/' . $profileImageName . '?id=' . rand(1, 10000);
			$objDB = new clsDB();
			$objDB->UpdateSingleColumnValueToTable('userrolemanagement', 'profileImagePath', $profileImagePath, 'id', $userRoleManagementId);
		}

		// Define a common function to set ChatApp user properties and save the user data
		$retUserRoleManagementId = $objChatApp->setAndSaveChatAppUser($objChatApp, $retstudentId, $firstName, $lastName, $email, $phone, $address1, $profileImagePath, $currenschoolDisplayname, $currentSchoolId, $role_id, $userRoleManagementId);

		if ($retUserRoleManagementId) {
			$objChatApp->prepareAndSendUserData($objChatApp, $retstudentId, $firstName, $lastName, $email, $phone, $profileImagePath, $address1, $role_id, $currenschoolDisplayname, $currentSchoolId, $userRoleManagementId);
		}
		if ($userRoleManagementId) {
			$objChatApp->UpdateUserProfilePicTochat($profileImagePath, $retstudentId, $role_id);
		}

		// exit;
		if ($loggedUserId) {
			if ($type)
				header('location:studentIdsList.html?status=' . $status);
			else
				header('location:schoolstudents.html?status=' . $status . '&rankId=' . EncodeQueryData($cborank));
		} else
			header('location:thankyou.html');

		if ($chkemailtopassword && $studentId == 0) {
			//Send email
			//------------------------------------------------------
			$objSendEmails = new clsSendEmails($currentSchoolId);
			$objSendEmails->SendStudentLoginDetails($retStudentId, $tempPassword);
			unset($objSendEmails);
		}
	} else {
		unset($objStudent);
		if ($loggedUserId)
			header('location:addstudent.html?status=error');
		else
			header('location:studentRegister.html?status=error');
	}
} else {
	if ($loggedUserId)
		header('location:addstudent.html');
	else
		header('location:studentRegister.html');

	exit();
}
