<?php
include("../../includes/config.php");
include("../../includes/commonfun.php");
include("../../class/clsDB.php");
include("../../class/clsStudent.php");
include("../../class/clsSummative.php");
include("../../class/clsRotation.php");
include("../../class/clsLocations.php");
include("../../class/clsStatusCodes.php");
include('../../class/clsExternalPreceptors.php');
include("../../class/clsClinician.php");
include('validateParameters.php');

$isTrue = true;
$isFalse = false;
$objStatusCode = new clsStatusCodes();

$UserId = "";
$Timezone = "";
$studentTimezone = "";
$AccessToken = '';
$errroMessage = "";
$rotationId = 0;

// Check if the request method 
if ($_SERVER['REQUEST_METHOD'] === 'GET') {

	$userType = isset($_GET['UserType']) ? $_GET['UserType'] : 0;
	$RoleType = isset($_GET['RoleType']) ? $_GET['RoleType'] : '';
	$LoggedUserType = $userType ? 'Clinician' : 'Student';

	$requestParameters = array(
		'UserId' => isset($_GET['UserId']) ? $_GET['UserId'] : 0,
		'AccessToken' => isset($_GET['AccessToken']) ? $_GET['AccessToken'] : '',
		'PageNo' => isset($_GET['PageNo']) ? $_GET['PageNo'] : 0
	);

	$validatedParameters = validateParameters($requestParameters, $objStatusCode, $isFalse, $isTrue);

	if (!$validatedParameters) {
		exit();
	}

	$UserId = $validatedParameters['UserId'];
	$AccessToken = $validatedParameters['AccessToken'];
	$PageNo = $validatedParameters['PageNo'];

	if (!ValidateUserAccessToken($UserId, $AccessToken)) {
		CreateResponce($objStatusCode::HTTP_UNAUTHORIZED, $isFalse, 'You are not authorized user to use this service');
		exit();
	}
	//-----------------------------------
	// SearchText
	$searchText = isset($_GET['SearchText']) ? $_GET['SearchText'] : '';

	//Validate rotationId
	//-----------------------------------	
	if (isset($_GET['RotationId']) && $_GET['RotationId'] != '') {
		$rotationId = ($_GET['RotationId']);
	} else {
		CreateResponce($objStatusCode::HTTP_BAD_REQUEST, $isFalse, 'Please pass rotation id');
		exit();
	}
	//-----------------------------------
	// Records per page
	$recordsPerPage = isset($_GET['RecordsPerPage']) ? $_GET['RecordsPerPage'] : 10;

	//-----------------------------------
	//Validate User
	$objUser = $userType ? new clsClinician() : new clsStudent();
	$rowUser = $userType ? $objUser->GetClinicianDetails($UserId) : $objUser->GetUserDetail($UserId);

	if ($rowUser == '') {
		CreateResponce($objStatusCode::HTTP_UNAUTHORIZED, $isFalse, 'Invalid User');
		exit();
	}

	$SchoolId = $rowUser['schoolId'];

	//For Summative List
	$objSummative = new clsSummative();
	$objRotation = new clsRotation();
	$generateLimitString = GenerateOrderLimitString($PageNo, $recordsPerPage);

	if ($userType) {
		$getsummativedetails = $objSummative->GetAllSummativeForApp($rotationId, 0, 1, $generateLimitString, $searchText);
		$totalRecordsList = $objSummative->GetAllSummativeForApp($rotationId, 0, 1, '', $searchText);
	} else {
		$getsummativedetails = $objSummative->GetAllSummativeForApp($rotationId, $UserId, 0, $generateLimitString);
		$totalRecordsList = $objSummative->GetAllSummativeForApp($rotationId, $UserId, 0, '');
	}

	$totalSummativeCount = 0;
	if ($getsummativedetails != '') {
		$totalSummativeCount = mysqli_num_rows($getsummativedetails);
	}
	$totalRecords = ($totalRecordsList != '') ? mysqli_num_rows($totalRecordsList) : 0;

	$SummativeListArray = array();
	$pager = array();
	$pager = array(
		"PageNumber" => strval($PageNo),
		"RecordsPerPage" => strval($recordsPerPage),
		"TotalRecords" => strval($totalRecords)
	);
	$TimeZone = "UTC";

	while ($row = mysqli_fetch_assoc($getsummativedetails)) {

		$firstName = $row['firstName'];
		$lastName = $row['lastName'];
		$studentSummativeMasterId = $row['studentSummativeMasterId'];
		$evaluationDate = stripslashes($row['evaluationDate']);
		$courselocationId = $row['locationId'];
		$rotationId = $row['rotationId'];
		$rotationName = $row['title'];
		$parentRotationId = stripslashes($row['parentRotationId']);
		$rotationLocationId = stripslashes($row['rotationLocationId']);

		$evaluationDate = converFromServerTimeZone($evaluationDate, $TimeZone);
		$evaluationDate = date("Y-m-d H:i:s", strtotime($evaluationDate));

		$dateOfStudentSignature = stripslashes($row['dateOfStudentSignature']);
		if ($dateOfStudentSignature != '' && $dateOfStudentSignature != '0000-00-00 00:00:00') {
			$dateOfStudentSignature = converFromServerTimeZone($dateOfStudentSignature, $TimeZone);
			$dateOfStudentSignature = date("Y-m-d H:i:s", strtotime($dateOfStudentSignature));
		} else {
			$dateOfStudentSignature = "";
		}
		//Total Eval And Avg
		$getSelectedOptionList = $objSummative->GetSiteEvaluationSelectedOptionListBySiteEvaluationMasterId($studentSummativeMasterId);
		$totalSelectedOptionCount = $getSelectedOptionList ? mysqli_num_rows($getSelectedOptionList) : '';
		$selectedOptionText = [];
		if ($totalSelectedOptionCount) {
			while ($optionRow = mysqli_fetch_array($getSelectedOptionList)) {
				$optionText = $optionRow['optionText'];
				$optionText = (int) filter_var($optionText, FILTER_SANITIZE_NUMBER_INT);
				$selectedOptionText[] = $optionText;
			}
		}
		$evalTotal = count($selectedOptionText) ? array_sum($selectedOptionText) : 0;
		$evalAvg = $evalTotal ? $evalTotal / 24 : 0;
		$evalAvg = $evalAvg ? (number_format((float)$evalAvg, 1, '.', '')) : 0;

		//Overall Rating
		$overallQuestionId = $objSummative->GetSiteEvaluationOverallRotationEvaluationQuestionId($SchoolId);
		$overAllSelectedQuestion = $objSummative->GetSiteEvaluationOverallRotationEvaluationQuestion($studentSummativeMasterId, $overallQuestionId);
		$totalEval = $evalTotal . "/" . $evalAvg;
		$overallRating = ($overAllSelectedQuestion != '') ? $overAllSelectedQuestion : '';
		$preceptorId = $row['preceptorId'];
		$isPreceptorCompletedStatus = $row['isPreceptorCompletedStatus'];
		$PreceptorDetails = new stdClass();

		if ($preceptorId > 0) {
			$objExternalPreceptors = new clsExternalPreceptors();
			$externalPreceptorDetail = $objExternalPreceptors->GetExternalPreceptorDetail($preceptorId);
			$preceptorFirstName = isset($externalPreceptorDetail['firstName']) ? $externalPreceptorDetail['firstName'] : '';
			$preceptorLastName = isset($externalPreceptorDetail['lastName']) ? $externalPreceptorDetail['lastName'] : '';
			$preceptorNum = isset($externalPreceptorDetail['mobile_num']) ? $externalPreceptorDetail['mobile_num'] : '';
			$preceptorFullName = $preceptorFirstName . ' ' . $preceptorLastName;
			$isPreceptorStatus = $isPreceptorCompletedStatus ? $isTrue : $isFalse;

			$PreceptorDetails = array(
				"PreceptorId" => strval($preceptorId),
				"PreceptorName" => $preceptorFullName,
				"PreceptorMobile" => $preceptorNum,
				"IsPreceptorStatus" => $isPreceptorStatus
			);
		}
		// 0 = pending , 1= signoff, 2= view, 3=edit
		if ($userType) {
			$status = ($dateOfStudentSignature && !in_array($dateOfStudentSignature, ['01/01/1970', '12/31/1969'])) ? '2' : '3';
		} else {
			$status = '1';
			if ($preceptorId > 0 && $isPreceptorCompletedStatus == 0) {
				$status = '0';
			} else {
				$status = ($dateOfStudentSignature && !in_array($dateOfStudentSignature, ['01/01/1970', '12/31/1969'])) ? '2' : '1';
			}
		}

		$rotationStatus = checkRotationStatus($rotationId, $isMobile = 1);
		$isExpire = ($rotationStatus) ? $isTrue : $isFalse;

		$SummativeList = array(
			"EvaluationId" => $studentSummativeMasterId,
			"FirstName" => $firstName,
			"LastName" => $lastName,
			"EvaluationDate" => $evaluationDate,
			"DateOfStudentSignature" => $dateOfStudentSignature,
			"EvalTotal" => $totalEval,
			"OverallRating" => $overallRating,
			"Status" => $status
		);
		
		if (!$userType) {
			$SummativeList["RotationId"] = $rotationId;
			$SummativeList["RotationName"] = $rotationName;
			$SummativeList["IsExpire"] = $isExpire;
			$SummativeList["PreceptorDetails"] = $PreceptorDetails;
		}
		
		$SummativeListArray[] = $SummativeList;
		
	}
	unset($objRotation);
	unset($objLocation);
	unset($objSummative);

	CreateResponce($objStatusCode::HTTP_OK, $isTrue, 'Action successful', $SummativeListArray, $pager);
	exit;
} else {
	CreateResponce($objStatusCode::HTTP_METHOD_NOT_ALLOWED, $isFalse, 'Method not allowed');
	exit();
}
