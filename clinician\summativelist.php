<?php
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../includes/commonfun.php'); 
    include('../class/clsDB.php');     
    include('../class/clsStudent.php');
    include('../class/clsSummative.php');
    include('../class/clsRotation.php');
    include('../class/clsLocations.php');
	include('../setRequest.php');
	
    $summativerotationid = 0;
    $schoolId = 0;
	$schoolId = $currentSchoolId;
    $transchooldisplayName = '';
    $totalSummativeCount = 0;

	$TimeZone= $_SESSION["loggedClinicianSchoolTimeZone"];	
	
    if(isset($_GET['summativerotationid'])) //Edit Mode
	{
		$summativerotationid = $_GET['summativerotationid'];
        $summativerotationid = DecodeQueryData($summativerotationid);
    }
    else
    {
        $schoolId = $currentSchoolId;
        $transchooldisplayName=$currenschoolDisplayname;
    }

    $title ="Summative Evalution ".$transchooldisplayName;  

        //For All Summative List
        $objSummative=new clsSummative();
        $isclinician = 1;
        $canvasStatus = '';
		$getsummativedetails=$objSummative->GetAllSummative($summativerotationid,0,$canvasStatus,0,$isclinician);
		
		if($getsummativedetails !='')
		{
			$totalSummativeCount = mysqli_num_rows($getsummativedetails);
		}
	
        
        //For Rotation Title
		$objRotation=new clsRotation();
		$RotationName=$objRotation->GetrotationDetails($summativerotationid,$schoolId);
        $rotationtitle=$RotationName['title'];
    
    
?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
        <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
        <title><?php echo($title); ?></title>
	    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
        <?php include('includes/headercss.php');?>
        <?php include("includes/datatablecss.php") ?>

		<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />

        <style>
            .input-group-addon {
                position: absolute;
                right: 7px;
                /* width: 100%; */
                z-index: 99;
                width: 35px;
                margin: auto;
                top: 5px;
                border-left: 1px solid #ccc;
                border-radius: 50% !important;
                padding: 10px -2px;
                height: 35px;
                /* background: #01A750; */
                /* color: #fff; */
                color: #555;
                background: #f6f9f9;
                border: none;
                display: flex;
                justify-content: center;
                align-items: center;
            }

            .panel-default>.panel-heading {
                background-color: transparent !important;
            }

            .btn-success,
            .btn-default {
                padding: 8px 25px;
                border-radius: 10px;
            }

            .panel {
                border-radius: 14px !important;
            }

            .select2-container--default .select2-selection--single .select2-selection__rendered {
                line-height: 45px !important;
            }

            .required-select2 {
                border-left: solid 3px red !important;
                border-radius: 12px !important;
            }

            .select2-container--default .select2-selection--single {
                background-color: #f6f9f9 !important;
                cursor: default !important;
                height: 45px !important;
                border-radius: 10px !important;
            }

            .select2-container--default .select2-selection--single {
                border: none !important;
            }

            .panel,
            .form-group {
                margin-bottom: 10px;
            }

            .bootstrap-datetimepicker-widget {
                border-radius: 12px !important;
            }
            .form-control {
                height: 45px;
            }

            .input-group{
                width: 100%;
            }

            div.dataTables_wrapper div.dataTables_length select{
                height: 45px;
            }

            /* .form-horizontal .form-group {
                margin-right: 0;
                margin-left: 0;
            } */

            @media screen and (max-width: 500px) {
                .panel-body ol {
                    padding-left: 20px;
                }

                .panel-default>.panel-heading {
                    padding-left: 5px;
                }

                /* .container-zero{
                    padding: 0;
                } */

                div.dataTables_wrapper div.dataTables_length{
                    text-align: left !important;
                    margin-top: 10px;
                }
            }
        </style>
    </head>

    <body>
        <?php include('includes/header.php');?>

        <div class="row margin_zero breadcrumb-bg">
            <div class="container">
                <div class="pull-left">
                    <ol class="breadcrumb">
                        <li><a href="dashboard.html">Home</a></li>
                        <li><a href="rotations.html">Rotation</a></li>
                        <li class="active"><a href="rotations.html"><?php echo ($rotationtitle); ?></a></li>
                        <li class="active">Summative Evaluation</li>
                        
                    </ol>
                </div>
         
               <div class="pull-right">
                     <a class="btn btn-link" href="summative.html?summativerotationid=<?php echo EncodeQueryData($summativerotationid); ?>">Add</a>
               </div>
         

            </div>
        </div>

        <div class="container">

            <?php
				if (isset($_GET["status"]))
				{
					if($_GET["status"] =="added")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Summative Evaluation added successfully.
                </div>
                <?php 
					}
                    else if($_GET["status"] =="updated")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Summative Evaluation updated successfully.
                </div>
                <?php 
					}
                    else if($_GET["status"] =="Deleted")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Summative Evaluation deleted successfully.
                </div>
                <?php 
					}
                     else if($_GET["status"] =="Error")
					{
						?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Error occurred.
                </div>
                <?php 
					}

				}
              ?>
			
			
                <div id="divTopLoading" >Loading...</div>
                <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                    <thead>
                        <tr>
                            <th>First Name</th>
                            <th>Last Name</th>                            
                            <th style="text-align: center">Evaluation <br> Date</th>
                            <th style="text-align: center">Signature <br> Date</th>
                            <th style="text-align: center">Eval Total/Avg</th>
                            <th style="text-align: center">Overall Rating</th>
                            <th style="text-align: center">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        if($totalSummativeCount > 0)
                        {
                            while($row = mysqli_fetch_array($getsummativedetails))
                            {
								
								$title = $row['title'];
								$firstName = $row['firstName'];
								$lastName = $row['lastName'];
								$studentName = $firstName . ' ' . $lastName;
								$studentSummativeMasterId = $row['studentSummativeMasterId'];
								
								$getSelectedOptionList=$objSummative->GetSiteEvaluationSelectedOptionListBySiteEvaluationMasterId($studentSummativeMasterId);
                        		$totalSelectedOptionCount = $getSelectedOptionList ? mysqli_num_rows($getSelectedOptionList) : '';
                        		$selectedOptionText = [];
                        		if($totalSelectedOptionCount)
                        		{
                        		    while($optionRow = mysqli_fetch_array($getSelectedOptionList))
                                    {
                                        $optionText = $optionRow['optionText'];
                                        $optionText = (int) filter_var($optionText, FILTER_SANITIZE_NUMBER_INT);
                                        $selectedOptionText [] = $optionText;
                                    }
                        		}
                        		$evalTotal = count($selectedOptionText) ? array_sum($selectedOptionText) : 0;
                        		$evalAvg = $evalTotal ? $evalTotal/24 : 0;
                                $evalAvg = $evalAvg ? (number_format((float)$evalAvg, 1, '.', '')) : 0;

                        		$overallQuestionId=$objSummative->GetSiteEvaluationOverallRotationEvaluationQuestionId($schoolId);
                        	    $overAllSelectedQuestion = $objSummative->GetSiteEvaluationOverallRotationEvaluationQuestion($studentSummativeMasterId,$overallQuestionId); 
                        		
                                $evaluationDate = stripslashes($row['evaluationDate']);	
								$courselocationId = $row['locationId'];
								$rotationId = $row['rotationId'];
						        $parentRotationId = stripslashes($row['parentRotationId']);
							    $rotationLocationId = stripslashes($row['rotationLocationId']);
							    $locationId = 0;
							
								if($rotationLocationId != $courselocationId && $parentRotationId > 0)
                                {
                                    if(!$rotationLocationId)
                                        $locationId = $objRotation->GetLocationByRotation($rotationId);
                                    else
                                        $locationId  = $rotationLocationId;
                                }
                                else
                                {	
                                    $locationId  = $courselocationId;
                                }
										
									//Get Time Zone By Rotation 
									$objLocation = new clsLocations();
									$TimeZone = $objLocation->GetTimeZoneByLocation($locationId);
									unset($objLocation);
									if($TimeZone == '')
										$TimeZone = $_SESSION["loggedClinicianSchoolTimeZone"];

                                $evaluationDate = converFromServerTimeZone($evaluationDate,$TimeZone);
                                $evaluationDate =date("m/d/Y",strtotime($evaluationDate));
                                $dateOfStudentSignature = stripslashes($row['dateOfStudentSignature']);
                                $dateOfStudentSignature = converFromServerTimeZone($dateOfStudentSignature,$TimeZone);
                                $dateOfStudentSignature =date("m/d/Y",strtotime($dateOfStudentSignature));
                                if($dateOfStudentSignature != '' && $dateOfStudentSignature != '0000-00-00' && $dateOfStudentSignature != '01/01/1970' && $dateOfStudentSignature != '12/31/1969')
                                {
                                    $dateOfStudentSignature = (date("m/d/Y",strtotime($dateOfStudentSignature)));
                                    $isActionName = 'View';
                                } 
                                else 
                                {
                                    $dateOfStudentSignature = "-";
                                    $isActionName = 'Edit';
                                }

                                $totalUserCount = 0;
                                ?>
                            <tr>
                                <td><?php echo($firstName); ?></td>
                                <td><?php echo($lastName); ?></td>                               
                                <td style="text-align: center">
                                   <?php 
								   if($evaluationDate != '' && $evaluationDate != '0000-00-00' && $evaluationDate != '01/01/1970'){
										
									echo (date("m/d/Y",strtotime($evaluationDate)));
								   } else {
											 echo '-';
									 } ?>
                                </td>
								
								<td style="text-align: center">
                                   <?php echo $dateOfStudentSignature; ?>
                                </td>
                                <td style="text-align: center"><?php echo $evalTotal." | ".$evalAvg; ?></td>
                                <td style="text-align: center"><?php echo $overAllSelectedQuestion; ?></td>
                                                                
                                <td style="text-align: center">
                                    <a href="summative.html?studentSummativeMasterId=<?php echo(EncodeQueryData($studentSummativeMasterId)); ?>
									&summativerotationid=<?php echo(EncodeQueryData($summativerotationid));?>"><?php echo $isActionName; ?></a></td>
                            </tr>
                            <?php
                            }
                        }
					 unset($objRotation);
					 	unset($objSummative);
                    ?>
                    </tbody>
                </table>
        </div>

        <?php include('includes/footer.php');?>
        <?php include("includes/datatablejs.php") ?>
        <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>

        <script type="text/javascript">

        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "summ-control";


            $(window).load(function(){
                $("#divTopLoading").addClass('hide');
            });
			
            var current_datatable = $("#datatable-responsive").DataTable({
                "ordering": true,
                "order": [[2, "desc" ]],

                "aoColumns": [{"sWidth": "15%"}
                ,{"sWidth": "15%"}
                ,{"sWidth": "25%","sClass": "alignCenter",}
                ,{"sWidth": "25%","sClass": "alignCenter",}
                ,{"sWidth": "25%","sClass": "alignCenter",}
                ,{"sWidth": "25%","sClass": "alignCenter"}
                ,{ "sWidth": "20%","sClass": "alignCenter","bSortable": false}
                ]
            });

            
        </script>


    </body>

    </html>