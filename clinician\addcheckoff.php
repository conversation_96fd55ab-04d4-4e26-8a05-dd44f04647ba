<?php
$IsMobile = isset($_GET['IsMobile']) ? ($_GET['IsMobile']) : 0;

// ini_set('display_errors', 1);
// ini_set('display_startup_errors', 1);
// error_reporting(E_ALL);

include('../includes/config.php');
include('includes/validateUserLogin.php');
include('../class/clsDB.php');
include('../class/clsSchool.php');
include('../class/clsStudent.php');
include('../class/clscheckoff.php');
include('../class/clsRotation.php');
include('../class/clsLocations.php');
include('../class/clsClinician.php');
include('../includes/commonfun.php');
include('../class/clsQuestionOption.php');
include('../class/clsHospitalSite.php');
include('../setRequest.php');

$objSchool  = new clsSchool();
$isActiveCheckoff = '';
$isActiveCheckoff = $_SESSION["isActiveCheckoff"];
$loggedClinicianType = '';
$loggedClinicianType = $_SESSION['loggedClinicianType'];
$displaySchoolName = $objSchool->GetSchoolName($currentSchoolId);
unset($objSchool);
$schoolSectionId = 0;
$checkoffId = 0;
$startdatetime = 0;
$selTopicId = 0;
$cbostudents = 0;
$rotationId = 0;
$totalrowscheckoff = 0;
$studentId = 0;
$sellocationId = 0;
$clinicianId = '';
$student = '';
$studentComment  = '';
$student_evaluationDate  = null;
$display_to_date = date('m/d/Y');
$evaluationDate = $display_to_date;
$TimeZone = '';
$filterStudentId = 0;
$totalquistion = 0;
$view = '';
$hospitalSiteId = 0;
$topicIds = array();
$isMultipleCheckoff = 0;
$TimeZone = $_SESSION["loggedClinicianSchoolTimeZone"];
if (isset($_GET['rotationId'])) {
	$rotationId = DecodeQueryData($_GET['rotationId']);
}
if (isset($_GET['view'])) {
	$view = ($_GET['view']);
}
$objClinician = new clsClinician();
$objRotation = new clsRotation();
$objStudent = new clsStudent();

if (isset($_GET['studentId'])) {
	$filterStudentId = DecodeQueryData($_GET['studentId']);
}

if (isset($_GET['schoolTopicId'])) {
	$schoolTopicId = $_GET['schoolTopicId'];
	$selTopicId = ($schoolTopicId);
}

if ($IsMobile) {
	// echo "is mobile";
	if ($_SERVER['REQUEST_METHOD'] == "GET") {
		$selTopicId = 0;
		$startdatetime  = isset($_GET['startdatetime']) ? $_GET['startdatetime'] : '';
		$clinicianId  = isset($_GET['UserId']) ? DecodeQueryData($_GET['UserId']) : 0;
		$Clinician = $objClinician->GetClinicianDetails($clinicianId);
		$firstname = isset($Clinician['firstName']) ? $Clinician['firstName'] : '';
		$lastname = isset($Clinician['lastName']) ? $Clinician['lastName'] : '';
		$clinicianFullname = $firstname . ' ' . $lastname;

		$selTopicId  = isset($_GET['schoolTopicId']) ? DecodeQueryData($_GET['schoolTopicId']) : ($_GET['alltopic']);

		$studentIds = isset($_GET['studentId']) ?  $_GET['studentId'] : 0;
		$studentIdArray = explode(",", $studentIds);

		// $studentIdArray = isset($_GET['studentId']) ? (is_array($_GET['studentId']) ? $_GET['studentId'] : array($_GET['studentId'])) : array();

		// echo "</br>";
		// print_r($studentIdArray);exit;

		$Clinician = $objClinician->GetClinicianByRotation($currentSchoolId, $rotationId);

		$RotationName = $objRotation->GetrotationDetails($rotationId, $currentSchoolId);
		$rotationtitle = isset($RotationName['title']) ? $RotationName['title'] : '';

		$studentFullNameArray = array();
		if (count($studentIdArray)) {
			foreach ($studentIdArray as &$studentId) {
				if (is_string($studentId)) {
					$studentId = DecodeQueryData($studentId);
				}
				$Rowstudent = $objStudent->GetSingleStudent($currentSchoolId, $studentId);
				$studentFullNameArray[] = $Rowstudent ? $Rowstudent['firstName'] . ' ' . $Rowstudent['lastName'] : '';
			}
			// unset($studentId);
		}
		$studentFullName = implode(', ', $studentFullNameArray);
	}
}



// exit;
// print_r($_POST);
if ($_SERVER['REQUEST_METHOD'] == "POST") {
	$selTopicId = 0;
	$startdatetime  = ($_POST['startdatetime']);
	$clinicianId  = $_SESSION["loggedClinicianId"];
	$Clinician = $objClinician->GetClinicianDetails($clinicianId);
	$firstname = isset($Clinician['firstName']) ? $Clinician['firstName'] : '';
	$lastname = isset($Clinician['lastName']) ? $Clinician['lastName'] : '';
	$clinicianFullname = $firstname . ' ' . $lastname;


	// $selTopicId  = isset($_POST['cbotopic']) ? ($_POST['cbotopic']) : ($_POST['alltopic']);
	$isMultipleCheckoff = isset($_POST['isMultipleCheckoff']) ? $_POST['isMultipleCheckoff'] : 0;

	$cbotopic = isset($_POST['cbotopic']) ? $_POST['cbotopic'] : '';
	$alltopic = isset($_POST['alltopic']) ? $_POST['alltopic'] : '';

	if ($isMultipleCheckoff) {
		$topicIds  = (isset($_POST['cbotopic']) && $_POST['cbotopic'] != '') ? ($_POST['cbotopic']) : ($_POST['alltopic']);
		$selTopicId = $topicIds[0];
	} else {
		$selTopicId  = isset($_POST['cbotopic']) ? ($_POST['cbotopic']) : ($_POST['alltopic']);
		$topicIds[] = $selTopicId;
	}

	$rotationId  = DecodeQueryData($_POST['cborotation']);
	//$studentId  = ($_POST['cboStudent']);

	$studentIdArray = $_POST['cboStudent'];
	// print_r($studentIdArray);exit;

	$Clinician = $objClinician->GetClinicianByRotation($currentSchoolId, $rotationId);

	$RotationName = $objRotation->GetrotationDetails($rotationId, $currentSchoolId);
	$rotationtitle = isset($RotationName['title']) ? $RotationName['title'] : '';

	$studentFullNameArray = array();
	if (count($studentIdArray)) {
		foreach ($studentIdArray as $key => $studentId) {
			$Rowstudent = $objStudent->GetSingleStudent($currentSchoolId, $studentId);
			$studentFullNameArray[] = $Rowstudent ? $Rowstudent['firstName'] . ' ' . $Rowstudent['lastName'] : '';
		}
	}
	$studentFullName = implode(', ', $studentFullNameArray);
}
$objcheckoff = new clscheckoff();



if (isset($_GET['checkoffId'])) {

	$checkoffId = $_GET['checkoffId'];
	$checkoffId = DecodeQueryData($checkoffId);
	$title = "Edit checkoff";
	$bedCrumTitle = 'Edit';
	$row = $objcheckoff->GetCheckOff($checkoffId);

	$studentId  = stripslashes($row['studentId']);
	$selTopicId  = stripslashes($row['schoolTopicId']);
	$rotationId  = stripslashes($row['rotationId']);
	$startdatetime  = stripslashes($row['checkoffDateTime']);
	$clinicianId  = stripslashes($row['clinicianId']);
	$Clinician = $objClinician->GetClinicianDetails($clinicianId);
	$firstname = isset($Clinician['firstName']) ? $Clinician['firstName'] : '';
	$lastname = isset($Clinician['lastName']) ? $Clinician['lastName'] : '';
	$clinicianFullname = $firstname . ' ' . $lastname;

	$studentComment  = stripslashes($row['studentComment']);
	$studentComment  = strip_tags($row['studentComment']);
	$student  = stripslashes($row['student']);
	$evaluationDate  = stripslashes($row['evaluationDate']);
	$courselocationId = $row['locationId'];
	$parentRotationId = stripslashes($row['parentRotationId']);
	$rotationLocationId = stripslashes($row['rotationLocationId']);
	$locationId = 0;

	if ($rotationLocationId != $courselocationId && $parentRotationId > 0) {
		if (!$rotationLocationId)
			$locationId = $objRotation->GetLocationByRotation($rotationId);
		else
			$locationId  = $rotationLocationId;
	} else {
		$locationId  = $courselocationId;
	}

	//Get Time Zone By Rotation 
	$objLocation = new clsLocations();
	$TimeZone = $objLocation->GetTimeZoneByLocation($locationId);
	unset($objLocation);
	if ($TimeZone == '')
		$TimeZone = $_SESSION["loggedClinicianSchoolTimeZone"];
	if ($evaluationDate != '' && $evaluationDate != '0000-00-00 00:00:00' && $evaluationDate != '01/01/1970'  && $evaluationDate != '11/29/-0001 10:00 PM') {
		$evaluationDate = converFromServerTimeZone($evaluationDate, $TimeZone);
		$evaluationDate = date('m/d/Y', strtotime($evaluationDate));
	} else {
		$evaluationDate = '';
	}
	$student_evaluationDate  = stripslashes($row['student_evaluationDate']);

	if ($student_evaluationDate != '' && $student_evaluationDate != '0000-00-00 00:00:00') {
		$student_evaluationDate = converFromServerTimeZone($student_evaluationDate, $TimeZone);
		$student_evaluationDate = date('m/d/Y', strtotime($student_evaluationDate));
	} else {
		$student_evaluationDate = null;
	}

	$Clinician = $objClinician->GetClinicianByRotation($currentSchoolId, $rotationId);
	$RotationName = $objRotation->GetrotationDetails($rotationId, $currentSchoolId);
	$rotationtitle = isset($RotationName['title']) ?  $RotationName['title'] : '';
	$hospitalTitle = isset($RotationName['hospitalSite']) ? $RotationName['hospitalSite'] : '';
	$rotationtitle = $rotationtitle . ' ( ' . $hospitalTitle . ' )';
	$Rowstudent = $objStudent->GetSingleStudent($currentSchoolId, $studentId);
	$studentFullName = $Rowstudent ? $Rowstudent['firstName'] . ' ' . $Rowstudent['lastName'] : '';
	$rowscheckoff = $objcheckoff->GetDefaulttopicDetails($selTopicId);
	if ($rowscheckoff != '') {
		$totalrowscheckoff = mysqli_num_rows($rowscheckoff);
	}
} else {
	$title = "Add checkoff";
	$bedCrumTitle = 'Add';
	$rowscheckoff = $objcheckoff->GetDefaulttopicDetails($selTopicId);
	if ($rowscheckoff != '') {
		$totalrowscheckoff = mysqli_num_rows($rowscheckoff);
	}
}
//Get Topic Detail
// $rowsTopicDetails = $objcheckoff->GetTopicDetails($selTopicId);
// $instructions=$rowsTopicDetails['instructions'];

// Get checkoffTitleId
$objDB = new clsDB();
$checkoffTitleId = $objDB->GetSingleColumnValueFromTable('schooltopicmaster', 'checkoffTitleId', 'schoolTopicId', $selTopicId);
unset($objDB);

$checkoffTitleId = explode(".", $checkoffTitleId);
$checkoffTitleName = $checkoffTitleId[0];

$bedCrumTitle = (isset($_GET['view']) && $_GET['view'] == 'V') ? 'View' : $bedCrumTitle;

$rotation = $objRotation->GetRotationBySchool($currentSchoolId);
$objHospitalSite = new clsHospitalSite();

$hospitalSite = $objHospitalSite->GetHospitalSiteByRotation($rotationId);
$hospitalSiteTitle = $hospitalSite['title'];
unset($objRotation);

$topicIds = (empty($topicIds)) ? array($selTopicId) : $topicIds;
$topicIdsCount = count($topicIds);
// print_r($topicIds);
?>
<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
	<!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
	<title><?php echo ($title); ?></title>
	<?php include('includes/headercss.php'); ?>
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
	<?php include("includes/datatablecss.php") ?>

	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">

	<style>
		.some-class {
			float: left;
			clear: none;
		}

		.loader {
			border: 5px solid #B5B0AF;
			border-radius: 50%;
			border-top: 5px solid #3498db;
			width: 20px;
			height: 20px;
			-webkit-animation: spin 2s linear infinite;
			/* Safari */
			animation: spin 2s linear infinite;
		}

		.dt-responsive {
			width: 100%;
		}

		/* Safari */
		@-webkit-keyframes spin {
			0% {
				-webkit-transform: rotate(0deg);
			}

			100% {
				-webkit-transform: rotate(360deg);
			}
		}

		@keyframes spin {
			0% {
				transform: rotate(0deg);
			}

			100% {
				transform: rotate(360deg);
			}
		}

		body {
			font-weight: 400;
		}

		.disable_div {
			pointer-events: none;
		}

		.some-class {
			float: left;
			clear: none;
		}

		.checkoff-table-font {
			font-size: 14px;
		}

		.mydiv {
			padding: 15px 0;
		}

		.form-control {
			height: 45px;
		}

		/* Style for the collapsible content */
		.panel-collapse {
			display: none;
			/* Hidden by default */
			/* padding: 10px;
            border: 1px solid #ddd;
            background-color: #f9f9f9; */
		}


		/* Style for the collapsible button */
		.collapsible {
			/* background-color: #3498db;
            color: white;
            padding: 10px;
            border: none; */
			width: 100%;
			cursor: pointer;
			display: flex;
			justify-content: space-between;
			/* Align content horizontally */
		}

		.panel-heading {
			width: 100%;
		}

		/* Style for the arrow icons */
		.arrow-icon {
			transition: transform 0.3s;
		}

		.collapsible.expanded .arrow-icon i {
			transform: rotate(180deg);
		}

		.select2-container {
			box-sizing: border-box;
			display: inline-block;
			margin: 0;
			position: relative;
			vertical-align: middle;
			width: 100% !important;
		}

		@media screen and (max-width: 500px) {

			.checkoff-table-font {
				font-size: 12px;
			}

			.panel-body {
				padding: 10px 5px;
			}

			.panel-body b {
				font-size: 15px;
				font-weight: 600;
			}

		}

		@media screen and (max-width: 992px) {
			.margin_bottom_thirty {
				margin-bottom: 0;
			}

			.mobile-px-0 {
				padding-left: 0 !important;
				padding-right: 0 !important;
			}
		}

		@media screen and (max-width: 767px) {
			.completion-date-section {
				padding-left: 0;
				display: block;
				margin-bottom: 20px !important;
			}

			.bottom {
				margin-bottom: 20px !important;
			}
		}
	</style>
</head>

<body>
	<?php if ($IsMobile == 0) { ?>
		<?php include('includes/header.php'); ?>
		<div class="row margin_zero breadcrumb-bg">
			<div class="container">
				<div class="pull-left">
					<ol class="breadcrumb">
						<li><a href="dashboard.html">Home</a></li>
						<?php if ($isActiveCheckoff == 1) {
							if ($studentId > 0) { ?>
								<li class="active"><a href="studentCheckoffList.html">Student List</a></li>
								<li><a href="checkoff.html?studentId=<?php echo EncodeQueryData($studentId); ?>&Type=C">Checkoff</a></li>
							<?php } else { ?>
								<li><a href="checkoff.html?rotationId=<?php echo EncodeQueryData($rotationId); ?>&Type=R">Checkoff</a></li>
							<?php }
						} else {
							if ($studentId > 0) { ?>
								<li class="active"><a href="studentCheckoffList.html">Student List</a></li>
								<li><a href="checkoffs.html?studentId=<?php echo EncodeQueryData($studentId); ?>&Type=C">Checkoff</a></li>
							<?php } else { ?>
								<li><a href="checkoffs.html?rotationId=<?php echo EncodeQueryData($rotationId); ?>&Type=R">Checkoff</a></li>
						<?php }
						} ?>
						<li class="active"><?php echo ($bedCrumTitle); ?></a></li>

					</ol>
				</div>
			</div>
		</div>
	<?php  }   ?>

	<div class="container">

		<!--div class="formSubHeading"><?php //echo($displaySchoolName); 
										?></div-->

		<form name='frmCheckoff' autocomplete="off" id='frmCheckoff' data-parsley-validate method='POST' <?php if ($isActiveCheckoff == 1) { ?> action='addcheckoffsubmit.html?checkoffId=<?php echo (EncodeQueryData($checkoffId)); ?>&studentId=<?php echo EncodeQueryData($studentId); ?>&rotationId=<?php echo EncodeQueryData($rotationId); ?>' <?php } else { ?>action='addcheckoffssubmit.html?checkoffId=<?php echo (EncodeQueryData($checkoffId)); ?>&studentId=<?php echo EncodeQueryData($studentId); ?>&rotationId=<?php echo EncodeQueryData($rotationId); ?>' <?php } ?>>
			<input type="hidden" name="IsMobile" id="IsMobile" value="<?php echo $IsMobile; ?>">
			<div class="row">
				<div class="col-md-6 p-0">
					<!-- lab and clinical -->
					<input type="hidden" name="hiddenIsClinical" id="hiddenIsClinical" value="">
					<input type="hidden" name="hiddenIsLab" id="hiddenIsLab" value="">
					<input type="hidden" name="isMultipleCheckoff" id="isMultipleCheckoff" value="<?php echo $isMultipleCheckoff; ?>">
					<input type="hidden" name="topicIds" id="topicIds" value="<?php echo implode(',', $topicIds); ?>">
					<!-- <div class="form-group">
						<label class="col-md-12 control-label" for="studentname">Rotation</label>
						<div class="col-md-12 col-sm-12 col-xs-12 mb-10">
							<input type='text' readonly name="Rotationname" id="Rotationname" class="form-control input-md required-input " value="<?php echo ($rotationtitle); ?>" required />
							<div id="error-txtDate"></div>
						</div>
					</div> -->
					<div class="form-group">
						<label class="col-md-12 control-label" for="cborotation">Rotation</label>
						<div class="col-md-12 col-sm-12 col-xs-12 mb-10">

							<select id="cborotation" name="cborotation" class="form-control input-md required-input select2_single border-12" required disabled>
								<option value="" selected>Select</option>
								<?php
								if ($rotation != "") {
									while ($row = mysqli_fetch_assoc($rotation)) {
										$selrotationId  = $row['rotationId'];
										$name  = stripslashes($row['title']);

								?>
										<option value="<?php echo ($selrotationId); ?>" <?php if ($rotationId == $selrotationId) { ?> selected="true" <?php } ?>>
											<?php echo ($name); ?>
										</option>
								<?php

									}
								}
								?>
							</select>
						</div>
					</div>
					<!--- ROTATION DD END----->
				</div>
				<div class="col-md-6 p-0">
					<div class="form-group">
						<label class="col-md-12 control-label" for="cbohospitalsites">Hospital Sites</label>
						<div class="col-md-12 col-sm-12 col-xs-12 mb-10">
							<input type='text' name="cbohospitalsites" id="cbohospitalsites" class="form-control input-md required-input required" readonly value="<?php echo ($hospitalSiteTitle); ?>" />

							<!-- <select id="cbohospitalsites" name="cbohospitalsites" class="form-control input-md required-input select2_single border-10" required disabled>

								<option value=""> </option>
							</select>
							<p id="hospitalerror" style="color:#E74C3C; display: none; border: 1px solid #E8544;"></p> -->
						</div>
					</div>
				</div>

			</div>
			<!-- <br> -->
			<div class="row">
				<div class="col-md-6 p-0">
					<div class="form-group">
						<label class="col-md-12 control-label" for="studentsigniture">Clinician</label>
						<div class="col-md-12 col-sm-12 col-xs-12 mb-10">
							<input type='text' readonly name="" id="" title="<?php echo ($clinicianFullname); ?>" class="form-control input-md" value="<?php echo ($clinicianFullname); ?>" />
							<div id="error-txtDate"></div>
						</div>
					</div>
				</div>

				<div class="col-md-6 p-0">
					<div class="form-group">
						<label class="col-md-12 control-label" for="evaluationDate">Date of Clinician Signature</label>
						<div class="col-md-12 col-sm-12 col-xs-12 mb-10">
							<div class='input-group date w-full' id='evaluationDates' style="position: relative;">

								<input type='text' name="evaluationDate" id="evaluationDate" class="form-control input-md required-input rotation_date dateInputFormat evaluationDate" value="<?php if ($evaluationDate != '12/31/1969' && $evaluationDate != '01/01/1970') {
																																																	echo ($evaluationDate);
																																																} ?>" required data-parsley-errors-container="#error-evaluationDate" placeholder="MM-DD-YYYY" />
								<span class="input-group-addon calender-icon">
									<span class="glyphicon glyphicon-calendar"></span>
								</span>
							</div>
							<div id="error-evaluationDate"></div>
						</div>
					</div>
				</div>
			</div>
			<!-- <br> -->
			<div class="row">
				<div class="col-md-6 p-0">
					<div class="form-group">
						<label class="col-md-12 control-label" for="studentsigniture">Student</label>
						<div class="col-md-12 col-sm-12 col-xs-12 mb-10">
							<input type='text' readonly name="studentsigniture" id="studentsigniture" title="<?php echo ($studentFullName); ?>" class="form-control input-md required-input " value="<?php echo ($studentFullName); ?>" required />
							<div id="error-txtDate"></div>
						</div>
					</div>
				</div>

				<div class="col-md-6 p-0">
					<div class="form-group">
						<label class="col-md-12 control-label" for="studentsignitureDate">Date of Student Signature</label>
						<div class="col-md-12 col-sm-12 col-xs-12 mb-10">
							<div class='input-group date w-full' id='studentsignitureDate' style="position: relative;">

								<input readonly type='text' name="studentsignitureDate" id="studentsignitureDate" class="form-control input-md required-input rotation_date dateInputFormat" value="<?php echo ($student_evaluationDate);  ?>" data-parsley-errors-container="#error-txtDate" placeholder="MM-DD-YYYY" />
								<span class="input-group-addon calender-icon">
									<span class="glyphicon glyphicon-calendar"></span>
								</span>
							</div>
							<div id="error-studentsigniture"></div>
						</div>
					</div>
				</div>
			</div>
			<!-- <br> -->
			<div class="row">
				<div class="col-md-12 p-0">
					<div class="form-group">
						<label class="col-md-12 control-label" for="studentcomment">Student Comments</label>
						<div class="col-md-12 col-sm-12 col-xs-12">
							<textarea readonly name="studentcomment" id="studentcomment" class="form-control input-md " rows="4" cols="100"><?php echo ($studentComment); ?></textarea>
						</div>
					</div>
				</div>
			</div>
			<br />
			<!-- <div class="col-md-12"> -->
			<div class="panel-group" id="posts">
				<?php
				$sectionIdArray = array();

				if ($totalrowscheckoff > 0) {
					// for section
					$sectionNo = 1;
					while ($row = mysqli_fetch_array($rowscheckoff)) {
						$schoolSectionId = $row['schoolSectionId'];
						$schoolSectionTitle = $row['schoolSectionTitle'];
						$sections = explode("-", $schoolSectionTitle);
						if (trim($sections[0]) == 'Clinical Instructor Checkoff')
							$sectionIdArray[] = $schoolSectionId;
						$description = $row['description'];
						$sortOrder = $row['sortOrder'];
						$sectionDescription = isset($row['description']) ? $row['description'] : '';

						$mycls = 'required-input required';
						$style = 'required="true"';

				?>
						<div class="panel panel-default" sectionNo="<?php echo $sectionNo; ?>">
							<a class="collapsible" style="color: #000000; text-decoration: none;" href="#<?php echo $schoolSectionId; ?>" data-toggle="collapse" data-parent="#posts" sortOrder="<?php echo $sortOrder; ?>" schoolSectionId="<?php echo $schoolSectionId; ?>" viewOnly="<?php echo $view; ?>">
								<div class="panel-heading" style="display:flex; justify-content: space-between;align-items:center">
									<h4 class="panel-title">
										<?php echo $schoolSectionTitle; ?>
										<!-- <div class="glyphicon glyphicon-plus pull-right margin_left_ten"></div> <span id="divTopLoading_<?php echo $schoolSectionId; ?>" class="loader pull-right hide"></span> -->

									</h4>
									<span class="arrow-icon"> <i class="fa fa-angle-down" aria-hidden="true" id="collapse-icon" style="font-size: 19px;"></i></span>
								</div>
							</a>

							<div id="<?php echo $schoolSectionId; ?>" class="panel-collapse collapse">
								<?php if ($sectionDescription != '') { ?>
									<div class='panel-body'>
										<span><?php echo $sectionDescription; ?></span>
									</div>
									<!-- <div class="rendorHtml_<?php echo $schoolSectionId; ?>">
									Here Merge All Questions  
								</div> -->
									<?php }
								$rowsquistion = $objcheckoff->Getschooldefaultquestionmaster($schoolSectionId, $currentSchoolId);
								if ($rowsquistion != '') {
									$totalquistion = mysqli_num_rows($rowsquistion);
								}

								$rendorHtml = '';
								if ($totalquistion > 0) {
									$tableHtmlSec1 = '';
									$isPartClass = '';
									$isPartCount = 0;
									while ($row = mysqli_fetch_array($rowsquistion)) {

										$schoolQuestionTitle = $row['schoolQuestionTitle'];
										$schoolQuestionId = $row['schoolQuestionId'];
										$schoolQuestionType = $row['schoolQuestionType'];
										$questionSortOrder = $row['sortOrder'];
										$proceduralSteps = $row['proceduralSteps'];

										//For Display vertical to horizontal Options
										$isLastSection = ($sortOrder == 5) ? 'isLastSection' : '';

										if ($isActiveCheckoff != 1 && $sortOrder == 1) {
											if ($questionSortOrder || $proceduralSteps || $schoolQuestionTitle) {
									?><table class="table-bordered dt-responsive nowrap table-hover">
													<tr>
														<td style="width:15%;padding-left: 10px;"><b><?php echo $questionSortOrder; ?></b></td>
														<td style="width:30%;padding-left: 10px;"><?php echo $proceduralSteps; ?></td>
														<td style="width:45%;padding-left: 10px;"><?php echo $schoolQuestionTitle; ?></td>
													</tr>
												</table> <?php
														}
													} else {

														if (strpos($schoolQuestionTitle, 'Part') !== false) {
															$isPartCount++;
															$isPartClass = 'isPart_' . $isPartCount;
														}

														//For Mansfield University
														if ($currentSchoolId == '127' && $schoolQuestionType == 9) {
															$objDB = new clsDB();
															$commentAnswer = $objDB->GetSingleColumnValueFromTable('checkoffdetail', 'comments', 'checkoffId', $checkoffId, 'schoolQuestionDId', $schoolQuestionId);
															unset($objDB);
														}
														$qhtml = GetCheckoffQuestionHtml($schoolQuestionId, $schoolQuestionType, $checkoffId, $currentSchoolId, $isActiveCheckoff, $view, '', $schoolSectionId);

														//For Clinicial Date And dropdown in one line
														if ($schoolQuestionTitle == 'Clinical Instructor:' || $schoolQuestionTitle == 'Lab Instructor:') { ?>
												<div class="row">
													<div class="">
														<div class="col-md-6 bottom">
															<b class="col-md-12 "> <?php echo $schoolQuestionTitle; ?></b>
															<div class="">
																<?php echo $qhtml; ?>
															</div>
														</div>


													<?php } else if ($schoolQuestionTitle == 'Lab Date:' || $schoolQuestionTitle == 'CI Date:') { ?>
														<div class="col-md-6">
															<b class="col-md-12"> <?php echo $schoolQuestionTitle; ?></b>
															<?php echo $qhtml; ?>
														</div>
													</div>
												</div>
												<br>

											<?php } else { ?>
												<div <?php if ($loggedClinicianType != 'P' && $sortOrder != 4 || $sortOrder != 3 || $sortOrder != 5) { ?> id="mydiv" <?php } ?> class="panel-body isAllRadioButton <?php echo $isPartClass; ?> <?php echo $isLastSection; ?>" style="border-top: 1px solid #ddd;">
													<b class="questionDiv"> <?php echo $schoolQuestionTitle; ?></b><br /><br />
													<?php echo $qhtml; ?>
													<?php if ($currentSchoolId == '127' && $schoolQuestionType == 9) { ?>
														<textarea name='mansfield_comment_question_<?php echo $schoolQuestionId; ?>' id="" class="form-control input-md clstextarea" rows="3"><?php echo $commentAnswer; ?></textarea>
													<?php } ?>
												</div>
									<?php
														}
													}
												}
									?>
									<input type="hidden" name="" id="isParts" value="<?php echo $isPartCount; ?>">
								<?php
								} ?>
								<input type="hidden" name="sectionIdCollapse" id="sectionIdCollapse_<?php echo $schoolSectionId; ?>" value="">
							</div>
						</div>
				<?php
						$sectionNo++;
					}
				}
				?>
				<!-- </div> -->
			</div>
			<?php if ($currentSchoolId == 127) { ?>
				<div class="row partWiseScoreDiv"></div>
				<div class="row margin_bottom_thirty">
					<div class="col-md-12">
						<div class="form-group">
							<div style="display: flex;">
								<div style="margin-right: 20px;">
									<label class="control-label margin_top_five px-0">Total Score:</label>
									<div class="padding_zero">
										<input type="text" name="score" id="score" class="form-control mb-5" readonly>
									</div>
								</div>
								<div>
									<label class=" control-label margin_top_five mobile-px-0">Total Possible:</label>
									<div class="padding_zero">
										<input type="text" name="totalPossible" id="totalPossible" class="form-control mb-5" readonly>
									</div>
								</div>
							</div>
							<!-- // Score percentage -->
							<?php if ($checkoffTitleName == 'MUL') { ?>
								<div style="display: flex;">
									<div style="margin-right: 20px;">
										<label class="control-label margin_top_five mobile-px-0">Score Percentage:</label>
										<div class="padding_zero">
											<input type="text" name="scorePercentage" id="scorePercentage" class="form-control mb-5" readonly>
										</div>
									</div>
								</div>
							<?php } ?>

						</div>
					</div>
				</div>
			<?php } ?>
			<input type="hidden" name="rotationId" id="rotationId" value="<?php echo ($rotationId) ?>">
			<input type="hidden" name="sectionIds[]" id="sectionIds">
			<!--input type="hidden"	name="cbostudentshide" id="cbostudentshide" value="<?php //echo ($studentId)
																							?>"--->
			<input type="hidden" name="selTopicId" id="selTopicId" value="<?php echo ($selTopicId) ?>">
			<input type="hidden" name="startdatetime" id="startdatetime" value="<?php echo ($startdatetime) ?>">
			<div class="col-md-12" style="display: flex; justify-content: center; margin: 10px 0;">
				<?php if (isset($_GET['checkoffId'])) { ?>
					<input type="hidden" name="multiplestudentids" id="multiplestudentids" value="<?php echo ($studentId) ?>">
				<?php } else { ?>
					<input type='hidden' name="multiplestudentids" id="multiplestudentids" value="<?php if ($IsMobile) {
																										echo  implode(',', $studentIdArray);
																									} else {
																										echo implode(',', $_POST['cboStudent']);
																									} ?>" />
				<?php } ?>
				<input type="hidden" name="clinicianId" id="clinicianId" value="<?php echo ($clinicianId) ?>">
				<?php if ($isActiveCheckoff == 1) {
					if ($view != 'V') { ?>
						<button style="margin-right: 10px;" type="hidden" name="btnSubmit" id="btnSubmit" class="btn btn-success">Submit</button>
					<?php }
					if ($IsMobile) { ?>
						<a type="button" href="<?php echo BASE_PATH ?>/webRedirect.html?status=Cancel&type=checkoff" class="btn btn-default">Cancel</a>
					<?php } else if ($filterStudentId > 0) { ?>
						<a href="checkoff.html?studentId=<?php echo EncodeQueryData($filterStudentId); ?>&Type=C" name="btnCancel" id="btnCancel" class="btn btn-default">Cancel</a>
					<?php } else { ?>
						<a href="checkoff.html?rotationId=<?php echo EncodeQueryData($rotationId); ?>&Type=R" name="btnCancel" id="btnCancel" class="btn btn-default">Cancel</a>
					<?php }
				} else {
					if ($view != 'V') { ?>
						<button style="margin-right: 10px;" type="hidden" name="btnSubmit" id="btnSubmit" class="btn btn-success">Signoff</button>
						<?php }
					if ($filterStudentId > 0) {
						if ($IsMobile) {
						?>
							<a type="button" href="<?php echo BASE_PATH ?>/webRedirect.html?status=Cancel&type=checkoff" class="btn btn-default">Cancel</a>
						<?php
						} else {
						?>
							<a href="checkoffs.html?studentId=<?php echo EncodeQueryData($filterStudentId); ?>&Type=C" name="btnCancel" id="btnCancel" class="btn btn-default">Cancel</a>
						<?php }
					} else {
						if ($IsMobile) {
						?>
							<a type="button" href="<?php echo BASE_PATH ?>/webRedirect.html?status=Cancel&type=checkoff" class="btn btn-default">Cancel</a>
						<?php
						} else {
						?>
							<a href="checkoffs.html?rotationId=<?php echo EncodeQueryData($rotationId); ?>&Type=R" name="btnCancel" id="btnCancel" class="btn btn-default">Cancel</a>
				<?php }
					}
				} ?>
			</div>
		</form>
	</div>
	<?php //echo json_encode($sectionIdArray);
	?>

	<br />
	<?php include('includes/footer.php'); ?>
	<?php include("includes/datatablejs.php") ?>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
	<script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
	<script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
	<script src="https://cdn.ckeditor.com/ckeditor5/35.0.1/classic/ckeditor.js"></script>
	<script type="text/javascript">
		var currentSchoolId = '<?php echo $currentSchoolId; ?>';

		alertify.defaults.transition = "slide";
		alertify.defaults.theme.ok = "btn btn-success";
		alertify.defaults.theme.cancel = "btn btn-danger";
		alertify.defaults.theme.input = "form-control";

		//For Student Comment CK Editor										
		// ClassicEditor
		// 	.create(document.querySelector('#studentcomment'))
		// 	.catch(error => {
		// 		console.error(error);
		// 	});	

		var isActiveCheckoff = '<?php echo $isActiveCheckoff; ?>';

		if (isActiveCheckoff != 1) {
			// $('#mydiv *').prop('disabled',true);
		}

		$(window).load(function() {

			//For standard School Display last section horizontal to vertical
			$(".isLastSection .row").removeClass('some-class');

			if (currentSchoolId == 127)
				getScore();

			//For Mansfiled University
			// if(currentSchoolId == 127)
			// 	$("#posts a:first").trigger("click");

			$("#divTopLoading").addClass('hide');
			$(".select2_tags").select2({
				'placeholder': 'Select'
			});

			$(".select2_single").select2();


			// $(".cboclinician").select2();
			//for multiple selection
			$('#select2-cbohospitalsites-container').addClass('required-select2');
			$('#studentsignitureDate').datetimepicker({
				format: 'MM/DD/YYYY'
			});

			$('.completionDate').datetimepicker({
				format: 'MM/DD/YYYY'
			});

			$('#evaluationDate').datetimepicker({
				format: 'MM/DD/YYYY',
				maxDate: moment()
			});
			var evaluationDate = '<?php echo $evaluationDate; ?>';
			// console.log(evaluationDate);
			$('.evaluationDate').val(evaluationDate);

			var view = '<?php echo $view; ?>';
			var isActiveCheckoff = '<?php echo $isActiveCheckoff; ?>';
			// console.log('view', view);
			if (view == 'V' && isActiveCheckoff == 0) {
				$('.mydiv *').prop('disabled', true);
				$('.panel-default select, .panel-default input[type="date"],.panel-default input[type="checkbox"]').prop('disabled', true);
			}

			var isMultipleCheckoff = '<?php echo $isMultipleCheckoff; ?>';
			var topicIdsCount = '<?php echo $topicIdsCount; ?>';
			if (isMultipleCheckoff == 1 && topicIdsCount > 1) {
				console.log('isMultipleCheckoff', isMultipleCheckoff);

				$('div[sectionNo="2"]').find('input, select, textarea').prop('disabled', true);

			}


		});


		// function getQuestions(elemObj)
		// {

		// 	var schoolSectionId =$(elemObj).attr('schoolSectionId');
		// 	var sortOrder =$(elemObj).attr('sortOrder');
		// 	var checkoffId ='<?php echo $checkoffId; ?>';
		// 	var currentSchoolId ='<?php echo $currentSchoolId; ?>';
		// 	var isActiveCheckoff ='<?php echo $isActiveCheckoff; ?>';
		// 	var loggedClinicianType ='<?php echo $loggedClinicianType; ?>';
		// 	var view ='<?php echo $view; ?>';

		// 	var sectionIdCollapse = $("#sectionIdCollapse_"+schoolSectionId).val();

		// 	if(!sectionIdCollapse)
		// 	{
		// 		$("#divTopLoading_"+schoolSectionId).removeClass('hide');

		// 		$.ajax({
		// 		type: "POST",			
		// 		url: "../ajax/ajax_get_checkoff_questions_to_clinician.html",
		// 		data:{
		// 				schoolSectionId : schoolSectionId,
		// 				checkoffId : checkoffId,
		// 				currentSchoolId : currentSchoolId,
		// 				isActiveCheckoff : isActiveCheckoff,
		// 				sortOrder : sortOrder,
		// 				loggedClinicianType : loggedClinicianType,
		// 				view : view

		// 			},
		// 				success:function(responseData)
		// 				{

		// 					$( ".rendorHtml_"+schoolSectionId ).html( responseData );
		// 					$("#divTopLoading_"+schoolSectionId).addClass('hide');
		// 					$("#sectionIdCollapse_"+schoolSectionId).val('1');


		// 					//For Get opened SectionID	
		// 					if(isActiveCheckoff == 0 && sortOrder != 1)
		// 					{
		// 						$("#sectionIds").after(
		// 							"<input type='hidden' name='sectionIds[]' value="+schoolSectionId+" />"
		// 						);

		// 					}
		// 					else if(isActiveCheckoff != 0){
		// 						$("#sectionIds").after(
		// 							"<input type='hidden' name='sectionIds[]' value="+schoolSectionId+" />"
		// 						);
		// 					}


		// 				}
		// 			}); 
		// 	}

		// }

		// checkbox select with sections
		var sectionIdArray = <?php echo json_encode($sectionIdArray); ?>;
		// console.log('sectionIdArray'+sectionIdArray);

		$(document).on('click', '.isLab', function() {
			// Parse the sectionIdArray string into a JavaScript array
			// console.log('sectionIdArray'+sectionIdArray);

			var sectionId = $(this).attr('isSectionId');
			var className = 'isLab'; // You can change this dynamically based on your requirement

			var checkboxesWithSameSectionId = $('input[type="checkbox"].' + className + '[isSectionId="' + sectionId + '"]');

			// console.log('sectionId: ' + sectionId + ', className: ' + className);
			if (this.checked)
				checkboxesWithSameSectionId.prop('checked', true);
			else
				checkboxesWithSameSectionId.prop('checked', false);

			var result = getCheckedSectionsCount(sectionIdArray, className);
			$('#hiddenIsLab').val(result);
			// Log the result
			// console.log('Number of checked sections: ' + result);
		});

		$(document).on('click', '.isClinical', function() {
			// Parse the sectionIdArray string into a JavaScript array
			// var sectionIdArray = <?php echo json_encode($sectionIdArray); ?>;
			// console.log(sectionIdArray);

			var sectionId = $(this).attr('isSectionId');
			var className = 'isClinical'; // You can change this dynamically based on your requirement

			var checkboxesWithSameSectionId = $('input[type="checkbox"].' + className + '[isSectionId="' + sectionId + '"]');

			// console.log('sectionId: ' + sectionId + ', className: ' + className);
			if (this.checked)
				checkboxesWithSameSectionId.prop('checked', true);
			else
				checkboxesWithSameSectionId.prop('checked', false);

			var result = getCheckedSectionsCount(sectionIdArray, className);
			$('#hiddenIsClinical').val(result);
			// Log the result
			// console.log('Number of checked sections: ' + result);
		});

		// Function to get the count of checked sections
		function getCheckedSectionsCount(sectionIds, className) {
			var checkedSectionsCount = 0;

			// Iterate over each section ID in the array
			for (var i = 0; i < sectionIds.length; i++) {
				var sectionId = sectionIds[i];
				// console.log('sectionId'+sectionId);
				// Select checkboxes with the specific isSectionId and className
				var checkboxesWithSameSectionId = $('input[type="checkbox"].' + className + '[isSectionId="' + sectionId + '"]');

				// Check if at least one checkbox in the group is selected
				if (checkboxesWithSameSectionId.filter(':checked').length > 0) {
					checkedSectionsCount++;
				}
			}
			// console.log('count'+checkedSectionsCount);
			return checkedSectionsCount;
		}

		var result = getCheckedSectionsCount(sectionIdArray, 'isLab');
		$('#hiddenIsLab').val(result);
		var result = getCheckedSectionsCount(sectionIdArray, 'isClinical');
		$('#hiddenIsClinical').val(result);

		<?php if ($isActiveCheckoff != 1) { ?>
			$(document).ready(function() {
				$("#btnSubmit").click(function() {
					$('#mydiv *').prop('disabled', false);

				});
			});
		<?php } ?>

		//For  Chekoff required field
		<?php if ($isActiveCheckoff != 0 && $isActiveCheckoff != 2) { ?>

			$(document).on('click', '#btnSubmit', function(e) {


				var sectionId = '<?php echo $schoolSectionId; ?>';
				var totalCountSection = '<?php echo $totalrowscheckoff; ?>';

				var allradioButtons = $("#" + sectionId + " input[type='radio']").length;
				/* section five required Validation 06052021 */
				var requiredCount = allradioButtons / 4;
				var selectedButtons = $("#" + sectionId + " input[type='radio']:checked").length;

				if (totalCountSection == 5) {
					if (requiredCount >= 4) {
						if (requiredCount == selectedButtons) {
							$("input:radio:checked").prop('required', false);

							$('#radioCheck').val('1');
							return true;
						} else {
							alertify.alert("", "Required - Please fill in inputs of this Section 5: Performance Evaluation and Supervision Recommendation.");
							return false;
						}
					} else {

						alertify.alert("", "Required - Please fill in inputs of this Section 5: Performance Evaluation and Supervision Recommendation.");
						return false;

					}
					if (requiredCount != selectedButtons) {
						$("input").prop('required', true);
						alertify.alert("", "Please Select All Questions in Section 5");
						return false;
					}
				}


			});
		<?php } ?>
		$('#frmCheckoff').parsley().on('field:validated', function() {
				var ok = $('.parsley-error').length === 0;
			})
			.on('form:submit', function() {
				ShowProgressAnimation();
				return true; // Don't submit form for this demo
			});

		$(document).ready(function() {
			// Add minus icon for collapse element which is open by default
			$(".collapse.in").each(function() {
				$(this).siblings(".panel-heading").find(".glyphicon").addClass("glyphicon-minus").removeClass("glyphicon-plus");
			});

			// Toggle plus minus icon on show hide of collapse element
			$(".collapse").on('show.bs.collapse', function() {
				$(this).parent().find(".glyphicon").removeClass("glyphicon-plus").addClass("glyphicon-minus");
			}).on('hide.bs.collapse', function() {
				$(this).parent().find(".glyphicon").removeClass("glyphicon-minus").addClass("glyphicon-plus");
			});


		});

		//For Mansfield Univeristy Only
		if (currentSchoolId == 127) {

			$(document).ready(function() {
				$(document).on('click', 'input[type=radio]', function() {
					getScore();
				});
			});

			function getScore() {
				var partName = '';
				$('.partWiseScoreDiv').html('');
				var isPartCount = $('#isParts').val();
				if (isPartCount > 0) {
					var partArray = [];
					var partNameArray = ['A', 'B', 'C', 'D', 'E', 'F', 'G'];
					for (var i = 1; i <= isPartCount; i++) {
						var data = {};
						data.Item = i;
						data.partName = partNameArray[i - 1];

						partArray.push(data);
					}
					console.log(partArray);
					$.each(partArray, function(index, value) {
						var partName = value.partName;
						var Item = value.Item;
						partHTML = '<div class="row" style="margin-left: 0;margin-right: 0; margin-bottom: 5px;"><div class="col-md-12"><div class="form-group" style="margin-bottom: 0;"><div style="display: flex;">';
						partHTML += '<div style="margin-right: 20px;"><label class="control-label margin_top_five px-0">Total Part ' + partName + ' Score:</label>';
						partHTML += '<div class=" padding_zero">';
						partHTML += '<input type="text" name="" id="partScore_' + Item + '" class="form-control mb-5" value="" readonly></div></div>';
						partHTML += '<div><label class=" control-label margin_top_five margin_right_two mobile-px-0">Total Part ' + partName + ' Possible:</label>';
						partHTML += '<div class=" padding_zero"><input type="text" name="" id="partPossible_' + Item + '" class="form-control mb-5" value="" readonly></div></div></div>'
						partHTML += '</div></div></div>';

						$('.partWiseScoreDiv').append(partHTML);

						var partWiseSumCheckedButton = 0;
						var partClass = '.isPart_' + Item;
						$(partClass + " input[type=radio]:checked").each(function() {
							var checkedRadio = ($(this).parent().text());
							if ($.trim(checkedRadio) != 'NA')
								partWiseSumCheckedButton += parseInt(($.trim(checkedRadio)));
						});
						var partwiseAllradioButtons = $(partClass + " input[type=radio]:checked").length;
						console.log(partwiseAllradioButtons);
						allpartPossible = partwiseAllradioButtons * 5;
						$('#partScore_' + Item).val(partWiseSumCheckedButton);
						$('#partPossible_' + Item).val(allpartPossible);

					});

				}

				var sumCheckedButton = 0;
				$("input[type=radio]:checked").each(function() {
					var checkedRadio = ($(this).parent().text());
					if ($.trim(checkedRadio) != 'NA')
						sumCheckedButton += parseInt(($.trim(checkedRadio)));
				});
				var allradioButtons = $("input[type='radio']:checked").length;

				allPossible = allradioButtons * 5;

				// Score percentage
				scorePercentage = (sumCheckedButton / allPossible) * 100;
				scorePercentage = (scorePercentage).toFixed(2);
				$('#score').val(sumCheckedButton);
				$('#totalPossible').val(allPossible);
				$('#scorePercentage').val(scorePercentage);

			}

		}
	</script>
	<script>
		// Get all collapsible button elements
		var buttons = document.querySelectorAll(".collapsible");
		var contents = document.querySelectorAll(".panel-collapse");

		// Add click event listeners to all buttons
		buttons.forEach(function(button, index) {
			button.addEventListener("click", function() {
				// Check if the content is currently expanded
				var isExpanded = contents[index].style.display === "block";

				// Close all sections
				contents.forEach(function(content) {
					content.style.display = "none";
				});

				// Reset the "expanded" class for all buttons
				buttons.forEach(function(btn) {
					btn.classList.remove("expanded");
				});

				// Toggle the content for the clicked section
				if (!isExpanded) {
					contents[index].style.display = "block";
					button.classList.add("expanded");
				}
			});
		});
	</script>
</body>
</body>

</html>