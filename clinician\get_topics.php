<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');



if (!empty($_POST["Rotation_Id"])) {
	$objDB = new clsDB();
	$query = '';
	$currentSchoolId = '';

	$studentId = $_POST["studentId"];
	$query = "SELECT schooltopicmaster.*, rotation.rotationId
				FROM schooltopicmaster 
				INNER JOIN coursetopicdetail ON schooltopicmaster.schoolTopicId=coursetopicdetail.schoolTopicId
				INNER JOIN courses ON coursetopicdetail.courseId=courses.courseId
				INNER JOIN rotation ON rotation.courseId=courses.courseId 
				WHERE rotation.rotationId='" . DecodeQueryData($_POST["Rotation_Id"]) . "' AND schooltopicmaster.status =1
				AND NOT EXISTS (select checkoff.schoolTopicId from checkoff where schooltopicmaster.schoolTopicId =checkoff.schoolTopicId AND checkoff.studentId ='" . ($studentId) . "' AND checkoff.rotationId ='" . DecodeQueryData($_POST["Rotation_Id"]) . "')					
				GROUP BY `schooltopicmaster`.`checkoffTitleId` 
				ORDER BY `schooltopicmaster`.`schooltitle` ASC";

	// echo $query;exit;
	$Topics = $objDB->GetResultSet($query);

	$seletedTopicId = (isset($_POST["schoolTopicId"])) ? $_POST["schoolTopicId"] : 0;
	$isMultipleCheckoff = (isset($_POST["isMultipleCheckoff"])) ? $_POST["isMultipleCheckoff"] : 0;
?>
	
<?php
	if ($isMultipleCheckoff == 0)
		echo "<option value='0' {$seletedString} >Select</option>";
	
	while ($rows = mysqli_fetch_assoc($Topics)) {
		$seletedString = ($seletedTopicId == $rows['schoolTopicId']) ? "selected"  : "";

		$TopicTitle = $rows["schooltitle"];

		echo "<option value='{$rows['schoolTopicId']}' {$seletedString} >{$TopicTitle}</option>";
	}
}
?>