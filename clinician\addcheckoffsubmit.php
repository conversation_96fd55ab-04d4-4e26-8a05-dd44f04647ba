<?php
$IsMobile  = isset($_POST['IsMobile']) ? $_POST['IsMobile'] : 0;

// ini_set('display_errors', 1);
// ini_set('display_startup_errors', 1);
// error_reporting(E_ALL);

include('../includes/config.php');
include('includes/validateUserLogin.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clscheckoff.php');
include('../class/clsSendEmails.php');
include('../class/clsSMTPSettings.php');
include('../setRequest.php');
// echo '<pre>';
// print_r($_POST);
// print_r($_GET);
// exit;


$Type = 'C';
$checkoffId = 0;
$scorePercentage = 0;
$objcheckoff = new clscheckoff();
if (isset($_GET['checkoffId'])) {
	$checkoffId = DecodeQueryData($_GET['checkoffId']);
	//$objcheckoff->DeleteCheckoffDetail($checkoffId);
}

if (isset($_GET['studentId']))
	$studentId = DecodeQueryData($_GET['studentId']);

if (isset($_GET['rotationId']))
	$rotationId = DecodeQueryData($_GET['rotationId']);

$sectionIds = $_POST['sectionIds'];
$sectionIds = $sectionIds ? array_unique($sectionIds) : [];
$sectionIds = $sectionIds ? array_filter($sectionIds) : [];

$objcheckoff->DeleteCheckoffDetail($checkoffId);

//--------------------------------------------------------------
//GET USER CONVERTED CURRENT DATE TIME
//--------------------------------------------------------------
$TimeZone = $_SESSION["loggedClinicianSchoolTimeZone"];
$CurrentDate = $_POST['evaluationDate'];
$CurrentDate = converToServerTimeZone($CurrentDate, $TimeZone);
$CurrentDate = str_replace('00:00:00', '12:00 PM', $CurrentDate);
$evaluationDate = date('Y-m-d H:i', strtotime($CurrentDate));
//--------------------------------------------------------------
$status = ($checkoffId > 0) ? 'Updated' : 'Added';

$selTopicId = ($_POST['selTopicId']);
$rotationId  = ($_POST['rotationId']);
//$studentId  = ($_POST['cbostudentshide']);
$startdatetime  = ($_POST['startdatetime']);

if ($startdatetime != '') {
	$startdatetime = GetDateStringInServerFormat($startdatetime);
	$startdatetime = str_replace('00:00:00', '12:00 PM', $startdatetime);
	$startdatetime = date('Y-m-d H:i', strtotime($startdatetime));
}
$checkoffDate = date('m-d-Y', strtotime($startdatetime));

$clinicianId  = ($_POST['clinicianId']);

$multiplestudentids  = ($_POST['multiplestudentids']);
$studentIds = $myarray = explode(",", $_POST['multiplestudentids']);
$topicids  = isset($_POST['topicIds']) ? $_POST['topicIds'] : '';
$topicarray = ($topicids != '') ? explode(",", $topicids) : '';
$isMultipleCheckoff = isset($_POST['isMultipleCheckoff']) ? $_POST['isMultipleCheckoff'] : 0;

$baseStudentId = $studentIds[0];
$baseCheckoffs = [];
// if($currentSchoolId == 127)
// 	$objcheckoff->DeleteCheckoffDetail($checkoffId);

// echo "<pre>"; print_r($topicarray);exit;
//EDIT
// if ($checkoffId > 0) {
// 	foreach ($myarray as $value) {
// 		$objcheckoff->studentId = $value;
// 		$objcheckoff->schoolId = $currentSchoolId;
// 		$objcheckoff->schoolTopicId = $selTopicId;
// 		$objcheckoff->rotationId = $rotationId;
// 		$objcheckoff->clinicianId = $clinicianId;
// 		$objcheckoff->evaluationDate = $evaluationDate;
// 		$objcheckoff->checkoffDateTime = $startdatetime;
// 		$objcheckoff->createdBy = $clinicianId;
// 		$retcheckoffId = $objcheckoff->SaveCheckoff($checkoffId);

// 		if ($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSubmit'])) {

// 			foreach ($_POST as $id => $value) {
// 				if (strpos($id, 'questionoptions_') === 0) {

// 					$id = explode("_", $id)[1];
// 					$objcheckoff->checkoffId = $retcheckoffId;
// 					$objcheckoff->schoolQuestionDId = $id;
// 					$objcheckoff->schoolOptionValue = $value[0];
// 					$objcheckoff->questionId = $id;
// 					$objcheckoff->schoolOptionAnswerText = '';
// 					$checkOffDId = $objcheckoff->SaveCheckOffDetail();
// 				}
// 			} //exit;
// 			foreach ($_POST as $id => $value) {
// 				if (strpos($id, 'questionoptionst_') === 0) {
// 					$id = explode("_", $id)[1];
// 					$objcheckoff->checkoffId = $retcheckoffId;
// 					$objcheckoff->schoolQuestionDId = $id;
// 					$objcheckoff->schoolOptionValue = '';
// 					$objcheckoff->questionId = $id;
// 					$objcheckoff->schoolOptionAnswerText = addslashes($value[0]);
// 					$checkOffDId = $objcheckoff->SaveCheckOffDetail();
// 				}
// 			}

// 			//For Mansfield University
// 			foreach ($_POST as $id => $value) {
// 				if (strpos($id, 'mansfieldquestionoptions_') === 0) {
// 					$id = explode("_", $id)[1];
// 					$objcheckoff->checkoffId = $retcheckoffId;
// 					$objcheckoff->schoolQuestionDId = $id;
// 					$objcheckoff->schoolOptionValue = $value[0];
// 					$objcheckoff->questionId = $id;
// 					$objcheckoff->comments = isset($_POST['mansfield_comment_question_' . $id]) ? $_POST['mansfield_comment_question_' . $id] : '';
// 					$objcheckoff->schoolOptionAnswerText = '';
// 					$checkOffDId = $objcheckoff->SaveMansfieldCheckOffDetail();
// 				}
// 			}


// 			//Calculate checkoff 
// 			if ($currentSchoolId == 127) {
// 				$Score = isset($_POST['score']) ? $_POST['score'] : 0;
// 				$scorePercentage = isset($_POST['scorePercentage']) ? $_POST['scorePercentage'] : 0;
// 			} else
// 				$Score = $objcheckoff->CalculateCheckoffStudentScores($retcheckoffId);
// 			$objcheckoff->UpdateCalculatedCheckoffScore($retcheckoffId, $Score);

// 			if ($scorePercentage) {
// 				$objDB = new clsDB();
// 				$retscorePercentage = $objDB->UpdateSingleColumnValueToTable('checkoff', 'calculatedScorePercentage', $scorePercentage, 'checkoffId', $retcheckoffId);
// 				unset($objDB);
// 			}
// 			// echo $retscorePercentage;exit;
// 		}
// 		unset($objcheckoff);

// 		if ($retcheckoffId > 0) {


// 			//Audit Log Start
// 			// Instantiate the Logger class
// 			$objLog = new clsLogger();

// 			// Determine the action type (EDIT or ADD) based on the presence of a journal ID
// 			$action = ($checkoffId > 0) ? $objLog::EDIT : $objLog::ADD;
// 			$userType = $objLog::CLINICIAN; // User type is set to STUDENT

// 			$objcheckoff = new clscheckoff();
// 			$objcheckoff->saveCheckoffAuditLog($checkoffId, $retcheckoffId, $clinicianId, $userType, $action);
// 			unset($objcheckoff);

// 			unset($objLog);
// 			//Audit Log End
// 			if ($studentId != 0) {
// 				if ($IsMobile) {
// 					header('location:' . BASE_PATH . '/webRedirect.html?status=Success&type=checkoff');
// 					exit();
// 				} else {
// 					header('location:checkoff.html?studentId=' . EncodeQueryData($multiplestudentids) . '&Type=C&status=' . $status);
// 					exit;
// 				}
// 			} else {
// 				if ($IsMobile) {
// 					header('location:' . BASE_PATH . '/webRedirect.html?status=Success&type=checkoff');
// 					exit();
// 				} else {
// 					header('location:checkoff.html?rotationId=' . EncodeQueryData($rotationId) . '&Type=R&status=' . $status);
// 					exit();
// 				}
// 			}
// 		} else {
// 			if ($IsMobile) {
// 				header('location:' . BASE_PATH . '/webRedirect.html?status=Error&type=checkoff');
// 				exit();
// 			} else {
// 				// header('location:caseStudyList.html');
// 				header('location:addcheckoff.html?status=error');
// 				exit();
// 			}
// 			// header('location:addcheckoff.html?status=error');
// 		}
// 	}
// } else {
foreach ($topicarray as $topicId) {
	$selTopicId = $topicId;
	// foreach ($myarray as $value) {

	$checkoffStudentId = $baseStudentId;
	$objcheckoff->studentId = $checkoffStudentId;
	$objcheckoff->schoolId = $currentSchoolId;
	$objcheckoff->schoolTopicId = $selTopicId;
	$objcheckoff->rotationId = $rotationId;
	$objcheckoff->clinicianId = $clinicianId;
	$objcheckoff->evaluationDate = $evaluationDate;
	$objcheckoff->checkoffDateTime = $startdatetime;
	$objcheckoff->createdBy = $clinicianId;
	$retcheckoffId = $objcheckoff->SaveCheckoff($checkoffId);

	$baseCheckoffs[$topicId] = $retcheckoffId;

	if ($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSubmit'])) {

		foreach ($_POST as $id => $value) {

			if (strpos($id, 'questionoptions_') === 0) {
				$id = explode("_", $id)[1];
				$objcheckoff->checkoffId = $retcheckoffId;
				$objcheckoff->schoolQuestionDId = $id;
				$objcheckoff->schoolOptionValue = $value[0];
				$objcheckoff->questionId = $id;
				$objcheckoff->schoolOptionAnswerText = '';
				$checkOffDId = $objcheckoff->SaveCheckOffDetail();
			}
		} //exit;
		foreach ($_POST as $id => $value) {
			if (strpos($id, 'questionoptionst_') === 0) {
				$id = explode("_", $id)[1];
				$objcheckoff->checkoffId = $retcheckoffId;
				$objcheckoff->schoolQuestionDId = $id;
				$objcheckoff->schoolOptionValue = '';
				$objcheckoff->questionId = $id;
				$objcheckoff->schoolOptionAnswerText = $value[0];
				$checkOffDId = $objcheckoff->SaveCheckOffDetail();
			}
		}

		//For Mansfield University
		foreach ($_POST as $id => $value) {
			if (strpos($id, 'mansfieldquestionoptions_') === 0) {
				$id = explode("_", $id)[1];
				$objcheckoff->checkoffId = $retcheckoffId;
				$objcheckoff->schoolQuestionDId = $id;
				$objcheckoff->schoolOptionValue = $value[0];
				$objcheckoff->questionId = $id;
				$objcheckoff->comments = isset($_POST['mansfield_comment_question_' . $id]) ? $_POST['mansfield_comment_question_' . $id] : '';
				$objcheckoff->schoolOptionAnswerText = '';
				$checkOffDId = $objcheckoff->SaveMansfieldCheckOffDetail();
			}
		}
	}

	// //Calculate checkoff by tejas
	if ($currentSchoolId == 127) {
		$Score = isset($_POST['score']) ? $_POST['score'] : 0;
		$scorePercentage = isset($_POST['scorePercentage']) ? $_POST['scorePercentage'] : 0;
	} else
		$Score = $objcheckoff->CalculateCheckoffStudentScores($retcheckoffId);
	$objcheckoff->UpdateCalculatedCheckoffScore($retcheckoffId, $Score);

	if ($scorePercentage) {
		$objDB = new clsDB();
		$objDB->UpdateSingleColumnValueToTable('checkoff', 'calculatedScorePercentage', $scorePercentage, 'checkoffId', $CheckoffId);
		unset($objDB);
	}
	// send mail to clinician
	$objSendEmails = new clsSendEmails($currentSchoolId);
	$objSendEmails->SendCheckoffToStudentsByClinician($checkoffStudentId, $selTopicId, $checkoffDate);
	unset($objSendEmails);
	// }

	//Audit Log Start
	// Instantiate the Logger class
	$objLog = new clsLogger();

	// Determine the action type (EDIT or ADD) based on the presence of a journal ID
	$action = ($checkoffId > 0) ? $objLog::EDIT : $objLog::ADD;
	$userType = $objLog::CLINICIAN; // User type is set to STUDENT

	$objcheckoff->saveCheckoffAuditLog($checkoffId, $retcheckoffId, $clinicianId, $userType, $action);

	unset($objLog);
	//Audit Log End
}
// print_r($baseCheckoffs);
// exit;

// ==========================
// === Clone for others ====
// ==========================

for ($i = 1; $i < count($studentIds); $i++) {
	$studentId = $studentIds[$i];

	foreach ($topicarray as $topicId) {
		$oldCheckoffId = $baseCheckoffs[$topicId];

		$newCheckoffId = $objcheckoff->cloneCheckoff($oldCheckoffId, $studentId);
		$objcheckoff->cloneCheckoffDetails($oldCheckoffId, $newCheckoffId);

		if ($checkoffId == 0) {
			// send mail to clinician
			$objSendEmails = new clsSendEmails($currentSchoolId);
			$objSendEmails->SendCheckoffToStudentsByClinician($studentId, $topicId, $checkoffDate);
			unset($objSendEmails);
		}

		//Audit Log Start
		// Instantiate the Logger class
		$objLog = new clsLogger();

		// Determine the action type (EDIT or ADD) based on the presence of a journal ID
		$action = ($checkoffId > 0) ? $objLog::EDIT : $objLog::ADD;
		$userType = $objLog::CLINICIAN; // User type is set to STUDENT

		$objcheckoff->saveCheckoffAuditLog($checkoffId, $newCheckoffId, $clinicianId, $userType, $action);

		unset($objLog);
		//Audit Log End
	}
}

unset($objcheckoff);
// exit;


if ($retcheckoffId > 0) {

	if ($IsMobile) {
		header('location:' . BASE_PATH . '/webRedirect.html?status=Success&type=checkoff');
		exit;
	} else {
		header('location:checkoff.html?studentId=' . EncodeQueryData($myarray[0]) . '&Type=' . $Type . '&status=' . $status);
		exit();
	}
} else {
	if ($IsMobile) {
		header('location:' . BASE_PATH . '/webRedirect.html?status=Error&type=checkoff');
		exit();
	} else {
		// header('location:caseStudyList.html');
		header('location:addcheckoff.html?status=error');
		exit();
	}
}
// }
