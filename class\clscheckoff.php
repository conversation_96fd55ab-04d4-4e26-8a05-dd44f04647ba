<?php

use Twi<PERSON>\TwiML\Voice\Echo_;

class clscheckoff
{
	var $checkoffId = '';
	var $schoolQuestionDId = '';
	var $schoolOptionValue = '';
	var $schoolOptionAnswerText = '';
	var $questionId = '';
	var $student_evaluationDate = '';
	var $school_evaluationDate = '';

	function GetAllcheckoff($currentSchoolId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT schooltopicmaster.*, rotation.rotationId
				FROM schooltopicmaster 
				INNER JOIN coursetopicdetail ON schooltopicmaster.schoolTopicId=coursetopicdetail.schoolTopicId
				INNER JOIN courses ON coursetopicdetail.courseId=courses.courseId
				INNER JOIN rotation ON rotation.courseId=courses.courseId 
				WHERE courses.schoolId=" . $currentSchoolId;
		$sql .= " GROUP BY `schooltopicmaster`.`checkoffTitleId` ASC";
		$sql .= " ORDER BY `schooltopicmaster`.`checkoffTitleId` ASC";
		//echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetAllAdvancecheckoff($currentSchoolId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT schooltopicmaster.*, rotation.rotationId
				FROM schooltopicmaster 
				INNER JOIN coursetopicdetail ON schooltopicmaster.schoolTopicId=coursetopicdetail.schoolTopicId
				INNER JOIN courses ON coursetopicdetail.courseId=courses.courseId
				INNER JOIN rotation ON rotation.courseId=courses.courseId 
				WHERE courses.schoolId=" . $currentSchoolId . " AND schooltopicmaster.isAdvanceCheckoff='1'";
		$sql .= " GROUP BY `schooltopicmaster`.`checkoffTitleId` ASC";
		$sql .= " ORDER BY `schooltopicmaster`.`checkoffTitleId` ASC";
		//echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetAllRotation($studentId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT rotation.* FROM rotation
				INNER JOIN rotationdetails ON rotation.rotationId = rotationdetails.rotationId 
				WHERE rotationdetails.studentId=" . $studentId;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}
	function GetAllClinician($currentSchoolId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT *FROM clinician
				WHERE schoolId=" . $currentSchoolId;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetDefaulttopicDetails($selTopicId)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT schoolsectionmaster.schoolSectionTitle,schoolsectionmaster.schoolSectionId,
					schoolsectionmaster.description,schoolsectionmaster.sortOrder
                FROM schooltopicdetails  
                LEFT JOIN schoolsectionmaster ON schoolsectionmaster.`schoolSectionId`=
												schooltopicdetails.`schoolSectionId`
                WHERE schooltopicdetails.schoolTopicId=" . $selTopicId . " 
				group by schoolsectionmaster.schoolSectionTitle
				 ORDER BY schoolsectionmaster.sortOrder";

		//    echo $sql;
		// exit;
		$rows = $objDB->GetResultset($sql);

		return $rows;
		unset($objDB);
	}


	function GetDefaultadvancetopicDetails($selTopicId)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT schoolsectionmaster.schoolSectionTitle,schoolsectionmaster.schoolSectionId,
					schoolsectionmaster.description,schoolsectionmaster.sortOrder
                FROM schooltopicdetails  
                LEFT JOIN schoolsectionmaster ON schoolsectionmaster.`schoolSectionId`=
												schooltopicdetails.`schoolSectionId`
				
                WHERE schooltopicdetails.schoolTopicId=" . $selTopicId . " AND schoolsectionmaster.isAdvanceCheckoff='1'
				group by schoolsectionmaster.schoolSectionTitle
				 ORDER BY schoolsectionmaster.sortOrder";

		//echo $sql;
		$rows = $objDB->GetResultset($sql);

		return $rows;
		unset($objDB);
	}
	function Getschooldefaultquestionmaster($schoolSectionId, $currentSchoolId = 0)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT schooldefaultquestionmaster.schoolQuestionTitle,schooldefaultquestionmaster.schoolQuestionType,schooldefaultquestionmaster.sortOrder,
				schooldefaultquestionmaster.proceduralSteps,schooldefaultquestionmaster.marks,
				schooldefaultquestionmaster.schoolQuestionId
                FROM schooltopicdetails 
                LEFT JOIN schooldefaultquestionmaster ON schooldefaultquestionmaster.`schoolQuestionId`
															=schooltopicdetails.`schoolQuestionId`
                WHERE schooltopicdetails.schoolSectionId=" . $schoolSectionId . " AND schooldefaultquestionmaster.schoolQuestionId !=''";
		if ($currentSchoolId != 127)
			$sql .= " GROUP BY schooldefaultquestionmaster.schoolQuestionTitle";
		else
			$sql .= " GROUP BY schooldefaultquestionmaster.schoolQuestionId";

		// $sql .= " ORDER BY `schooldefaultquestionmaster`.`sortOrder` ASC"; 
		$sql .= " order by LENGTH(schooldefaultquestionmaster.sortOrder), schooldefaultquestionmaster.sortOrder";

		//   echo $sql;
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetschooldefaultquestionforIRRReport($schoolSectionId)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT schooldefaultquestionmaster.schoolQuestionTitle,				
				schooldefaultquestionmaster.`schoolQuestionId`,schooldefaultquestionmaster.schoolQuestionType	
                FROM schooltopicdetails 
                INNER JOIN schooldefaultquestionmaster ON schooldefaultquestionmaster.`schoolQuestionId`
																=schooltopicdetails.`schoolQuestionId`
				LEFT JOIN schooldefaultquestiondetail ON schooldefaultquestionmaster.schoolQuestionId=
														schooldefaultquestiondetail.schoolQuestionId														
				LEFT JOIN clinicianirrdetails ON schooldefaultquestiondetail.schoolQuestionDId=
													clinicianirrdetails.schoolOptionValue
				
                WHERE schooltopicdetails.schoolSectionId=" . $schoolSectionId . " 
				GROUP BY schooldefaultquestionmaster.schoolQuestionTitle
				ORDER BY `schooldefaultquestionmaster`.`sortOrder` ASC";

		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetCalculationsforIRRReport($schoolSectionId, $schoolQuestionId, $clinicianId, $irrMasterId, $preceptorId = 0)
	{

		$objDB = new clsDB();
		$rows = "";

		if ($clinicianId) {

			$sql = "SELECT schooldefaultquestionmaster.schoolQuestionTitle,				
					(select distinct (count(schoolOptionText)) from schooldefaultquestiondetail
						INNER JOIN clinicianirrdetails ON clinicianirrdetails.schoolOptionValue=
									schooldefaultquestiondetail.schoolQuestionDId
						INNER JOIN irrdetails ON irrdetails.irrDetailId=clinicianirrdetails.irrDetailId
						 AND irrdetails.clinicianId=" . $clinicianId . " AND irrdetails.irrMasterId=" . $irrMasterId . "
						 WHERE schooldefaultquestiondetail.schoolOptionText= 'Yes'
						AND clinicianirrdetails.questionId=" . $schoolQuestionId .
				") AS YesCount,
						
						(select count(schoolOptionText) from schooldefaultquestiondetail
						INNER JOIN clinicianirrdetails ON clinicianirrdetails.schoolOptionValue=
									schooldefaultquestiondetail.schoolQuestionDId
						INNER JOIN irrdetails ON irrdetails.irrDetailId=clinicianirrdetails.irrDetailId
						 AND irrdetails.clinicianId=" . $clinicianId . " AND irrdetails.irrMasterId=" . $irrMasterId . "
						WHERE schooldefaultquestiondetail.schoolOptionText= 'No'
						AND clinicianirrdetails.questionId=" . $schoolQuestionId .
				" ) AS NoCount,
						
						(select count(schoolOptionText) from schooldefaultquestiondetail
						INNER JOIN clinicianirrdetails ON clinicianirrdetails.schoolOptionValue=
									schooldefaultquestiondetail.schoolQuestionDId
						INNER JOIN irrdetails ON irrdetails.irrDetailId=clinicianirrdetails.irrDetailId
						 AND irrdetails.clinicianId=" . $clinicianId . " AND irrdetails.irrMasterId=" . $irrMasterId . "
						WHERE schooldefaultquestiondetail.choiceAnswer= 100
						AND clinicianirrdetails.questionId=" . $schoolQuestionId .
				") AS A,
						
						(select count(schoolOptionText) from schooldefaultquestiondetail
						INNER JOIN clinicianirrdetails ON clinicianirrdetails.schoolOptionValue=
									schooldefaultquestiondetail.schoolQuestionDId
						INNER JOIN irrdetails ON irrdetails.irrDetailId=clinicianirrdetails.irrDetailId
						 AND irrdetails.clinicianId=" . $clinicianId . " AND irrdetails.irrMasterId=" . $irrMasterId . "
						WHERE schooldefaultquestiondetail.choiceAnswer= 88
						AND clinicianirrdetails.questionId=" . $schoolQuestionId .
				") AS B,
						
						(select count(schoolOptionText) from schooldefaultquestiondetail
						INNER JOIN clinicianirrdetails ON clinicianirrdetails.schoolOptionValue=
									schooldefaultquestiondetail.schoolQuestionDId
						INNER JOIN irrdetails ON irrdetails.irrDetailId=clinicianirrdetails.irrDetailId
						AND irrdetails.clinicianId=" . $clinicianId . "  AND irrdetails.irrMasterId=" . $irrMasterId . "
						WHERE schooldefaultquestiondetail.choiceAnswer= 75
						AND clinicianirrdetails.questionId=" . $schoolQuestionId .
				") AS C,
						
						(select count(schoolOptionText) from schooldefaultquestiondetail
						INNER JOIN clinicianirrdetails ON clinicianirrdetails.schoolOptionValue=
									schooldefaultquestiondetail.schoolQuestionDId
						INNER JOIN irrdetails ON irrdetails.irrDetailId=clinicianirrdetails.irrDetailId
						AND irrdetails.clinicianId=" . $clinicianId . " AND irrdetails.irrMasterId=" . $irrMasterId . "
						WHERE schooldefaultquestiondetail.choiceAnswer= 63
						AND clinicianirrdetails.questionId=" . $schoolQuestionId .
				") AS D
						
                FROM schooltopicdetails 
                INNER JOIN schooldefaultquestionmaster ON schooldefaultquestionmaster.`schoolQuestionId`
																=schooltopicdetails.`schoolQuestionId`
				LEFT JOIN schooldefaultquestiondetail ON schooldefaultquestionmaster.schoolQuestionId=
														schooldefaultquestiondetail.schoolQuestionId														
				LEFT JOIN clinicianirrdetails ON schooldefaultquestiondetail.schoolQuestionDId=
													clinicianirrdetails.schoolOptionValue				
                WHERE schooltopicdetails.schoolSectionId=" . $schoolSectionId . " AND schooltopicdetails.schoolQuestionId
																					=" . $schoolQuestionId . "
				
				  GROUP BY schooldefaultquestionmaster.schoolQuestionTitle
				ORDER BY `schooldefaultquestionmaster`.`sortOrder` ASC";
		} else if ($preceptorId) {

			$sql = "SELECT schooldefaultquestionmaster.schoolQuestionTitle,				
					(select distinct (count(schoolOptionText)) from schooldefaultquestiondetail
						INNER JOIN clinicianirrdetails ON clinicianirrdetails.schoolOptionValue=
									schooldefaultquestiondetail.schoolQuestionDId
						INNER JOIN irrdetails ON irrdetails.irrDetailId=clinicianirrdetails.irrDetailId
						 AND irrdetails.preceptorId=" . $preceptorId . " AND irrdetails.irrMasterId=" . $irrMasterId . "
						 WHERE schooldefaultquestiondetail.schoolOptionText= 'Yes'
						AND clinicianirrdetails.questionId=" . $schoolQuestionId .
				") AS YesCount,
						
						(select count(schoolOptionText) from schooldefaultquestiondetail
						INNER JOIN clinicianirrdetails ON clinicianirrdetails.schoolOptionValue=
									schooldefaultquestiondetail.schoolQuestionDId
						INNER JOIN irrdetails ON irrdetails.irrDetailId=clinicianirrdetails.irrDetailId
						 AND irrdetails.preceptorId=" . $preceptorId . " AND irrdetails.irrMasterId=" . $irrMasterId . "
						WHERE schooldefaultquestiondetail.schoolOptionText= 'No'
						AND clinicianirrdetails.questionId=" . $schoolQuestionId .
				" ) AS NoCount,
						
						(select count(schoolOptionText) from schooldefaultquestiondetail
						INNER JOIN clinicianirrdetails ON clinicianirrdetails.schoolOptionValue=
									schooldefaultquestiondetail.schoolQuestionDId
						INNER JOIN irrdetails ON irrdetails.irrDetailId=clinicianirrdetails.irrDetailId
						 AND irrdetails.preceptorId=" . $preceptorId . " AND irrdetails.irrMasterId=" . $irrMasterId . "
						WHERE schooldefaultquestiondetail.choiceAnswer= 100
						AND clinicianirrdetails.questionId=" . $schoolQuestionId .
				") AS A,
						
						(select count(schoolOptionText) from schooldefaultquestiondetail
						INNER JOIN clinicianirrdetails ON clinicianirrdetails.schoolOptionValue=
									schooldefaultquestiondetail.schoolQuestionDId
						INNER JOIN irrdetails ON irrdetails.irrDetailId=clinicianirrdetails.irrDetailId
						 AND irrdetails.preceptorId=" . $preceptorId . " AND irrdetails.irrMasterId=" . $irrMasterId . "
						WHERE schooldefaultquestiondetail.choiceAnswer= 88
						AND clinicianirrdetails.questionId=" . $schoolQuestionId .
				") AS B,
						
						(select count(schoolOptionText) from schooldefaultquestiondetail
						INNER JOIN clinicianirrdetails ON clinicianirrdetails.schoolOptionValue=
									schooldefaultquestiondetail.schoolQuestionDId
						INNER JOIN irrdetails ON irrdetails.irrDetailId=clinicianirrdetails.irrDetailId
						AND irrdetails.preceptorId=" . $preceptorId . "  AND irrdetails.irrMasterId=" . $irrMasterId . "
						WHERE schooldefaultquestiondetail.choiceAnswer= 75
						AND clinicianirrdetails.questionId=" . $schoolQuestionId .
				") AS C,
						
						(select count(schoolOptionText) from schooldefaultquestiondetail
						INNER JOIN clinicianirrdetails ON clinicianirrdetails.schoolOptionValue=
									schooldefaultquestiondetail.schoolQuestionDId
						INNER JOIN irrdetails ON irrdetails.irrDetailId=clinicianirrdetails.irrDetailId
						AND irrdetails.preceptorId=" . $preceptorId . " AND irrdetails.irrMasterId=" . $irrMasterId . "
						WHERE schooldefaultquestiondetail.choiceAnswer= 63
						AND clinicianirrdetails.questionId=" . $schoolQuestionId .
				") AS D
						
                FROM schooltopicdetails 
                INNER JOIN schooldefaultquestionmaster ON schooldefaultquestionmaster.`schoolQuestionId`
																=schooltopicdetails.`schoolQuestionId`
				LEFT JOIN schooldefaultquestiondetail ON schooldefaultquestionmaster.schoolQuestionId=
														schooldefaultquestiondetail.schoolQuestionId														
				LEFT JOIN clinicianirrdetails ON schooldefaultquestiondetail.schoolQuestionDId=
													clinicianirrdetails.schoolOptionValue				
                WHERE schooltopicdetails.schoolSectionId=" . $schoolSectionId . " AND schooltopicdetails.schoolQuestionId
																					=" . $schoolQuestionId . "
				
				  GROUP BY schooldefaultquestionmaster.schoolQuestionTitle
				ORDER BY `schooldefaultquestionmaster`.`sortOrder` ASC";
		} else {

			$sql = "SELECT schooldefaultquestionmaster.schoolQuestionTitle,				
					(select distinct (count(schoolOptionText)) from schooldefaultquestiondetail
						INNER JOIN clinicianirrdetails ON clinicianirrdetails.schoolOptionValue=
									schooldefaultquestiondetail.schoolQuestionDId	
									 AND clinicianirrdetails.irrMasterId=" . $irrMasterId . "
						INNER JOIN irrdetails ON irrdetails.irrDetailId=clinicianirrdetails.irrDetailId
						WHERE schooldefaultquestiondetail.schoolOptionText= 'Yes'
						AND clinicianirrdetails.questionId=" . $schoolQuestionId . ") AS YesCount,
						
						(select count(schoolOptionText) from schooldefaultquestiondetail
						INNER JOIN clinicianirrdetails ON clinicianirrdetails.schoolOptionValue=
									schooldefaultquestiondetail.schoolQuestionDId
									 AND clinicianirrdetails.irrMasterId=" . $irrMasterId . "
						INNER JOIN irrdetails ON irrdetails.irrDetailId=clinicianirrdetails.irrDetailId
						WHERE schooldefaultquestiondetail.schoolOptionText= 'No'
						AND clinicianirrdetails.questionId=" . $schoolQuestionId . ") AS NoCount,
						
						(select count(schoolOptionText) from schooldefaultquestiondetail
						INNER JOIN clinicianirrdetails ON clinicianirrdetails.schoolOptionValue=
									schooldefaultquestiondetail.schoolQuestionDId
									 AND clinicianirrdetails.irrMasterId=" . $irrMasterId . "
						INNER JOIN irrdetails ON irrdetails.irrDetailId=clinicianirrdetails.irrDetailId
						WHERE schooldefaultquestiondetail.choiceAnswer= 100
						AND clinicianirrdetails.questionId=" . $schoolQuestionId . ") AS A,
						
						(select count(schoolOptionText) from schooldefaultquestiondetail
						INNER JOIN clinicianirrdetails ON clinicianirrdetails.schoolOptionValue=
									schooldefaultquestiondetail.schoolQuestionDId
									 AND clinicianirrdetails.irrMasterId=" . $irrMasterId . "
						INNER JOIN irrdetails ON irrdetails.irrDetailId=clinicianirrdetails.irrDetailId
						WHERE schooldefaultquestiondetail.choiceAnswer= 88
						AND clinicianirrdetails.questionId=" . $schoolQuestionId . ") AS B,
						
						(select count(schoolOptionText) from schooldefaultquestiondetail
						INNER JOIN clinicianirrdetails ON clinicianirrdetails.schoolOptionValue=
									schooldefaultquestiondetail.schoolQuestionDId
										AND clinicianirrdetails.irrMasterId=" . $irrMasterId . "
						INNER JOIN irrdetails ON irrdetails.irrDetailId=clinicianirrdetails.irrDetailId
						WHERE schooldefaultquestiondetail.choiceAnswer= 75
						AND clinicianirrdetails.questionId=" . $schoolQuestionId . ") AS C,
						
						(select count(schoolOptionText) from schooldefaultquestiondetail
						INNER JOIN clinicianirrdetails ON clinicianirrdetails.schoolOptionValue=
									schooldefaultquestiondetail.schoolQuestionDId
									 AND clinicianirrdetails.irrMasterId=" . $irrMasterId . "
						INNER JOIN irrdetails ON irrdetails.irrDetailId=clinicianirrdetails.irrDetailId
						WHERE schooldefaultquestiondetail.choiceAnswer= 63
						AND clinicianirrdetails.questionId=" . $schoolQuestionId . ") AS D
						
                FROM schooltopicdetails 
                INNER JOIN schooldefaultquestionmaster ON schooldefaultquestionmaster.`schoolQuestionId`
																=schooltopicdetails.`schoolQuestionId`
				LEFT JOIN schooldefaultquestiondetail ON schooldefaultquestionmaster.schoolQuestionId=
														schooldefaultquestiondetail.schoolQuestionId														
				LEFT JOIN clinicianirrdetails ON schooldefaultquestiondetail.schoolQuestionDId=
													clinicianirrdetails.schoolOptionValue
				
                WHERE schooltopicdetails.schoolSectionId=" . $schoolSectionId . " AND schooltopicdetails.schoolQuestionId
																					=" . $schoolQuestionId . "
				GROUP BY schooldefaultquestionmaster.schoolQuestionTitle
				ORDER BY `schooldefaultquestionmaster`.`sortOrder` ASC";
		}
		//   echo '<hr>'.$sql.'<hr>';
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetScoreForIRRReport($YesCountinperc, $Avalueinperc)
	{
		$objDB = new clsDB();
		$rows = "";

		$sql = "";

		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}
	function GetQuestionDetails($questionId)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT * FROM schooldefaultquestionmaster				
                WHERE schoolQuestionId=" . $questionId . "
				GROUP BY  schooldefaultquestionmaster.schoolQuestionTitle";
		//echo $sql;
		$rows = $objDB->GetDataRow($sql);
		return $rows;
		unset($objDB);
	}

	function GetQuestionDetailsToPreview($questionId)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT schooldefaultquestionmaster.*  ,schooldefaultquestiondetail.*
				FROM schooldefaultquestionmaster 
				LEFT JOIN schooldefaultquestiondetail ON schooldefaultquestionmaster.schoolQuestionId=
					schooldefaultquestiondetail.schoolQuestionId
                WHERE schooldefaultquestionmaster.schoolQuestionId=" . $questionId . "
				GROUP BY  schooldefaultquestionmaster.schoolQuestionTitle
				ORDER BY schooldefaultquestiondetail.schoolOptionText";
		//echo $sql;
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetQuestionOptionDetails($questionId)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT schooldefaultquestionmaster.*  ,schooldefaultquestiondetail.*
				FROM schooldefaultquestionmaster 
				INNER JOIN schooldefaultquestiondetail ON schooldefaultquestionmaster.schoolQuestionId=
					schooldefaultquestiondetail.schoolQuestionId
                WHERE schooldefaultquestionmaster.schoolQuestionId=" . $questionId .
			" ORDER BY schooldefaultquestiondetail.schoolOptionText";
		//echo $sql;
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function SaveCheckoff($checkoffId = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		if ($checkoffId > 0) {

			$sql = "UPDATE checkoff SET 
						schoolTopicId='" . ($this->schoolTopicId) . "',
						rotationId='" . ($this->rotationId) . "',
						studentId='" . ($this->studentId) . "', 
						evaluationDate='" . ($this->evaluationDate) . "', 
					    updatedBy='" . addslashes($this->createdBy) . "',		
						updatedDate = '" . (date("Y-m-d h:i:s")) . "' 
						 Where checkoffId= " . $checkoffId;
			//echo $sql;exit;
			$objDB->ExecuteQuery($sql);
		} else {

			$sql = "INSERT INTO checkoff (schoolId,studentId,checkoffDateTime,schoolTopicId,rotationId,
										clinicianId,evaluationDate,createdBy,createdDate) VALUES (
			'" . ($this->schoolId) . "',
			'" . ($this->studentId) . "',
			'" . ($this->checkoffDateTime) . "',
			'" . ($this->schoolTopicId) . "',
			'" . ($this->rotationId) . "',
			'" . ($this->clinicianId) . "',
			'" . ($this->evaluationDate) . "',
			'" . ($this->createdBy) . "',
			'" . (date("Y-m-d h:i:s")) . "')";
			//echo $sql;exit;
			$checkoffId = $objDB->ExecuteInsertQuery($sql);
		}
		unset($objDB);
		return $checkoffId;
	}

	function UpdateClinicianCheckoff($checkoffId = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		if ($checkoffId > 0) {

			$sql = "UPDATE checkoff SET 						
						evaluationDate='" . ($this->evaluationDate) . "', 
					    updatedBy='" . addslashes($this->updatedBy) . "',		
						updatedDate = '" . (date("Y-m-d h:i:s")) . "' 
						 Where checkoffId= " . $checkoffId;
			//echo $sql;exit;
			$objDB->ExecuteQuery($sql);
		}

		unset($objDB);
		return $checkoffId;
	}

	function SaveAdminCheckoff($checkoffId = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		if ($checkoffId > 0) {

			$sql = "UPDATE checkoff SET 
						schoolTopicId='" . ($this->schoolTopicId) . "',
						rotationId='" . ($this->rotationId) . "',
						studentId='" . ($this->studentId) . "', 
					    updatedBy='" . addslashes($this->createdBy) . "',		
						updatedDate = '" . (date("Y-m-d h:i:s")) . "' 
						 Where checkoffId= " . $checkoffId;
			//echo $sql.'<hr>';
			$objDB->ExecuteQuery($sql);
		}
		unset($objDB);
		return $checkoffId;
	}

	function SaveStudentCheckoff($checkoffId = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		if ($checkoffId > 0) {
			$sql = "UPDATE checkoff SET
						studentComment='" . ($this->studentComment) . "',												 
					    updatedBy='" . addslashes($this->createdBy) . "',
						student_evaluationDate='" . $this->student_evaluationDate . "',
						updatedDate = '" . (date("Y-m-d h:i:s")) . "' 
						 Where checkoffId= " . $checkoffId;
			//echo $sql.'<hr>';
			$objDB->ExecuteQuery($sql);
		}

		unset($objDB);
		return $checkoffId;
	}

	function SaveStudentCheckOffSignoffForApp($studentCheckoffMasterId, $StudentSignatureDate, $studentId, $StudentComment)
	{

		$objDB = new clsDB();
		$sql = '';
		if ($studentCheckoffMasterId > 0) {

			$sql = "UPDATE checkoff SET 						 
						
						student_evaluationDate='" . $StudentSignatureDate . "',
						 studentComment='" . $StudentComment . "',
						 updatedBy = '" . $studentId . "',
						 updatedDate = '" . (date("Y-m-d h:i:s")) . "' 
						 Where checkoffId = " . $studentCheckoffMasterId;
			//  echo 'update->'.$sql;exit;
			$objDB->ExecuteQuery($sql);
		}

		unset($objDB);
		return $studentCheckoffMasterId;
	}

	function SaveCheckOffDetail()
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "INSERT INTO checkoffdetail (checkoffId,schoolQuestionDId,schoolOptionValue,schoolOptionAnswerText,questionId) VALUES (
			'" . ($this->checkoffId) . "',
			'" . ($this->schoolQuestionDId) . "',
			'" . ($this->schoolOptionValue) . "',
			'" . ($this->schoolOptionAnswerText) . "',
			'" . ($this->questionId) . "')";
		// echo '<hr>'.$sql;exit;	
		$checkoffId = $objDB->ExecuteInsertQuery($sql);
		unset($objDB);
		return $checkoffId;
	}

	function SaveMansfieldCheckOffDetail()
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "INSERT INTO checkoffdetail (checkoffId,schoolQuestionDId,schoolOptionValue,schoolOptionAnswerText,questionId,comments) VALUES (
			'" . ($this->checkoffId) . "',
			'" . ($this->schoolQuestionDId) . "',
			'" . ($this->schoolOptionValue) . "',
			'" . ($this->schoolOptionAnswerText) . "',
			'" . ($this->questionId) . "',
			'" . ($this->comments) . "')";
		// echo '<hr>'.$sql;exit;	
		$checkoffId = $objDB->ExecuteInsertQuery($sql);
		unset($objDB);
		return $checkoffId;
	}

	function SaveSideStudentCheckoff($checkoffId = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		if ($checkoffId > 0) {
			$sql = "UPDATE checkoff SET
						studentComment='" . ($this->studentComment) . "',												 
					    updatedBy='" . addslashes($this->createdBy) . "',
						student_evaluationDate='" . $this->student_evaluationDate . "',
						evaluationDate='" . $this->evaluationDate . "',
						updatedDate = '" . (date("Y-m-d h:i:s")) . "' 
						 Where checkoffId= " . $checkoffId;
			// echo $sql.'<hr>';exit;
			$checkoffId = $objDB->ExecuteQuery($sql);
		} else {
			$sql = "INSERT INTO checkoff (schoolId,studentId,checkoffDateTime,schoolTopicId,rotationId,
										clinicianId,student_evaluationDate,studentComment,createdBy,createdDate,
										completion1stPreceptorId,completion2ndPreceptorId,completion3rdPreceptorId,completion4thPreceptorId,completion5thPreceptorId,isExternalPreceptorcheckoff) 
					VALUES (
					'" . ($this->schoolId) . "',
					'" . ($this->studentId) . "',
					'" . ($this->checkoffDateTime) . "',
					'" . ($this->schoolTopicId) . "',
					'" . ($this->rotationId) . "',
					'" . ($this->clinicianId) . "',
					'" . ($this->student_evaluationDate) . "',
					'" . ($this->studentComment) . "',
					'" . ($this->createdBy) . "',
					'" . (date("Y-m-d h:i:s")) . "',
					'" . ($this->completion1stPreceptorId) . "',
					'" . ($this->completion2ndPreceptorId) . "',
					'" . ($this->completion3rdPreceptorId) . "',
					'" . ($this->completion4thPreceptorId) . "',
					'" . ($this->completion5thPreceptorId) . "',
					'" . ($this->isExternalPreceptorcheckoff) . "'
					)";
			// echo 'insert->'.$sql;exit;
			$checkoffId = $objDB->ExecuteInsertQuery($sql);
		}

		unset($objDB);
		return $checkoffId;
	}

	function SaveStudentSideCheckOffDetail()
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "INSERT INTO checkoffdetail (checkoffId,schoolQuestionDId,schoolOptionValue,schoolOptionAnswerText,questionId) VALUES (
			'" . ($this->checkoffId) . "',
			'" . ($this->schoolQuestionDId) . "',
			'" . ($this->schoolOptionValue) . "',
			'" . ($this->schoolOptionAnswerText) . "',
			'" . ($this->questionId) . "')";
		//echo '<hr>'.$sql;exit;	
		$checkoffId = $objDB->ExecuteInsertQuery($sql);
		unset($objDB);
		return $checkoffId;
	}
	function SaveAdvanceCheckOffDetail()
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "INSERT INTO checkoffdetail 
				(checkoffId,schoolQuestionDId,schoolOptionValue,schoolOptionAnswerText,questionId,stage,completionDate,clinicianId,externalPreceptorId)
			VALUES (
			'" . ($this->checkoffId) . "',
			'" . ($this->schoolQuestionDId) . "',
			'" . ($this->schoolOptionValue) . "',
			'" . ($this->schoolOptionAnswerText) . "',
			'" . ($this->questionId) . "',
			'" . ($this->stage) . "',
			'" . ($this->completionDate) . "',
			'" . ($this->clinicianId) . "',
			'" . ($this->externalPreceptorId) . "'
			)
			";
		// echo '<hr>'.$sql;exit;
		$checkoffId = $objDB->ExecuteInsertQuery($sql);
		unset($objDB);
		return $checkoffId;
	}
	function GetCheckOffDetail($studentId, $rotationId, $currentSchoolId, $rankId, $createdStudentId = '', $isStudentEvalutionComplete = 0, $isSendToCanvas = '')
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT 
				checkoff.checkoffDateTime,checkoff.checkoffId,checkoff.schoolTopicId,				
				checkoff.student_evaluationDate,checkoff.evaluationDate,checkoff.rotationId,
				checkoff.school_evaluationDate ,checkoff.calculatedUsafScore ,checkoff.studentComment ,checkoff.createdStudentId,
				checkoff.completion1stPreceptorId,checkoff.is1stCompletionStatus,checkoff.isSendToCanvas,checkoff.canvasRecordId,checkoff.calculatedScorePercentage,checkoff.clinicianId,
				rotation.title as rotationname,clinician.firstName,clinician.lastName,
				student.firstName as studentfirstname,rankmaster.title as rankname, 
				student.lastName as studentlastname,student.studentId,
				courses.courseId,courses.title as coursename,schooltopicmaster.schooltitle,schooltopicmaster.checkoffTitleId,schooltopicmaster.procedureCategoryId,
				rotation.parentRotationId,rotation.locationId as rotationLocationId,courses.locationId
                 FROM checkoff  
				
				 LEFT JOIN rotation ON checkoff.rotationId=rotation.rotationId
				 LEFT JOIN student ON checkoff.studentId=student.studentId
				 INNER JOIN rankmaster ON rankmaster.rankId=student.rankId
				 LEFT JOIN schooltopicmaster ON schooltopicmaster.schoolTopicId=checkoff.schoolTopicId
				 LEFT JOIN courses ON rotation.courseId=courses.courseId
				 LEFT JOIN clinician ON clinician.clinicianId=checkoff.clinicianId";
		$sql .= " WHERE checkoff.schoolId=" . $currentSchoolId;

		if ($studentId) {
			$sql .= "  AND (checkoff.studentId=" . $studentId;
			if ($createdStudentId != '') {
				$sql .= " OR checkoff.createdStudentId=" . $createdStudentId;
			}

			$sql .= ")";
		}


		// if ($createdStudentId !='')
		// 	$sql.= "  OR checkoff.createdStudentId=" .$studentId." OR checkoff.studentId=" .$createdStudentId;

		if ($rotationId)
			$sql .= " AND checkoff.rotationId=" . $rotationId;

		if ($rankId)
			$sql .= " AND student.rankId=" . $rankId;

		if ($isStudentEvalutionComplete)
			$sql .= " AND (student_evaluationDate !='' OR student_evaluationDate !='0000-00-00 00:00:00') ";

		if ($isSendToCanvas != '')
			$sql .= " AND checkoff.isSendToCanvas = " . $isSendToCanvas;

		// $sql .= "  AND (
		// 		(CURDATE() BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate))
		// 		OR
		// 		(rotation.parentRotationId > 0
		// 			AND EXISTS (
		// 				SELECT 1
		// 				FROM rotation AS parent
		// 				WHERE parent.rotationId = rotation.parentRotationId
		// 				AND CURDATE() BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
		// 				AND rotation.isSchedule = 1 
		// 			)
		// 		)
		// 	) ";

		$sql .= " GROUP BY checkoff.checkoffId";

		$sql .= " ORDER BY checkoff.checkoffDateTime desc";
		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetCheckOffDetailToApp($studentId, $rotationId, $currentSchoolId, $rankId, $limitString, $searchText = '')
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT 
				checkoff.checkoffDateTime,checkoff.checkoffId,checkoff.schoolTopicId,checkoff.student_evaluationDate,
				checkoff.evaluationDate, checkoff.rotationId, checkoff.school_evaluationDate, rotation.title as rotationname,
				clinician.firstName,clinician.lastName,student.studentId,student.firstName as studentfirstname,rankmaster.title as rankname,
		        student.lastName as studentlastname,courses.courseId,courses.title as coursename,schooltopicmaster.schooltitle,
				courses.locationId as courseLocationId, checkoff.completion1stPreceptorId, checkoff.is1stCompletionStatus, checkoff.studentComment
                 FROM checkoff  
				 LEFT JOIN rotation ON checkoff.rotationId=rotation.rotationId
				 LEFT JOIN student ON checkoff.studentId=student.studentId
				 INNER JOIN rankmaster ON rankmaster.rankId=student.rankId
				 LEFT JOIN schooltopicmaster ON schooltopicmaster.schoolTopicId=checkoff.schoolTopicId
				 LEFT JOIN courses ON rotation.courseId=courses.courseId
				 LEFT JOIN clinician ON clinician.clinicianId=checkoff.clinicianId";
		$sql .= " WHERE checkoff.schoolId=" . $currentSchoolId;
		if ($studentId) {
			$sql .= "  AND checkoff.studentId=" . $studentId;
		}
		if ($rotationId) {
			$sql .= " AND checkoff.rotationId=" . $rotationId;
		}
		if ($rankId) {
			$sql .= " AND student.rankId=" . $rankId;
		}
		if ($searchText != "") {
			$sql .= " AND (schooltopicmaster.schooltitle LIKE '%" . $searchText . "%' OR rotation.title LIKE '%" . $searchText . "%') ";
		}

		// $sql .= "  AND (
		// 	(CURDATE() BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate))
		// 	OR
		// 	(rotation.parentRotationId > 0
		// 		AND EXISTS (
		// 			SELECT 1
		// 			FROM rotation AS parent
		// 			WHERE parent.rotationId = rotation.parentRotationId
		// 			AND CURDATE() BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
		// 			AND rotation.isSchedule = 1 
		// 		)
		// 	)
		// ) ";

		$sql .= " GROUP BY checkoff.checkoffId";

		$sql .= " ORDER BY checkoff.checkoffDateTime DESC " . $limitString;
		// echo $sql;exit;
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetCheckOffDetailForClinician($currentstudentId, $SchoolId, $rotationId, $rankId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT checkoff.*,rotation.rotationId,rotation.title as rotationname,clinician.firstName,
		        clinician.lastName,student.firstName as studentfirstname,rankmaster.title as rankname,
		        student.lastName as studentlastname,
				courses.courseId,courses.title as coursename,schooltopicmaster.schooltitle,schooltopicmaster.checkoffTitleId,rotation.parentRotationId,rotation.locationId as rotationLocationId,courses.locationId,schooltopicmaster.procedureCategoryId
                 FROM checkoff  
				 INNER JOIN rotation ON checkoff.rotationId=rotation.rotationId
				 INNER JOIN student ON checkoff.studentId=student.studentId
				 INNER JOIN rankmaster ON rankmaster.rankId=student.rankId
				 LEFT JOIN schooltopicmaster ON schooltopicmaster.schoolTopicId=checkoff.schoolTopicId
				 LEFT JOIN courses ON rotation.courseId=courses.courseId
				 LEFT JOIN clinician ON clinician.clinicianId=checkoff.clinicianId";

		if ($SchoolId) {
			$sql .= " WHERE checkoff.schoolId=" . $SchoolId . " ";
		}
		if ($rotationId) {
			$sql .= " AND checkoff.rotationId=" . $rotationId . " ";
		}

		if ($rankId > 0) {
			$sql .= " AND student.rankId=" . $rankId;
		}
		if ($currentstudentId > 0) {
			$sql .= " AND student.studentId=" . $currentstudentId;
		}

		$sql .= "  AND (
			(CURDATE() BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate))
			OR
			(rotation.parentRotationId > 0
				AND EXISTS (
					SELECT 1
					FROM rotation AS parent
					WHERE parent.rotationId = rotation.parentRotationId
					AND CURDATE() BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
					AND rotation.isSchedule = 1 
				)
			)
		) ";
		$sql .= " ORDER BY checkoff.checkoffDateTime desc";
		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetCheckOffDetailByClinician($currentstudentId, $SchoolId, $rotationId, $clinicianId, $rankId, $isActiveCheckoff = 0)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT checkoff.*,rotation.rotationId,rotation.title as rotationname,clinician.firstName,
		        clinician.lastName,student.firstName as studentfirstname,rankmaster.title as rankname,
		        student.lastName as studentlastname,
				courses.courseId,courses.title as coursename,schooltopicmaster.schooltitle,schooltopicmaster.checkoffTitleId,rotation.parentRotationId,rotation.locationId as rotationLocationId,courses.locationId,schooltopicmaster.procedureCategoryId
                 FROM checkoff  
				 LEFT JOIN rotation ON checkoff.rotationId=rotation.rotationId
				 LEFT JOIN student ON checkoff.studentId=student.studentId
				 INNER JOIN rankmaster ON rankmaster.rankId=student.rankId
				 LEFT JOIN schooltopicmaster ON schooltopicmaster.schoolTopicId=checkoff.schoolTopicId
				 LEFT JOIN courses ON rotation.courseId=courses.courseId
				 INNER JOIN clinician ON clinician.clinicianId=checkoff.clinicianId";


		$sql .= " WHERE checkoff.schoolId=" . $SchoolId . " AND checkoff.clinicianId=" . $clinicianId;

		if ($rotationId) {
			$sql .= " AND checkoff.rotationId=" . $rotationId . " ";
		}

		if ($rankId > 0) {
			$sql .= " AND student.rankId=" . $rankId;
		}
		if ($currentstudentId > 0) {
			$sql .= " AND student.studentId=" . $currentstudentId;
		}

		$sql .= "  AND (
			(CURDATE() BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate))
			OR
			(rotation.parentRotationId > 0
				AND EXISTS (
					SELECT 1
					FROM rotation AS parent
					WHERE parent.rotationId = rotation.parentRotationId
					AND CURDATE() BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
					AND rotation.isSchedule = 1 
				)
			)
		) ";

		$sql .= " ORDER BY checkoff.checkoffDateTime ASC";
		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	// Cliniacian Checkoff list For APP For FACULTY Standard & Military platforms
	function GetCheckOffDetailForClinicianForApp($currentstudentId, $SchoolId, $rotationId, $rankId, $limitString = '', $searchText = '', $isActiveCheckoff = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT checkoff.checkoffId,checkoff.schoolTopicId,checkoff.evaluationDate,checkoff.checkoffDateTime,checkoff.studentComment,checkoff.student_evaluationDate,checkoff.studentComment,checkoff.completion1stPreceptorId,checkoff.is1stCompletionStatus,rotation.rotationId,rotation.title as rotationname,clinician.firstName,
		        clinician.lastName,student.studentId,student.firstName as studentfirstname,rankmaster.title as rankname,
		        student.lastName as studentlastname,
				courses.courseId,courses.title as coursename,schooltopicmaster.schooltitle,schooltopicmaster.checkoffTitleId,rotation.parentRotationId,rotation.locationId as rotationLocationId,courses.locationId,extenal_preceptors.firstName AS preceptorFname,extenal_preceptors.lastName AS preceptorLname
                 FROM checkoff  
				 INNER JOIN rotation ON checkoff.rotationId=rotation.rotationId
				 INNER JOIN student ON checkoff.studentId=student.studentId	
				 INNER JOIN rankmaster ON rankmaster.rankId=student.rankId
				 LEFT JOIN schooltopicmaster ON schooltopicmaster.schoolTopicId=checkoff.schoolTopicId
				 INNER JOIN courses ON rotation.courseId=courses.courseId
				 LEFT JOIN clinician ON clinician.clinicianId=checkoff.clinicianId
				 LEFT JOIN extenal_preceptors ON extenal_preceptors.id=checkoff.completion1stPreceptorId";

		if ($SchoolId) {
			$sql .= " WHERE checkoff.schoolId=" . $SchoolId . " ";
		}
		if ($rotationId) {
			$sql .= " AND checkoff.rotationId=" . $rotationId . " ";
		}

		if ($rankId > 0) {
			$sql .= " AND student.rankId=" . $rankId;
		}
		if ($currentstudentId > 0) {
			$sql .= " AND student.studentId=" . $currentstudentId;
		}

		if ($searchText != "") {
			if ($rotationId > 0) {
				$sql .= " AND (CONCAT(student.firstName, ' ', student.lastName) LIKE '%" . $searchText . "%' OR schooltopicmaster.schooltitle LIKE '%" . $searchText . "%' )";
			} else {

				$sql .= " AND (rotation.title LIKE '%" . $searchText . "%' OR schooltopicmaster.schooltitle LIKE '%" . $searchText . "%' OR CONCAT(extenal_preceptors.firstName, ' ', extenal_preceptors.lastName) LIKE '%" . $searchText . "%')";
			}
		}

		if ($isActiveCheckoff > 0) {
			$sql .= "  AND (
				(CURDATE() BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate))
				OR
				(rotation.parentRotationId > 0
					AND EXISTS (
						SELECT 1
						FROM rotation AS parent
						WHERE parent.rotationId = rotation.parentRotationId
						AND CURDATE() BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
						AND rotation.isSchedule = 1 
					)
				)
			) ";
		}
		$sql .= " ORDER BY checkoff.checkoffDateTime DESC" . $limitString;
		// echo $sql;exit;	
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	// Cliniacian Checkoff list For APP For Preceptor/cordinatore Sandard and Military Platform
	function 	GetCheckOffDetailByClinicianForApp($currentstudentId, $SchoolId, $rotationId, $clinicianId, $rankId, $limitString, $searchText)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT checkoff.checkoffId,checkoff.schoolTopicId,checkoff.evaluationDate,checkoff.checkoffDateTime,checkoff.studentComment,checkoff.student_evaluationDate,checkoff.studentComment,checkoff.completion1stPreceptorId,checkoff.is1stCompletionStatus,rotation.rotationId,rotation.title as rotationname,clinician.firstName,
		        clinician.lastName,student.studentId,student.firstName as studentfirstname,rankmaster.title as rankname,
		        student.lastName as studentlastname,
				courses.courseId,courses.title as coursename,schooltopicmaster.schooltitle,schooltopicmaster.checkoffTitleId,rotation.parentRotationId,rotation.locationId as rotationLocationId,courses.locationId,extenal_preceptors.firstName AS preceptorFname,extenal_preceptors.lastName AS preceptorLname
                 FROM checkoff  
				 LEFT JOIN rotation ON checkoff.rotationId=rotation.rotationId
				 LEFT JOIN student ON checkoff.studentId=student.studentId
				 INNER JOIN rankmaster ON rankmaster.rankId=student.rankId
				 LEFT JOIN schooltopicmaster ON schooltopicmaster.schoolTopicId=checkoff.schoolTopicId
				 LEFT JOIN courses ON rotation.courseId=courses.courseId
				 INNER JOIN clinician ON clinician.clinicianId=checkoff.clinicianId
				 LEFT JOIN extenal_preceptors ON extenal_preceptors.id=checkoff.completion1stPreceptorId";


		$sql .= " WHERE checkoff.schoolId=" . $SchoolId . " AND checkoff.clinicianId=" . $clinicianId;

		if ($rotationId) {
			$sql .= " AND checkoff.rotationId=" . $rotationId . " ";
		}

		if ($rankId > 0) {
			$sql .= " AND student.rankId=" . $rankId;
		}
		if ($currentstudentId > 0) {
			$sql .= " AND student.studentId=" . $currentstudentId;
		}

		if ($searchText != "") {
			if ($rotationId > 0) {
				$sql .= " AND (CONCAT(student.firstName, ' ', student.lastName) LIKE '%" . $searchText . "%' OR schooltopicmaster.schooltitle LIKE '%" . $searchText . "%' )";
			} else {
				$sql .= " AND (rotation.title LIKE '%" . $searchText . "%' OR schooltopicmaster.schooltitle LIKE '%" . $searchText . "%' OR CONCAT(extenal_preceptors.firstName, ' ', extenal_preceptors.lastName) LIKE '%" . $searchText . "%')";
			}
		}

		$sql .= "  AND (
			(CURDATE() BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate))
			OR
			(rotation.parentRotationId > 0
				AND EXISTS (
					SELECT 1
					FROM rotation AS parent
					WHERE parent.rotationId = rotation.parentRotationId
					AND CURDATE() BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
					AND rotation.isSchedule = 1 
				)
			)
		) ";

		$sql .= " ORDER BY checkoff.checkoffDateTime DESC" . $limitString;
		// echo $sql;exit;
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}



	function DeleteCheckoffDetail($checkoffId)
	{
		$objDB = new clsDB();

		$sql = "DELETE  FROM checkoffdetail WHERE checkoffId=" . $checkoffId;
		//ECHO 'DELETE->'.$sql;EXIT;
		$result = $objDB->ExecuteQuery($sql);
		unset($objDB);
		return $result;
	}
	function DeleteCheckoff($checkoffId)
	{

		$result = "";
		if ($checkoffId > 0) {
			$objDB = new clsDB();
			$sql =  "DELETE checkoffdetail,checkoff
			        FROM checkoff
      				LEFT JOIN checkoffdetail ON checkoffdetail.checkoffId= checkoff.`checkoffId` 
				    WHERE checkoff.checkoffId = " . $checkoffId;
			$result = $objDB->ExecuteQuery($sql);
			// echo $sql;exit;
			unset($objDB);
		}
		return $result;
	}
	function DeleteAdvanceStudentCheckoff($checkoffId)
	{

		$result = "";
		if ($checkoffId > 0) {
			$objDB = new clsDB();
			$sql =  "DELETE checkoffdetail
			        FROM checkoffdetail      				
				    WHERE checkoffId = " . $checkoffId;
			$result = $objDB->ExecuteQuery($sql);
			// echo $sql;exit;
			unset($objDB);
		}
		return $result;
	}

	function GetAdvanceStudentCheckoffIdsBySection($sectionId, $checkoffId)
	{

		$result = "";
		if ($checkoffId > 0) {
			$objDB = new clsDB();
			$sql =  "select checkoffdetail.checkoffDId FROM checkoffdetail
					INNER JOIN schooltopicdetails ON checkoffdetail.schoolQuestionDId = schooltopicdetails.schoolQuestionId
					WHERE schooltopicdetails.schoolSectionId =" . $sectionId . " AND checkoffdetail.checkoffId = " . $checkoffId . " 
					GROUP BY schooltopicdetails.schoolQuestionId";

			$result = $objDB->GetResultset($sql);
			//  echo $sql;exit;
			unset($objDB);
		}
		return $result;
	}

	function DeleteAdvanceStudentCheckoffIdsByDetailId($checkoffDetailIds)
	{
		// echo 'hii';
		$result = "";
		if ($checkoffDetailIds > 0) {
			$objDB = new clsDB();
			$sql =  "delete FROM checkoffdetail WHERE checkoffDId in (" . $checkoffDetailIds . ")";
			$result = $objDB->ExecuteQuery($sql);
			unset($objDB);

			// echo $sql;exit;
		}

		return $result;
	}

	function DeleteAdvanceStudentCheckoffIdsByCheckoffId($checkoffId)
	{
		// echo 'hii';
		$result = "";
		if ($checkoffId > 0) {
			$objDB = new clsDB();
			$sql =  "DELETE FROM checkoffdetail WHERE checkoffId=" . $checkoffId;
			$result = $objDB->ExecuteQuery($sql);
			unset($objDB);

			// echo $sql;exit;
		}

		return $result;
	}
	function GetCheckOff($checkoffId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT rotation.parentRotationId,rotation.locationId as rotationLocationId, courses.title AS Coursename,courses.locationId, checkoff.* from checkoff 
		LEFT JOIN rotation ON checkoff.`rotationId` = rotation.`rotationId` 
		LEFT JOIN courses ON rotation.courseId=courses.courseId
		WHERE checkoff.checkoffId=" . $checkoffId;
		$rows = $objDB->GetDataRow($sql);
		// echo $sql;
		return $rows;
		unset($objDB);
	}

	function GetCheckOffDetails($checkoffId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "select checkoff.*,rotation.title As rotationName, concat(clinician.firstName, ' ', clinician.lastName) AS clinicianName from checkoff 
		LEFT JOIN clinician ON checkoff.clinicianId = clinician.clinicianId
		LEFT JOIN rotation ON checkoff.`rotationId` = rotation.`rotationId` 
		WHERE checkoff.checkoffId=" . $checkoffId;
		$rows = $objDB->GetDataRow($sql);
		// echo $sql;
		return $rows;
		unset($objDB);
	}

	function GetCheckOffByRotation($rotationId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT checkoffId from checkoff WHERE rotationId=" . $rotationId;

		//echo $sql;
		$rows = $objDB->GetDataRow($sql);
		return $rows;
		unset($objDB);
	}

	function GetProcedurCountBySingleStudent($studentId = 0, $rotationId = 0, $procedureCategoryId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT schooltopicmaster.*,rotation.title as rotationname,checkoff.procedurePointsAssist,
				checkoff.procedurePointsObserve,
		        checkoff.procedurePointsPerform,student.firstName as studentfirstname,rankmaster.title 
				as rankname,
				student.lastName as studentlastname,checkoff.procedureDate, schooltopicmaster.schooltitle,
				checkoff.checkoffDateTime,
				checkoff.checkoffId,checkoff.procedureName,checkoff.schoolTopicId,checkoff.procedureCategoryId,
				 procedurecountmaster.*
				
				FROM checkoff  
				LEFT JOIN schooltopicmaster ON checkoff.schoolTopicId=schooltopicmaster.schoolTopicId
				LEFT JOIN  procedurecountmaster ON schooltopicmaster.proceduteCountId=procedurecountmaster.proceduteCountId
				LEFT JOIN rotation ON checkoff.rotationId=rotation.rotationId
				LEFT JOIN student ON checkoff.studentId=student.studentId
				LEFT JOIN rankmaster ON rankmaster.rankId=student.rankId
			     
				";
		$sql .= "  WHERE  checkoff.studentId=" . $studentId . " 
						AND schooltopicmaster.procedureCategoryId=" . $procedureCategoryId;

		$sql .= "  AND  checkoff.rotationId=" . $rotationId . " 
						AND schooltopicmaster.procedureCategoryId=" . $procedureCategoryId;



		$sql .= "  GROUP BY procedurecountmaster.proceduteCountName,rotation.title,student.studentId";
		//echo $sql;
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	//Updated By Sunil
	function GetProcedurCountForClinician($studentId = 0, $rotationId = 0, $procedureCategoryId = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = " SELECT procedurecountmaster.*,studentprocedutecount.*,checkoff.checkoffId as ID,
				procedurecategory.*
				FROM studentprocedutecount
				LEFT JOIN procedurecountmaster ON procedurecountmaster.proceduteCountId
													=studentprocedutecount.proceduteCountTopicId
				LEFT JOIN checkoff ON   studentprocedutecount.checkoffId=checkoff.checkoffId
				LEFT JOIN procedurecategory ON procedurecategory.procedureCategoryId=procedurecountmaster.procedureCategoryId";
		$sql .= " WHERE studentprocedutecount.procedureCountId !=0 ";
		if ($studentId > 0) {
			$sql .= " AND studentprocedutecount.studentId=" . $studentId;
		}
		if ($procedureCategoryId > 0) {
			$sql .= " AND procedurecountmaster.procedureCategoryId=" . $procedureCategoryId;
		}
		if ($rotationId > 0) {
			$sql .= " AND studentprocedutecount.rotationId=" . $rotationId;
		}

		//echo $sql;
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetAdvanceProcedurCountForClinician($studentId = 0, $rotationId = 0, $procedureCategoryId = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = " SELECT advanceprocedurecountmaster.advanceProceduteCountId as proceduteCountId, advanceprocedurecountmaster.advanceProceduteCountName as proceduteCountName,
					advanceprocedurecountmaster.advanceProcedureCountsCode as procedureCountsCode,studentprocedutecount.*,checkoff.checkoffId as ID,
				procedurecategory.*
				FROM studentprocedutecount
				LEFT JOIN advanceprocedurecountmaster ON advanceprocedurecountmaster.advanceProceduteCountId
													=studentprocedutecount.proceduteCountTopicId
				LEFT JOIN checkoff ON   studentprocedutecount.checkoffId=checkoff.checkoffId
				LEFT JOIN procedurecategory ON procedurecategory.procedureCategoryId=advanceprocedurecountmaster.advanceProcedureCategoryId";
		$sql .= " WHERE studentprocedutecount.procedureCountId !=0 ";
		if ($studentId > 0) {
			$sql .= " AND studentprocedutecount.studentId=" . $studentId;
		}
		if ($procedureCategoryId > 0) {
			$sql .= " AND advanceprocedurecountmaster.advanceProcedureCategoryId=" . $procedureCategoryId;
		}
		if ($rotationId > 0) {
			$sql .= " AND studentprocedutecount.rotationId=" . $rotationId;
		}

		//echo $sql;
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}


	function GetProcedurCount($studentId = 0, $rotationId = 0, $procedureCategoryId = 0)
	{
		$objDB = new clsDB();
		$rows = "";

		$sql = "SELECT procedurecountmaster . *,studentprocedutecount.checkoffId
		FROM procedurecountmaster
		INNER JOIN studentprocedutecount ON procedurecountmaster.proceduteCountId = studentprocedutecount.proceduteCountTopicId
		AND studentprocedutecount.procedurePointsPerformTotal !=0
		AND studentprocedutecount.procedureTotal !=0
		WHERE procedureCategoryId =" . $procedureCategoryId;

		if ($studentId > 0) {
			$sql .= " AND studentprocedutecount.studentId=" . $studentId;
		}
		if ($rotationId > 0) {
			$sql .= "  AND studentprocedutecount.rotationId=" . $rotationId;
		}
		$sql .= " GROUP by procedurecountmaster.proceduteCountId ORDER BY procedureCountsCode ASC ";

		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetAdvanceProcedurCount($studentId = 0, $rotationId = 0, $procedureCategoryId = 0)
	{
		$objDB = new clsDB();
		$rows = "";

		$sql = "SELECT advanceprocedurecountmaster.advanceProceduteCountId as proceduteCountId, advanceprocedurecountmaster.advanceProceduteCountName as proceduteCountName,advanceprocedurecountmaster.advanceProcedureCountsCode as procedureCountsCode,studentprocedutecount.checkoffId
		FROM advanceprocedurecountmaster
		INNER JOIN studentprocedutecount ON advanceprocedurecountmaster.advanceProceduteCountId = studentprocedutecount.proceduteCountTopicId
		AND studentprocedutecount.procedurePointsPerformTotal !=0
		AND studentprocedutecount.procedureTotal !=0
		WHERE advanceProcedureCategoryId =" . $procedureCategoryId;

		if ($studentId > 0) {

			$sql .= " AND studentprocedutecount.studentId=" . $studentId;
		}
		if ($rotationId > 0) {

			$sql .= "  AND studentprocedutecount.rotationId=" . $rotationId;
		}


		$sql .= " ORDER BY advanceProcedureCountsCode ASC";

		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetProcedurCountByStudent($studentId = 0, $rotationId = 0, $procedureCategoryId = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT schooltopicmaster.*,rotation.title as rotationname,student.firstName as studentfirstname,rankmaster.title 
				as rankname,
				student.lastName as studentlastname, schooltopicmaster.schooltitle,
				checkoff.checkoffDateTime,
				checkoff.checkoffId,checkoff.schoolTopicId,
				procedurecountmaster.*
				
				FROM checkoff  
				LEFT JOIN schooltopicmaster ON checkoff.schoolTopicId=schooltopicmaster.schoolTopicId	
				LEFT JOIN procedurecountmaster ON schooltopicmaster.proceduteCountId=
													procedurecountmaster.proceduteCountId
				LEFT JOIN rotation ON checkoff.rotationId=rotation.rotationId
				LEFT JOIN student ON checkoff.studentId=student.studentId
				LEFT JOIN rankmaster ON rankmaster.rankId=student.rankId";

		if ($studentId > 0) {

			$sql .= "  where  checkoff.studentId=" . $studentId . " 
						AND schooltopicmaster.procedureCategoryId=" . $procedureCategoryId;
		}
		if ($rotationId > 0) {

			$sql .= "  AND  checkoff.rotationId=" . $rotationId . " 
						AND schooltopicmaster.procedureCategoryId=" . $procedureCategoryId;
		}

		//echo $sql;
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetProcedureCategory($currentSchoolId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT * FROM procedurecategory";
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}
	function GetAdvanceProcedureCategory($currentSchoolId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT * FROM advanceprocedurecategory";
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetCheckOffDetailsBySchool($schoolId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT checkoff.*,rotation.rotationId,rotation.courseId,
				rotation.title as rotationname
				,student.studentId,student.firstName,student.lastName,student.rankId,
				 rankmaster.rankId,rankmaster.title AS Ranktitle
                 FROM checkoff 
				 INNER JOIN schools ON checkoff.schoolId=schools.schoolId
				 INNER JOIN rotation ON checkoff.rotationId=rotation.rotationId
				 INNER JOIN student ON checkoff.studentId=student.studentId
				 INNER JOIN rankmaster ON student.rankId=rankmaster.rankId				 
				 WHERE checkoff.schoolId=" . $schoolId . " 
				 AND  rankmaster.title !='Graduate' and rankmaster.title !='Dropout'
				 GROUP BY student.studentId  order by student.firstName asc LIMIT 10";
		//echo $sql;
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}



	function GetDoneCheckoffs($studentId, $rotationId, $courseId)
	{
		$objDB = new clsDB();
		$checkoffrows = "";
		$sql = "SELECT 
				 COUNT(checkoff.schoolTopicId) AS Submitedtopic
				FROM
				 checkoff
				 LEFT JOIN coursetopicdetail ON
					coursetopicdetail.schoolTopicId = checkoff.schoolTopicId
					 AND coursetopicdetail.courseId = " . $courseId . "
				WHERE
				 studentId =" . $studentId . " AND rotationId =" . $rotationId;

		$checkoffrows = $objDB->GetDataRow($sql);
		return $checkoffrows;
		unset($objDB);
	}
	function GetLeftCheckoffs($studentId, $rotationId, $courseId)
	{
		$objDB = new clsDB();
		$checkoffrows = "";
		$sql = " SELECT COUNT(schooltopicmaster.schoolTopicId) AS NotSubmitedtopic 
		FROM schooltopicmaster 
		INNER JOIN coursetopicdetail ON schooltopicmaster.schoolTopicId=coursetopicdetail.schoolTopicId
		INNER JOIN courses ON coursetopicdetail.courseId=courses.courseId
		INNER JOIN rotation ON rotation.courseId=courses.courseId 
		WHERE rotation.rotationId=" . $rotationId . "
		AND NOT EXISTS (select checkoff.schoolTopicId from checkoff where schooltopicmaster.schoolTopicId =checkoff.schoolTopicId AND checkoff.studentId =" . $studentId . " AND checkoff.rotationId =" . $rotationId . ")";

		$checkoffrows = $objDB->GetDataRow($sql);
		return $checkoffrows;
		unset($objDB);
	}

	function CalculateCheckoffStudentScores($checkoffId)
	{
		$objDB = new clsDB();
		$score = 0;
		$sql = "SELECT AVG(schooldefaultquestiondetail.choiceAnswer) AS CheckoffScore FROM checkoffdetail 
				INNER JOIN checkoff ON checkoffdetail.checkoffId=checkoff.checkoffId  
				INNER JOIN schooldefaultquestiondetail  ON checkoffdetail.schoolOptionValue = schooldefaultquestiondetail.schoolQuestionDId
				INNER JOIN schooltopicdetails ON schooldefaultquestiondetail.schoolQuestionId = schooltopicdetails.schoolQuestionId  
				INNER JOIN schoolsectionmaster ON schooltopicdetails.schoolSectionId= schoolsectionmaster.schoolSectionId 
				WHERE schoolsectionmaster.scoreSection=1 AND checkoff.checkoffId=" . $checkoffId;
		$score = $objDB->GetSingleFieldValue($sql);
		// echo $sql;exit;
		unset($objDB);
		return $score;
	}

	function GetCHeckoffStudentScore($checkoffId)
	{
		$objDB = new clsDB();
		$score = 0;
		$sql = "SELECT calculatedScore AS CheckoffScore FROM checkoff WHERE checkoffId=" . $checkoffId;
		$score = $objDB->GetSingleFieldValue($sql);
		unset($objDB);

		// //This is for all existing checkoff records who has not calculated score remove this once aal checkoff score calculate
		// if(floatval($score) == 0)
		// {
		// 	$score =$this->CalculateCheckoffStudentScores($checkoffId);
		// 	$this->UpdateCalculatedCheckoffScore($checkoffId,$score); 
		// }
		// echo '<br> score '.$score;
		// echo '<br> query '.$sql;
		return $score;
	}

	//get assigned topics to course	/ rotation
	function GetAssignedTopic($courseId)
	{
		$objDB = new clsDB();
		$topicsvalues = "";
		$sql = "SELECT count(schoolTopicId) AS AssignedTopic FROM coursetopicdetail			 
				 WHERE courseId=" . $courseId;
		$topicsvalues = $objDB->GetDataRow($sql);
		return $topicsvalues;

		unset($objDB);
	}
	function GetCheckOffDetailsByClinician($schoolId, $clinicianId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT checkoff.*,rotation.rotationId,rotation.title as rotationname,
				student.studentId,student.firstName,student.lastName,
				rankmaster.title AS Ranktitle,
				rotation.courseId,rotation.rotationId
                 FROM checkoff				 
				 LEFT JOIN rotation ON checkoff.rotationId=rotation.rotationId				 
				 LEFT JOIN clinicianhospitalsite ON rotation.hospitalSiteId=clinicianhospitalsite.hospitalSiteId
				 LEFT JOIN clinician ON clinicianhospitalsite.clinicianId=clinician.clinicianId
				 LEFT JOIN student ON checkoff.studentId=student.studentId
				 LEFT JOIN rankmaster ON student.rankId=rankmaster.rankId				 
				 WHERE checkoff.schoolId=" . $schoolId . " AND checkoff.clinicianId=" . $clinicianId . "
				 and rankmaster.title !='Graduate' and rankmaster.title !='Dropout' ";
		$sql .= " GROUP BY student.studentId LIMIT 0 , 10 ";
		//echo $sql;
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}
	function GetStudentCheckOffDetailsForreport(
		$schoolId,
		$rotationId = 0,
		$subcborotation = 0,
		$studentId = '',
		$rankId = 0,
		$evaluator = 0,
		$clinicianId = 0,
		$locationId = 0,
		$hospitalSiteId = 0,
		$startdate = '',
		$endtdate = '',
		$checkoffTopic,
		$ascdesc,
		$sordorder,
		$cbosemester,
		$start,
		$length,
		$studentIdArr = ''
	) {
		$totalFiltered = 0;

		$studentIdArray =  $studentId ? unserialize($studentId) : [];
		$studentIds =  count($studentIdArray) ? implode(",", $studentIdArray) : '';
		$rotationId = str_replace(" ", ",", $rotationId);
		$cbosemester = str_replace(" ", ",", $cbosemester);
		$subcborotation = str_replace(" ", ",", $subcborotation);

		$objDB = new clsDB();
		$query = "";
		$sql = "SELECT checkoff.*, student.studentId, student.rankId, 
				student.firstName,student.lastName,rankmaster.rankId,rankmaster.title AS Rankname,
				clinician.clinicianId,clinician.firstName AS clinicianfname,clinician.lastName  
				AS clinicianlname,rotation.rotationId,rotation.title AS rotationname,
				schools.schoolId,schooltopicmaster.schoolTopicId,schooltopicmaster.checkoffTitleId,schooltopicmaster.schooltitle, hospitalsites.title AS hospitalsite
				FROM checkoff
				INNER JOIN schools ON checkoff.schoolId=schools.schoolId
				INNER JOIN schooltopicmaster ON checkoff.schoolTopicId=schooltopicmaster.schoolTopicId
				INNER JOIN student ON checkoff.studentId=student.studentId
				INNER JOIN rankmaster ON student.rankId=rankmaster.rankId
				INNER JOIN clinician ON checkoff.clinicianId=clinician.clinicianId
				INNER JOIN rotation ON checkoff.rotationId=rotation.rotationId
				INNER JOIN courses ON courses.`courseId` = rotation.`courseId`
				LEFT join hospitalsites on hospitalsites.hospitalSiteId = rotation.hospitalSiteId
				INNER JOIN semestermaster ON courses.`semesterId` = semestermaster.`semesterId`";
		$sql .= " WHERE schools.schoolId= " . $schoolId;

		if ($checkoffTopic > 0)
			$sql .= " AND schooltopicmaster.schoolTopicId =" . $checkoffTopic;
		if ($cbosemester > 0)
			$sql .= " AND semestermaster.semesterId IN ($cbosemester)";

		if ($subcborotation > 0)
			$sql .= " AND (rotation.rotationId IN ($subcborotation) OR rotation.rotationId IN ($rotationId) )";
		else if ($rotationId > 0)
			$sql .= " AND rotation.rotationId IN ($rotationId)";
		if ($studentIds > 0)
			$sql .= " AND student.studentId IN ($studentIds)";
		if ($rankId > 0)
			$sql .= " AND rankmaster.rankId=" . $rankId;
		if ($evaluator > 0) {
			$sql .= " AND checkoff.clinicianId =" . $evaluator;
		}
		if ($hospitalSiteId > 0) {
			$sql .= " AND hospitalsites.hospitalSiteId  =" . $hospitalSiteId;
		}
		if ($startdate != '' || $endtdate != '')
			$sql .= " AND date(checkoff.createdDate) >= '" . $startdate . "'
					AND date(checkoff.createdDate) <= '" . $endtdate . "' ";

		if ($ascdesc && $sordorder == 1)
			$sql .= "  ORDER BY student.firstName " . $ascdesc;
		elseif ($ascdesc && $sordorder == 2)
			$sql .= "  ORDER BY rankmaster.title " . $ascdesc;
		elseif ($ascdesc && $sordorder == 5)
			$sql .= "  ORDER BY rotation.title " . $ascdesc;

		else if ($ascdesc && $sordorder == 14)
			$sql .= "  ORDER BY student.firstName " . $ascdesc;

		else if ($ascdesc && $sordorder == 15)
			$sql .= "  ORDER BY student.lastName " . $ascdesc;

		else if ($ascdesc && $sordorder == 8)
			$sql .= "  ORDER BY hospitalsites.title " . $ascdesc;
		else
			$sql .= "  ORDER BY schooltopicmaster.checkoffTitleId ";

		$query = $objDB->GetResultset($sql);
		if ($query)
			$totalFiltered = mysqli_num_rows($query); // when there is a search parameter then we have to modify total number filtered rows as per search result. 

		if ($length > 0)
			$sql .= " LIMIT " . $start . " ," . $length . " ";

		// echo $sql;
		// exit;	
		$query = $objDB->GetResultset($sql);
		unset($objDB);
		return $query;
	}

	function GetStudentPreceptorCheckOffDetailsForreport(
		$schoolId,
		$rotationId = 0,
		$subcborotation = 0,
		$studentId = '',
		$rankId = 0,
		$evaluator = 0,
		$locationId = 0,
		$hospitalSiteId = 0,
		$startdate = '',
		$endtdate = '',
		$checkoffTopic,
		$ascdesc,
		$sordorder,
		$cbosemester,
		$start,
		$length,
		$studentIdArr = ''
	) {
		$totalFiltered = 0;

		$studentIdArray =  $studentId ? unserialize($studentId) : [];
		$studentIds =  count($studentIdArray) ? implode(",", $studentIdArray) : '';
		$rotationId = str_replace(" ", ",", $rotationId);
		$cbosemester = str_replace(" ", ",", $cbosemester);
		$subcborotation = str_replace(" ", ",", $subcborotation);

		$objDB = new clsDB();
		$query = "";
		$sql = "SELECT checkoff.*, student.studentId, student.rankId, student.firstName,student.lastName,rankmaster.rankId,rankmaster.title AS Rankname, 
				extenal_preceptors.id,extenal_preceptors.firstName AS preceptorfname,extenal_preceptors.lastName AS preceptorlname, extenal_preceptors.mobile_num AS preceptorMobile, TIME_FORMAT(extenal_preceptors.preceptorhours, '%H:%i') AS preceptorhours,
				rotation.rotationId,rotation.title AS rotationname, schools.schoolId,schooltopicmaster.schoolTopicId,schooltopicmaster.checkoffTitleId,schooltopicmaster.schooltitle FROM checkoff 
				INNER JOIN schools ON checkoff.schoolId=schools.schoolId 
				INNER JOIN schooltopicmaster ON checkoff.schoolTopicId=schooltopicmaster.schoolTopicId 
				INNER JOIN student ON checkoff.studentId=student.studentId 
				INNER JOIN rankmaster ON student.rankId=rankmaster.rankId 
				INNER JOIN extenal_preceptors ON checkoff.completion1stPreceptorId = extenal_preceptors.id
				INNER JOIN rotation ON checkoff.rotationId=rotation.rotationId 
				INNER JOIN courses ON courses.`courseId` = rotation.`courseId` 
				INNER JOIN semestermaster ON courses.`semesterId` = semestermaster.`semesterId`";
		$sql .= " WHERE schools.schoolId= " . $schoolId;

		if ($checkoffTopic > 0)
			$sql .= " AND schooltopicmaster.schoolTopicId =" . $checkoffTopic;
		if ($cbosemester > 0)
			$sql .= " AND semestermaster.semesterId IN ($cbosemester)";

		if ($subcborotation > 0)
			$sql .= " AND (rotation.rotationId IN ($subcborotation) OR rotation.rotationId=" . $rotationId . " )";
		else if ($rotationId > 0)
			$sql .= " AND rotation.rotationId=" . $rotationId;
		if ($studentIds > 0)
			$sql .= " AND student.studentId IN ($studentIds)";
		if ($rankId > 0)
			$sql .= " AND rankmaster.rankId=" . $rankId;
		if ($studentIdArr != '') {
			$sql .= " AND student.studentId IN ($studentIdArr)";
		}
		if ($startdate != '' || $endtdate != '')
			$sql .= " AND date(checkoff.createdDate) >= '" . $startdate . "'
					AND date(checkoff.createdDate) <= '" . $endtdate . "' ";

		if ($ascdesc && $sordorder == 1)
			$sql .= "  ORDER BY student.firstName " . $ascdesc;
		elseif ($ascdesc && $sordorder == 2)
			$sql .= "  ORDER BY rankmaster.title " . $ascdesc;
		elseif ($ascdesc && $sordorder == 5)
			$sql .= "  ORDER BY rotation.title " . $ascdesc;

		else if ($ascdesc && $sordorder == 14)
			$sql .= "  ORDER BY student.firstName " . $ascdesc;

		else if ($ascdesc && $sordorder == 15)
			$sql .= "  ORDER BY student.lastName " . $ascdesc;
		else
			$sql .= "  ORDER BY checkoff.checkoffDateTime DESC";

		$query = $objDB->GetResultset($sql);
		if ($query)
			$totalFiltered = mysqli_num_rows($query); // when there is a search parameter then we have to modify total number filtered rows as per search result. 

		if ($length > 0)
			$sql .= " LIMIT " . $start . " ," . $length . " ";

		// echo $sql;
		// exit;	
		$query = $objDB->GetResultset($sql);
		unset($objDB);
		return $query;
	}

	function GetStudentPreceptorCheckOffDetailsForreportForAdvance(
		$schoolId,
		$rotationId = 0,
		$subcborotation = 0,
		$studentId = '',
		$rankId = 0,
		$evaluator = 0,
		$locationId = 0,
		$hospitalSiteId = 0,
		$startdate = '',
		$endtdate = '',
		$checkoffTopic,
		$ascdesc,
		$sordorder,
		$cbosemester,
		$start,
		$length,
		$studentIdArr = ''
	) {
		$totalFiltered = 0;

		$studentIdArray =  $studentId ? unserialize($studentId) : [];
		$studentIds =  count($studentIdArray) ? implode(",", $studentIdArray) : '';
		$rotationId = str_replace(" ", ",", $rotationId);
		$cbosemester = str_replace(" ", ",", $cbosemester);
		$subcborotation = str_replace(" ", ",", $subcborotation);

		$objDB = new clsDB();
		$query = "";
		$sql = "SELECT checkoff.*, student.studentId, student.rankId, student.firstName,student.lastName,rankmaster.rankId,rankmaster.title AS Rankname, 
				rotation.rotationId,rotation.title AS rotationname, schools.schoolId,schooltopicmaster.schoolTopicId,schooltopicmaster.checkoffTitleId,schooltopicmaster.schooltitle FROM checkoff 
				INNER JOIN schools ON checkoff.schoolId=schools.schoolId 
				INNER JOIN schooltopicmaster ON checkoff.schoolTopicId=schooltopicmaster.schoolTopicId 
				INNER JOIN student ON checkoff.studentId=student.studentId 
				INNER JOIN rankmaster ON student.rankId=rankmaster.rankId 
				INNER JOIN rotation ON checkoff.rotationId=rotation.rotationId 
				INNER JOIN courses ON courses.`courseId` = rotation.`courseId` 
				INNER JOIN semestermaster ON courses.`semesterId` = semestermaster.`semesterId`";
		$sql .= " WHERE schools.schoolId= " . $schoolId;

		if ($checkoffTopic > 0)
			$sql .= " AND schooltopicmaster.schoolTopicId =" . $checkoffTopic;
		if ($cbosemester > 0)
			$sql .= " AND semestermaster.semesterId IN ($cbosemester)";

		if ($subcborotation > 0)
			$sql .= " AND (rotation.rotationId IN ($subcborotation) OR rotation.rotationId=" . $rotationId . " )";
		else if ($rotationId > 0)
			$sql .= " AND rotation.rotationId=" . $rotationId;
		if ($studentIds > 0)
			$sql .= " AND student.studentId IN ($studentIds)";
		if ($rankId > 0)
			$sql .= " AND rankmaster.rankId=" . $rankId;
		if ($studentIdArr != '') {
			$sql .= " AND student.studentId IN ($studentIdArr)";
		}
		if ($startdate != '' || $endtdate != '')
			$sql .= " AND date(checkoff.createdDate) >= '" . $startdate . "'
					AND date(checkoff.createdDate) <= '" . $endtdate . "' ";

		if ($ascdesc && $sordorder == 1)
			$sql .= "  ORDER BY student.firstName " . $ascdesc;
		elseif ($ascdesc && $sordorder == 2)
			$sql .= "  ORDER BY rankmaster.title " . $ascdesc;
		elseif ($ascdesc && $sordorder == 5)
			$sql .= "  ORDER BY rotation.title " . $ascdesc;

		else if ($ascdesc && $sordorder == 14)
			$sql .= "  ORDER BY student.firstName " . $ascdesc;

		else if ($ascdesc && $sordorder == 15)
			$sql .= "  ORDER BY student.lastName " . $ascdesc;
		else
			$sql .= "  ORDER BY checkoff.checkoffDateTime DESC";

		$query = $objDB->GetResultset($sql);
		// if ($query)
		// 	$totalFiltered = mysqli_num_rows($query); // when there is a search parameter then we have to modify total number filtered rows as per search result. 

		if ($length > 0)
			$sql .= " LIMIT " . $start . " ," . $length . " ";

		// echo $sql;
		// exit;	
		$query = $objDB->GetResultset($sql);
		unset($objDB);
		return $query;
	}

	function GetCheckOffByCoursesForreport(
		$schoolId,
		$rotationId = 0,
		$subcborotation = 0,
		$studentId,
		$rankId = 0,
		$evaluator = 0,
		$locationId = 0,
		$hospitalSiteId = 0,
		$startdate = '',
		$endtdate = '',
		$checkoffTopic,
		$ascdesc,
		$sordorder,
		$cbosemester,
		$courseTopicCompletion = '',
		$courseId = 0
	) {

		$studentIds = is_array($studentId) ? implode(",", $studentId) : '';
		// $studentIds =  $studentId ? implode(',', $studentId) : '';
		$rotationId = str_replace(" ", ",", $rotationId);
		$cbosemester = str_replace(" ", ",", $cbosemester);
		$subcborotation = str_replace(" ", ",", $subcborotation);

		$objDB = new clsDB();
		$query = "";

		//Not Completed Course Topics
		if ($courseTopicCompletion == 2 || $courseTopicCompletion == '') {

			$sql = "SELECT student.firstName,student.lastName,checkoff.checkoffDateTime,checkoff.calculatedScore, checkoff.calculatedUsafScore, checkoff.checkoffId,schooltopicmaster.schooltitle AS schooltitle,schooltopicmaster.checkoffTitleId, 
			rotation.parentRotationId,rotation.locationId as rotationLocationId, courses.title AS courseTitle,courses.locationId,
			IF(checkoff.schoolTopicId IS NOT NULL,'Completed','Remaining') as Status ,clinician.firstName as clinicianfname,clinician.lastName as clinicianlname,rotation.title as rotationTitle
			FROM schooltopicmaster 
			INNER JOIN coursetopicdetail ON schooltopicmaster.schoolTopicId=coursetopicdetail.schoolTopicId 
			INNER JOIN rotation ON rotation.courseId=coursetopicdetail.courseId
			INNER JOIN courses ON rotation.courseId=courses.courseId
			INNER JOIN rotationdetails ON rotation.rotationId=rotationdetails.rotationId 
			INNER JOIN student ON student.studentId=rotationdetails.studentId 
			LEFT JOIN checkoff ON schooltopicmaster.schoolTopicId=checkoff.schoolTopicId AND checkoff.rotationId=rotationdetails.rotationId AND checkoff.studentId=rotationdetails.studentId
			LEFT JOIN clinician ON clinician.clinicianId = checkoff.clinicianId
			WHERE checkoff.checkoffDateTime IS NULL AND schooltopicmaster.schoolId=" . $schoolId;
			if ($checkoffTopic > 0)
				$sql .= " AND schooltopicmaster.schoolTopicId =" . $checkoffTopic;
			if ($cbosemester > 0)
				$sql .= " AND semestermaster.semesterId IN ($cbosemester)";

			if ($subcborotation > 0)
				$sql .= " AND (rotation.rotationId IN ($subcborotation) OR rotation.rotationId=" . $rotationId . " )";
			else if ($rotationId > 0)
				$sql .= " AND rotation.rotationId=" . $rotationId;
			if ($studentIds > 0)
				$sql .= " AND student.studentId IN ($studentIds)";
			if ($rankId > 0)
				$sql .= " AND rankmaster.rankId=" . $rankId;
			if ($courseId > 0)
				$sql .= " AND courses.courseId=" . $courseId;
		}

		// For Bothe Completed Or Not Completed
		if ($courseTopicCompletion == '')
			$sql .= " UNION ";

		//Completed Course Topics
		if ($courseTopicCompletion == 1 || $courseTopicCompletion == '') {
			if ($courseTopicCompletion == 1)
				$sql = '';

			$sql .= "SELECT student.firstName,student.lastName,checkoff.checkoffDateTime,checkoff.calculatedScore, checkoff.calculatedUsafScore, checkoff.checkoffId,schooltopicmaster.schooltitle AS schooltitle,schooltopicmaster.checkoffTitleId, 
			rotation.parentRotationId,rotation.locationId as rotationLocationId, courses.title AS courseTitle,courses.locationId,
			IF(checkoff.schoolTopicId IS NOT NULL,'Completed','Remaining') as Status,clinician.firstName as clinicianfname,clinician.lastName as clinicianlname,rotation.title as rotationTitle
			FROM schooltopicmaster 
			INNER JOIN coursetopicdetail ON schooltopicmaster.schoolTopicId=coursetopicdetail.schoolTopicId 
			INNER JOIN rotation ON rotation.courseId=coursetopicdetail.courseId
			INNER JOIN courses ON rotation.courseId=courses.courseId
			INNER JOIN rotationdetails ON rotation.rotationId=rotationdetails.rotationId 
			INNER JOIN student ON student.studentId=rotationdetails.studentId 
			LEFT JOIN checkoff ON schooltopicmaster.schoolTopicId=checkoff.schoolTopicId AND checkoff.rotationId=rotationdetails.rotationId AND checkoff.studentId=rotationdetails.studentId
			LEFT JOIN clinician ON clinician.clinicianId = checkoff.clinicianId
			WHERE schooltopicmaster.schoolId= " . $schoolId;
			if ($checkoffTopic > 0)
				$sql .= " AND schooltopicmaster.schoolTopicId =" . $checkoffTopic;
			if ($cbosemester > 0)
				$sql .= " AND semestermaster.semesterId IN ($cbosemester)";

			if ($subcborotation > 0)
				$sql .= " AND (rotation.rotationId IN ($subcborotation) OR rotation.rotationId=" . $rotationId . " )";
			else if ($rotationId > 0)
				$sql .= " AND rotation.rotationId=" . $rotationId;
			if ($studentIds > 0)
				$sql .= " AND student.studentId IN ($studentIds)";
			if ($rankId > 0)
				$sql .= " AND rankmaster.rankId=" . $rankId;
			if ($startdate != '' || $endtdate != '')
				$sql .= " AND  (date(checkoff.checkoffDateTime) >= '" . $startdate . "'
						AND date(checkoff.checkoffDateTime) <= '" . $endtdate . "') ";
			if ($courseId > 0)
				$sql .= " AND courses.courseId=" . $courseId;
		}

		// $sql ="SELECT checkoff.evaluationDate,checkoff.calculatedScore, checkoff.calculatedUsafScore, checkoff.checkoffId,schooltopicmaster.schooltitle AS Topics,schooltopicmaster.checkoffTitleId, 
		// rotation.parentRotationId,rotation.locationId as rotationLocationId, courses.title AS Coursename,courses.locationId,
		// IF(checkoff.schoolTopicId IS NOT NULL,'Completed','Remaining') as Status
		//  FROM schooltopicmaster
		//  LEFT JOIN coursetopicdetail ON schooltopicmaster.schoolTopicId=coursetopicdetail.schoolTopicId 
		//  LEFT JOIN rotation ON rotation.courseId=coursetopicdetail.courseId
		//  LEFT JOIN courses ON rotation.courseId=courses.courseId
		//  LEFT JOIN checkoff ON schooltopicmaster.schoolTopicId=checkoff.schoolTopicId ";

		// if($subcborotation > 0)
		// 	$sql .=" AND (checkoff.rotationId IN ($subcborotation) OR checkoff.rotationId=" . $rotationId." )";	 
		// else if($rotationId > 0)
		// 	$sql .=" AND checkoff.rotationId=" . $rotationId;

		// $sql .=" WHERE checkoff.schoolId= ". $schoolId;

		// if($checkoffTopic > 0)
		// 	$sql .=" AND schooltopicmaster.schoolTopicId =".$checkoffTopic;
		// if($cbosemester > 0)
		// 	$sql .=" AND semestermaster.semesterId IN ($cbosemester)";

		// if($subcborotation > 0)
		// 	$sql .=" AND (rotation.rotationId IN ($subcborotation) OR rotation.rotationId=" . $rotationId." )";	 
		// else if($rotationId > 0)
		// 	$sql .=" AND rotation.rotationId=" . $rotationId;
		// if($studentIds > 0)
		// 	$sql .=" AND student.studentId IN ($studentIds)";
		// if($rankId > 0)
		// 	$sql .=" AND rankmaster.rankId=" . $rankId;
		// if($startdate !='' || $endtdate !='')
		// 	$sql .=" OR  (date(checkoff.createdDate) >= '".$startdate."'
		// 			AND date(checkoff.createdDate) <= '".$endtdate."') ";

		if ($ascdesc && $sordorder == 14)
			$sql .= "  ORDER BY firstName " . $ascdesc;
		elseif ($ascdesc && $sordorder == 15)
			$sql .= "  ORDER BY lastName " . $ascdesc;
		// elseif($ascdesc && $sordorder == 2)
		// 	$sql .="  ORDER BY rankmaster.title " . $ascdesc;
		elseif ($ascdesc && $sordorder == 5)
			$sql .= "  ORDER BY rotationTitle " . $ascdesc;
		elseif ($ascdesc && $sordorder == 13)
			$sql .= "  ORDER BY courseTitle " . $ascdesc;

		// else if($ascdesc && $sordorder == 14)
		// 	$sql .="  ORDER BY student.firstName " . $ascdesc;

		// else if($ascdesc && $sordorder == 15)
		// 	$sql .="  ORDER BY student.lastName " . $ascdesc;

		// else
		// 	$sql .="  ORDER BY schooltopicmaster.checkoffTitleId ";	
		// echo $sql;
		// exit;
		$query = $objDB->GetResultset($sql);

		unset($objDB);
		return $query;
	}

	function GetStudentProcedureDetailsForreport(
		$schoolId,
		$rotationId = 0,
		$studentId = 0,
		$rankId = 0,
		$evaluator = 0,
		$locationId = 0,
		$hospitalSiteId = 0,
		$startdate = '',
		$endtdate = '',
		$ascdesc,
		$sortorder,
		$cbosemester,
		$subcborotation
	) {
		$cbosemester = str_replace(" ", ",", $cbosemester);
		$subcborotation = str_replace(" ", ",", $subcborotation);
		$studentIds = $studentId ? implode(',', unserialize($studentId)) : 0;

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT procedurecountmaster.proceduteCountName,procedurecountmaster.procedureCountsCode,studentprocedutecount.procedureDate,studentprocedutecount.procedurePointsAssist,studentprocedutecount.procedurePointsObserve,studentprocedutecount.procedurePointsPerform, student.studentId, student.rankId, 
					student.firstName,student.lastName,rankmaster.rankId,rankmaster.title AS Rankname,
					clinician.clinicianId,clinician.firstName AS clinicianfname,clinician.lastName  
					AS clinicianlname,rotation.rotationId,rotation.title AS rotationname,
					schools.schoolId
					FROM studentprocedutecount
					LEFT JOIN procedurecountmaster ON studentprocedutecount.proceduteCountTopicId=procedurecountmaster.proceduteCountId 
					LEFT JOIN student ON studentprocedutecount.studentId=student.studentId 
					LEFT JOIN schools ON student.schoolId=schools.schoolId 
					INNER JOIN rankmaster ON student.rankId=rankmaster.rankId 
					LEFT JOIN rotation ON studentprocedutecount.rotationId=rotation.rotationId 
					LEFT JOIN courses ON courses.`courseId` = rotation.`courseId`
					LEFT JOIN semestermaster ON courses.`semesterId` = semestermaster.`semesterId`
					LEFT JOIN clinicianhospitalsite ON rotation.hospitalSiteId=clinicianhospitalsite.hospitalSiteId
					LEFT JOIN clinician ON clinicianhospitalsite.clinicianId=clinician.clinicianId";
		$sql .= " WHERE student.schoolId= " . $schoolId . " AND studentprocedutecount.isActivitySheet = 0 ";
		if ($cbosemester > 0)
			$sql .= " AND semestermaster.semesterId IN ($cbosemester)";

		if ($studentIds > 0)
			$sql .= " AND student.studentId IN ($studentIds)";
		if ($rankId > 0)
			$sql .= " AND rankmaster.rankId=" . $rankId;
		if ($startdate != '' || $endtdate != '')
			$sql .= " AND date(studentprocedutecount.procedureDate) >= '" . $startdate . "'
							AND date(studentprocedutecount.procedureDate) <= '" . $endtdate . "' ";
		if ($subcborotation > 0)
			$sql .= " AND (rotation.rotationId IN ($subcborotation) OR rotation.rotationId=" . $rotationId . " )";
		else if ($rotationId > 0)
			$sql .= " AND rotation.rotationId=" . $rotationId;
		$sql .= " GROUP BY studentprocedutecount.procedureCountId ";

		if ($ascdesc && $sortorder == 1)
			$sql .= "  ORDER BY student.firstName " . $ascdesc;

		else if ($ascdesc && $sortorder == 5)
			$sql .= "  ORDER BY rotation.title " . $ascdesc;

		else if ($ascdesc && $sortorder == 2)
			$sql .= "  ORDER BY rankmaster.title " . $ascdesc;

		else if ($ascdesc)
			$sql .= "  ORDER BY student.firstName " . $ascdesc;
		else if ($ascdesc && $sortorder == 10)
			$sql .= "  ORDER BY rotation.`title` " . $ascdesc;
		else if ($ascdesc && $sortorder == 14)
			$sql .= "  ORDER BY student.firstName " . $ascdesc;

		else if ($ascdesc && $sortorder == 15)
			$sql .= "  ORDER BY student.lastName " . $ascdesc;


		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetStudentProcedureTotalForreport($studentId, $startdate = '', $endtdate = '')
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT sum(procedurePointsAssist) as procedurePointsAssistTotal ,sum(procedurePointsObserve) as procedurePointsObserveTotal,sum(procedurePointsPerform) as procedurePointsPerformTotal
							FROM studentprocedutecount  WHERE  studentId = $studentId";

		if ($startdate != '' || $endtdate != '')
			$sql .= " AND date(procedureDate) >= '" . $startdate . "'AND date(procedureDate) <= '" . $endtdate . "' ";

		$sql .= "  GROUP BY studentId ";
		// 			echo $sql; 
		$rows = $objDB->GetDataRow($sql);
		unset($objDB);
		return $rows;
	}

	function GetStudentProcedureSummaryForreport($schoolId, $rotationId = 0, $studentId = 0, $rankId = 0, $startdate = '', $endtdate = '', $ascdesc, $sortorder, $subcborotation)
	{

		$subcborotation = str_replace(" ", ",", $subcborotation);

		$studentIds =  $studentId ? implode(',', $studentId) : '';

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT student.studentId, student.rankId, 
					student.firstName,student.lastName,rankmaster.rankId,rankmaster.title AS Rankname,
					schools.schoolId,rotation.title AS rotationname, studentprocedutecount.rotationId 
					FROM studentprocedutecount
					LEFT JOIN student ON studentprocedutecount.studentId=student.studentId 
					LEFT JOIN schools ON student.schoolId=schools.schoolId 
					inner JOIN rotation ON studentprocedutecount.rotationId=rotation.rotationId
					INNER JOIN rankmaster ON student.rankId=rankmaster.rankId ";
		$sql .= " WHERE student.schoolId= " . $schoolId . " AND studentprocedutecount.isActivitySheet = 0 ";

		if ($rotationId > 0 && $subcborotation > 0)
			$sql .= " AND (rotation.rotationId=" . $rotationId . " OR rotation.rotationId IN ($subcborotation))";
		elseif ($rotationId > 0)
			$sql .= " AND rotation.rotationId=" . $rotationId;
		elseif ($subcborotation > 0)
			$sql .= " AND rotation.rotationId IN ($subcborotation)";

		if ($studentIds > 0)
			$sql .= " AND student.studentId IN ($studentIds)";
		if ($rankId > 0)
			$sql .= " AND rankmaster.rankId=" . $rankId;

		if ($startdate != '' || $endtdate != '')
			$sql .= " AND date(studentprocedutecount.procedureDate) >= '" . $startdate . "'
						AND date(studentprocedutecount.procedureDate) <= '" . $endtdate . "' ";

		$sql .= " group by studentprocedutecount.studentId, rotation.rotationId ";

		if ($ascdesc && $sortorder == 1)
			$sql .= "  ORDER BY student.firstName " . $ascdesc;
		else if ($ascdesc && $sortorder == 2)
			$sql .= "  ORDER BY rankmaster.title " . $ascdesc;


		else if ($ascdesc && $sortorder == 10 || $sortorder == 5)
			$sql .= "  ORDER BY rotation.`title` " . $ascdesc;
		else if ($ascdesc && $sortorder == 14)
			$sql .= "  ORDER BY student.firstName " . $ascdesc;

		else if ($ascdesc && $sortorder == 15)
			$sql .= "  ORDER BY student.lastName " . $ascdesc;

		else
			$sql .= "  ORDER BY student.firstName " . $ascdesc;
		// echo $sql;
		// exit;	
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetStandardProceudreNameByStudent($studentId, $startdate = '', $endtdate = '', $ascdesc, $sordorder = 0, $rotationId = 0)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT  studentprocedutecount.proceduteCountTopicId,procedurecountmaster.proceduteCountName,procedurecountmaster.procedureCountsCode
					FROM studentprocedutecount
					LEFT JOIN procedurecountmaster ON studentprocedutecount.proceduteCountTopicId=procedurecountmaster.proceduteCountId ";
		$sql .= " WHERE studentprocedutecount.studentId= " . $studentId . " AND studentprocedutecount.isActivitySheet = 0 ";

		if ($startdate != '' || $endtdate != '')
			$sql .= " AND date(studentprocedutecount.procedureDate) >= '" . $startdate . "'
							AND date(studentprocedutecount.procedureDate) <= '" . $endtdate . "' ";
		if ($rotationId)
			$sql .= " AND studentprocedutecount.rotationId = " . $rotationId;

		$sql .= " group by studentprocedutecount.proceduteCountTopicId";

		if ($sordorder == 16)
			$sql .= " ORDER by advanceprocedurecountmaster.advanceProceduteCountName " . $ascdesc;

		$rows = $objDB->GetResultset($sql);
		// echo $sql;
		unset($objDB);
		return $rows;
	}

	function GetAdvanceProceudreNameByStudent($studentId, $startdate = '', $endtdate = '', $ascdesc, $sordorder = 0, $rotationId = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT  studentprocedutecount.proceduteCountTopicId,advanceprocedurecountmaster.advanceProceduteCountName as proceduteCountName,
					advanceprocedurecountmaster.advanceProcedureCountsCode as procedureCountsCode
					FROM studentprocedutecount
					LEFT JOIN advanceprocedurecountmaster ON studentprocedutecount.proceduteCountTopicId=advanceprocedurecountmaster.advanceProceduteCountId ";
		$sql .= " WHERE studentprocedutecount.studentId= " . $studentId . " AND studentprocedutecount.isActivitySheet = 0 ";

		if ($startdate != '' || $endtdate != '')
			$sql .= " AND date(studentprocedutecount.procedureDate) >= '" . $startdate . "'
				AND date(studentprocedutecount.procedureDate) <= '" . $endtdate . "' ";

		if ($rotationId)
			$sql .= " AND studentprocedutecount.rotationId = " . $rotationId;

		$sql .= " group by studentprocedutecount.proceduteCountTopicId";

		if ($sordorder == 16)
			$sql .= " ORDER by advanceprocedurecountmaster.advanceProceduteCountName " . $ascdesc;

		$rows = $objDB->GetResultset($sql);
		// echo $sql;

		unset($objDB);
		return $rows;
	}

	function GetUsafProceudreNameByStudent($studentId, $startdate = '', $endtdate = '', $ascdesc, $sordorder = 0, $rotationId = 0)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT studentprocedutecount.proceduteCountTopicId,usafprocedurecountmaster.proceduteCountName,usafprocedurecountmaster.procedureCountsCode FROM studentprocedutecount 
					LEFT JOIN usafprocedurecountmaster ON studentprocedutecount.proceduteCountTopicId=usafprocedurecountmaster.proceduteCountId ";
		$sql .= " WHERE studentprocedutecount.studentId= " . $studentId . " AND studentprocedutecount.isActivitySheet = 0 ";

		if ($startdate != '' || $endtdate != '')
			$sql .= " AND date(studentprocedutecount.procedureDate) >= '" . $startdate . "'
							AND date(studentprocedutecount.procedureDate) <= '" . $endtdate . "' ";

		if ($rotationId)
			$sql .= " AND studentprocedutecount.rotationId = " . $rotationId;

		$sql .= " group by studentprocedutecount.proceduteCountTopicId";

		if ($sordorder == 16)
			$sql .= " ORDER by advanceprocedurecountmaster.advanceProceduteCountName " . $ascdesc;

		$rows = $objDB->GetResultset($sql);
		// echo $sql;
		unset($objDB);
		return $rows;
	}
	function GetStudentProcedureTotalById($studentId, $proceduteCountTopicId)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT sum(procedurePointsAssist) as procedurePointsAssist ,sum(procedurePointsObserve) as procedurePointsObserve, sum(procedurePointsPerform) as procedurePointsPerform 
					FROM studentprocedutecount";
		$sql .= " WHERE studentId= " . $studentId . " AND proceduteCountTopicId=" . $proceduteCountTopicId . " AND studentprocedutecount.isActivitySheet = 0 ";

		$rows = $objDB->GetDataRow($sql);
		unset($objDB);
		return $rows;
	}

	function GetStudentProcedureDetailsForreportForAdvance(
		$schoolId,
		$rotationId = 0,
		$studentId,
		$rankId = 0,
		$evaluator = 0,
		$locationId = 0,
		$hospitalSiteId = 0,
		$startdate = '',
		$endtdate = '',
		$ascdesc,
		$sortorder,
		$cbosemester,
		$subcborotation
	) {
		$cbosemester = str_replace(" ", ",", $cbosemester);
		$subcborotation = str_replace(" ", ",", $subcborotation);
		$studentIds =  $studentId ? implode(',', unserialize($studentId)) : '';

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT advanceprocedurecountmaster.advanceProceduteCountId as proceduteCountId , advanceprocedurecountmaster.advanceProceduteCountName as proceduteCountName,
					advanceprocedurecountmaster.advanceProcedureCountsCode as procedureCountsCode ,studentprocedutecount.procedureDate,studentprocedutecount.procedurePointsAssist,studentprocedutecount.procedurePointsObserve,studentprocedutecount.procedurePointsPerform, student.studentId, student.rankId, 
					student.firstName,student.lastName,rankmaster.rankId,rankmaster.title AS Rankname,
					clinician.clinicianId,clinician.firstName AS clinicianfname,clinician.lastName  
					AS clinicianlname,rotation.rotationId,rotation.title AS rotationname,
					schools.schoolId
					FROM studentprocedutecount
					LEFT JOIN advanceprocedurecountmaster ON studentprocedutecount.proceduteCountTopicId = advanceprocedurecountmaster.advanceProceduteCountId 
					LEFT JOIN student ON studentprocedutecount.studentId=student.studentId 
					LEFT JOIN schools ON student.schoolId=schools.schoolId 
					INNER JOIN rankmaster ON student.rankId=rankmaster.rankId 
					LEFT JOIN rotation ON studentprocedutecount.rotationId=rotation.rotationId 
					LEFT JOIN courses ON courses.`courseId` = rotation.`courseId`
					LEFT JOIN semestermaster ON courses.`semesterId` = semestermaster.`semesterId`
					LEFT JOIN clinicianhospitalsite ON rotation.hospitalSiteId=clinicianhospitalsite.hospitalSiteId
					LEFT JOIN clinician ON clinicianhospitalsite.clinicianId=clinician.clinicianId";
		$sql .= " WHERE student.schoolId= " . $schoolId . " AND studentprocedutecount.isActivitySheet = 0 ";
		if ($cbosemester > 0)
			$sql .= " AND semestermaster.semesterId IN ($cbosemester)";
		if ($rotationId > 0)
			$sql .= " AND rotation.rotationId=" . $rotationId;
		if ($subcborotation > 0)
			$sql .= " AND rotation.rotationId IN ($subcborotation)";
		if ($studentIds > 0)
			$sql .= " AND student.studentId IN ($studentIds)";

		if ($rankId > 0)
			$sql .= " AND rankmaster.rankId=" . $rankId;
		if ($startdate != '' || $endtdate != '')
			$sql .= " AND date(studentprocedutecount.procedureDate) >= '" . $startdate . "'
							AND date(studentprocedutecount.procedureDate) <= '" . $endtdate . "' ";

		$sql .= " GROUP BY studentprocedutecount.procedureCountId ";

		if ($ascdesc && $sortorder == 1)
			$sql .= "  ORDER BY student.firstName " . $ascdesc;

		else if ($ascdesc && $sortorder == 5)
			$sql .= "  ORDER BY rotation.title " . $ascdesc;

		else if ($ascdesc && $sortorder == 2)
			$sql .= "  ORDER BY rankmaster.title " . $ascdesc;

		else if ($ascdesc)
			$sql .= "  ORDER BY student.firstName " . $ascdesc;
		else if ($ascdesc && $sortorder == 10)
			$sql .= "  ORDER BY rotation.`title` " . $ascdesc;
		else if ($ascdesc && $sortorder == 14)
			$sql .= "  ORDER BY student.firstName " . $ascdesc;

		else if ($ascdesc && $sortorder == 15)
			$sql .= "  ORDER BY student.lastName " . $ascdesc;

		// 			echo $sql; exit;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetStudentProcedureDetailsForreportForUsaf(
		$schoolId,
		$rotationId = 0,
		$studentId = 0,
		$rankId = 0,
		$evaluator = 0,
		$locationId = 0,
		$hospitalSiteId = 0,
		$startdate = '',
		$endtdate = '',
		$ascdesc,
		$sortorder,
		$cbosemester,
		$subcborotation
	) {
		$cbosemester = str_replace(" ", ",", $cbosemester);
		$subcborotation = str_replace(" ", ",", $subcborotation);

		$studentIds = $studentId ? implode(',', unserialize($studentId)) : '';

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT usafprocedurecountmaster.proceduteCountName,usafprocedurecountmaster.procedureCountsCode,studentprocedutecount.procedureDate,studentprocedutecount.procedurePointsAssist,studentprocedutecount.procedurePointsObserve,studentprocedutecount.procedurePointsPerform, student.studentId, student.rankId, 
					student.firstName,student.lastName,rankmaster.rankId,rankmaster.title AS Rankname,
					clinician.clinicianId,clinician.firstName AS clinicianfname,clinician.lastName  
					AS clinicianlname,rotation.rotationId,rotation.title AS rotationname,
					schools.schoolId
					FROM studentprocedutecount
					LEFT JOIN usafprocedurecountmaster ON studentprocedutecount.proceduteCountTopicId=usafprocedurecountmaster.proceduteCountId 
					LEFT JOIN student ON studentprocedutecount.studentId=student.studentId 
					LEFT JOIN schools ON student.schoolId=schools.schoolId 
					INNER JOIN rankmaster ON student.rankId=rankmaster.rankId 
					LEFT JOIN rotation ON studentprocedutecount.rotationId=rotation.rotationId 
					LEFT JOIN courses ON courses.`courseId` = rotation.`courseId`
					LEFT JOIN semestermaster ON courses.`semesterId` = semestermaster.`semesterId`
					LEFT JOIN clinicianhospitalsite ON rotation.hospitalSiteId=clinicianhospitalsite.hospitalSiteId
					LEFT JOIN clinician ON clinicianhospitalsite.clinicianId=clinician.clinicianId";
		$sql .= " WHERE student.schoolId= " . $schoolId . " AND studentprocedutecount.isActivitySheet = 0 ";
		if ($cbosemester > 0)
			$sql .= " AND semestermaster.semesterId IN ($cbosemester)";
		if ($rotationId > 0)
			$sql .= " AND rotation.rotationId=" . $rotationId;
		if ($subcborotation > 0)
			$sql .= " AND rotation.rotationId IN ($subcborotation)";
		if ($studentIds > 0)
			$sql .= " AND student.studentId IN ($studentIds)";
		if ($rankId > 0)
			$sql .= " AND rankmaster.rankId=" . $rankId;
		if ($startdate != '' || $endtdate != '')
			$sql .= " AND date(studentprocedutecount.procedureDate) >= '" . $startdate . "'
							AND date(studentprocedutecount.procedureDate) <= '" . $endtdate . "' ";

		$sql .= " GROUP BY studentprocedutecount.procedureCountId ";

		if ($ascdesc && $sortorder == 1)
			$sql .= "  ORDER BY student.firstName " . $ascdesc;

		else if ($ascdesc && $sortorder == 5)
			$sql .= "  ORDER BY rotation.title " . $ascdesc;

		else if ($ascdesc && $sortorder == 2)
			$sql .= "  ORDER BY rankmaster.title " . $ascdesc;

		else if ($ascdesc)
			$sql .= "  ORDER BY student.firstName " . $ascdesc;
		else if ($ascdesc && $sortorder == 10)
			$sql .= "  ORDER BY rotation.`title` " . $ascdesc;
		else if ($ascdesc && $sortorder == 14)
			$sql .= "  ORDER BY student.firstName " . $ascdesc;

		else if ($ascdesc && $sortorder == 15)
			$sql .= "  ORDER BY student.lastName " . $ascdesc;

		// echo $sql; exit;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function AddProcedurCount($checkoffId)
	{
		if ($checkoffId > 0) {
			$objDB = new clsDB();
			$sql = "UPDATE studentprocedutecount SET " .
				"procedurePointsAssist = '" . addslashes($this->procedurePointsAssist) . "',
					procedurePointsObserve = '" . addslashes($this->procedurePointsObserve) . "',
					procedurePointsPerform = '" . addslashes($this->procedurePointsPerform) . "'
             		Where checkoffId  =" . $checkoffId;

			$result = $objDB->ExecuteQuery($sql);
			unset($objDB);
		}
	}

	function GetStudentCheckoffCounts($schoolId, $studentId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT  count(*) as CheckoffCount
				FROM checkoff
				 WHERE  checkoff.schoolId=" . $schoolId . " AND checkoff.studentId=" . $studentId;
		// echo $sql;
		$rows = $objDB->GetDataRow($sql);
		return $rows;
		unset($objDB);
	}

	function GetCountCheckoffByStudent($studentId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT  count(rotationId) as  CheckoffCount FROM  checkoff
				 WHERE studentId=" . $studentId . " group by rotationId";
		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetAllCheckoffTopic($currentSchoolId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT * FROM schooltopicmaster WHERE schoolId=" . $currentSchoolId;
		$sql .= " ORDER BY `checkoffTitleId` ASC";
		//echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}
	function GetSelectedClinicianForAdvanceCheckoff($SchoolId, $QuestionDId, $checkoffId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT checkoff.*,checkoffdetail.clinicianId
			FROM checkoffdetail
			INNER JOIN checkoff ON checkoffdetail.checkoffId=checkoff.checkoffId
				WHERE schoolId=" . $SchoolId . " 
				AND checkoffdetail.schoolQuestionDId=" . $QuestionDId . " AND checkoffdetail.checkoffId=" . $checkoffId;

		//echo $sql;exit;
		$rows = $objDB->GetDataRow($sql);
		unset($objDB);
		return $rows;
	}
	function GetSelectedDateForAdvanceCheckoff($SchoolId, $QuestionDId, $checkoffId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT checkoffdetail.completionDate AS SingleDate
			FROM checkoffdetail
			INNER JOIN checkoff ON checkoffdetail.checkoffId=checkoff.checkoffId
				WHERE schoolId=" . $SchoolId . " 
				AND checkoffdetail.schoolQuestionDId=" . $QuestionDId . " AND checkoffdetail.checkoffId=" . $checkoffId;

		//echo $sql;
		$rows = $objDB->GetDataRow($sql);
		unset($objDB);
		return $rows;
	}
	function GetMultipleSelectedClinicianForAdvanceCheckoff($SchoolId, $QuestionDId, $checkoffId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = " SELECT checkoffdetail.clinicianId,checkoffdetail.externalPreceptorId, date(checkoffdetail.completionDate) as completionDate, checkoffdetail.schoolOptionValue 
				FROM checkoffdetail 
				INNER JOIN checkoff ON checkoffdetail.checkoffId=checkoff.checkoffId
				LEFT JOIN schooldefaultquestiondetail ON checkoffdetail.schoolOptionValue=schooldefaultquestiondetail.schoolQuestionDId
			  WHERE schoolId=" . $SchoolId . " 
			  AND checkoffdetail.schoolQuestionDId=" . $QuestionDId . " 
			  AND checkoffdetail.checkoffId=" . $checkoffId . " order by checkoffDId desc";

		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}
	function GetAllCheckoffTopicsBySchool($SchoolId, $checkoffType = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT * FROM schooltopicmaster WHERE schoolId=" . $SchoolId . " AND schooltopicmaster.status =1 ";
		if ($checkoffType)
			$sql .= " AND length(SUBSTRING_INDEX(checkoffTitleId, '.', 1)) = $checkoffType ";

		$sql .= " GROUP BY `schooltopicmaster`.`checkoffTitleId` ASC";
		if ($SchoolId == 127)
			$sql .= " ORDER BY `schooltopicmaster`.`schooltitle` ASC";
		else
			$sql .= " ORDER BY `schooltopicmaster`.`checkoffTitleId`,`schooltopicmaster`.`schooltitle` ASC";
		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}
	function GetTopicStatus($studentId, $SchoolId, $rotationId, $courseId = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT checkoff.evaluationDate,checkoff.calculatedScore,checkoff.isExternalPreceptorcheckoff, checkoff.calculatedUsafScore, checkoff.checkoffId,schooltopicmaster.schooltitle AS Topics,schooltopicmaster.checkoffTitleId, 
			rotation.parentRotationId,rotation.locationId as rotationLocationId, courses.title AS Coursename,courses.locationId,
			IF(checkoff.schoolTopicId IS NOT NULL,'Completed','Remaining') as Status
			 FROM schooltopicmaster
			 INNER JOIN coursetopicdetail ON schooltopicmaster.schoolTopicId=coursetopicdetail.schoolTopicId 
			 LEFT JOIN rotation ON rotation.courseId=coursetopicdetail.courseId
			 INNER JOIN courses ON coursetopicdetail.courseId=courses.courseId
			 LEFT JOIN checkoff ON schooltopicmaster.schoolTopicId=checkoff.schoolTopicId ";

		if ($studentId > 0) {
			$sql .= " AND checkoff.studentId=" . $studentId;
		}
		if ($rotationId > 0) {
			$sql .= " AND checkoff.rotationId=" . $rotationId;
		}

		$sql .= " WHERE schooltopicmaster.schoolId=" . $SchoolId;
		if ($rotationId > 0) {
			$sql .= " AND rotation.rotationId=" . $rotationId;
		}

		if ($courseId > 0) {
			$sql .= " AND courses.courseId=" . $courseId;
		}



		$sql .= " GROUP BY `schooltopicmaster`.`schoolTopicId`";
		$sql .= " ORDER BY `schooltopicmaster`.`schooltitle` ASC";
		// echo $sql;
		// exit;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}
	function GetCheckoffDetailsByServerSide($studentId, $rotationId, $rankId, $currentSchoolId, $start, $length, $searchExpression, $orderByColumn, $orderByColumnDir)
	{
		//$requestData= $_REQUEST;

		if ($orderByColumn == '') {
			$orderByColumn = 'checkoffDateTime';
			$orderByColumnDir = 'desc';
		}
		$objDB = new clsDB();
		$rows = "";

		$sql = "SELECT 
				checkoff.checkoffDateTime,
				checkoff.checkoffId,
				checkoff.schoolTopicId,				
				checkoff.student_evaluationDate,
				checkoff.evaluationDate,
				checkoff.rotationId,
				checkoff.school_evaluationDate ,
				rotation.title as rotationname,
				clinician.firstName,
		        clinician.lastName,
				CONCAT(clinician.firstName, ' ', clinician.lastName) as fullname,
				student.firstName as studentfirstname,
				rankmaster.title as rankname,
		        student.lastName as studentlastname,
				courses.courseId,
				courses.title as coursename,
				schooltopicmaster.schooltitle

                 FROM checkoff  
				 LEFT JOIN rotation ON checkoff.rotationId=rotation.rotationId
				 LEFT JOIN student ON checkoff.studentId=student.studentId
				 left JOIN rankmaster ON rankmaster.rankId=student.rankId
				 LEFT JOIN schooltopicmaster ON schooltopicmaster.schoolTopicId=checkoff.schoolTopicId
				 LEFT JOIN courses ON rotation.courseId=courses.courseId
				 LEFT JOIN clinician ON clinician.clinicianId=checkoff.clinicianId";
		$sql .= " WHERE checkoff.schoolId=" . $currentSchoolId;
		if ($studentId) {
			$sql .= "  AND checkoff.studentId=" . $studentId;
		}
		if ($rotationId) {
			$sql .= " AND checkoff.rotationId=" . $rotationId;
		}
		if ($rankId) {
			$sql .= " AND student.rankId=" . $rankId;
		}

		if ($searchExpression != '') {
			$sql .= " AND ( student.firstName LIKE '%" . $searchExpression . "%' ";
			$sql .= " OR student.lastName LIKE '%" . $searchExpression . "%' ";

			$sql .= " OR schooltopicmaster.schooltitle LIKE '%" . $searchExpression . "%' )";
		}
		$sql .= " group by schooltopicmaster.schoolTopicId";
		$query = $objDB->GetResultset($sql);
		$totalFiltered = mysqli_num_rows($query); // when there is a search parameter then we have to modify total number filtered rows as per search result. 
		if ($orderByColumn == 0) {
			$sql .= " ORDER BY checkoff.checkoffDateTime desc ";
		} else {
			$sql .= " ORDER BY " . $orderByColumn . " " . $orderByColumnDir;
		}


		if ($length > 0) {
			$sql .= " LIMIT " . $start . " ," . $length . " ";
		}

		//echo $sql;	
		$query = $objDB->GetResultset($sql);

		unset($objDB);
		return array($totalFiltered, $query);
	}

	function GetStudentTopicList($studentId)
	{
		$objDB = new clsDB();
		$rows = "";

		$sql = "SELECT 
				checkoff.checkoffDateTime,
				checkoff.checkoffId,
				checkoff.schoolTopicId,				
				checkoff.student_evaluationDate,
				checkoff.evaluationDate,
				checkoff.rotationId,
				checkoff.school_evaluationDate ,
				rotation.title as rotationname,
				courses.courseId,
				courses.title as coursename,
				schooltopicmaster.schooltitle,
				schooltopicmaster.schoolTopicId
                 FROM checkoff  
				 LEFT JOIN rotation ON checkoff.rotationId=rotation.rotationId
				 LEFT JOIN schooltopicmaster ON schooltopicmaster.schoolTopicId=checkoff.schoolTopicId
				 LEFT JOIN courses ON rotation.courseId=courses.courseId";
		$sql .= " WHERE ";
		if ($studentId) {
			$sql .= " checkoff.studentId=" . $studentId;
		}



		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetYesCountForCheckoffQuestion($checkoffId)
	{
		$objDB = new clsDB();
		$rows = "";

		$sql = "SELECT 
           sum(IF(schooldefaultquestiondetail.schoolOptionText='YES', 1, 0)) AS YesCount,
           sum(IF(schooldefaultquestiondetail.schoolOptionText='No', 0, 1)) AS NoCount,
          sum(IF(schooldefaultquestionmaster.schoolQuestionTitle='(Note ANY and ALL dangerous actions; note advanced skills/knowledge.)', 0, 1)) AS Comments 
            FROM checkoff 
             
			INNER JOIN schools ON checkoff.schoolId=schools.schoolId 
			INNER JOIN schooltopicmaster ON checkoff.schoolTopicId=schooltopicmaster.schoolTopicId
			INNER JOIN checkoffdetail ON checkoff.checkoffId=checkoffdetail.checkoffId
			INNER JOIN schooldefaultquestionmaster ON schooldefaultquestionmaster.schoolQuestionId=checkoffdetail.schoolQuestionDId AND checkoffdetail.schoolOptionValue
			INNER JOIN schooldefaultquestiondetail ON schooldefaultquestiondetail.schoolQuestionDId=checkoffdetail.schoolOptionValue
            
	        WHERE checkoff.checkoffId=" . $checkoffId . "  AND (schooldefaultquestiondetail.schoolOptionText='Yes' OR  schooldefaultquestiondetail.schoolOptionText='No')";

		// echo $sql;exit;
		$rows = $objDB->GetDataRow($sql);
		unset($objDB);
		return $rows;
	}


	function GetNoCountForCheckoffQuestion($checkoffId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = " SELECT count(checkoff.checkoffId) As NoCount FROM checkoff 
			INNER JOIN schools ON checkoff.schoolId=schools.schoolId 
			INNER JOIN schooltopicmaster ON checkoff.schoolTopicId=schooltopicmaster.schoolTopicId
			INNER JOIN checkoffdetail ON checkoff.checkoffId=checkoffdetail.checkoffId
			INNER JOIN schooldefaultquestionmaster ON schooldefaultquestionmaster.schoolQuestionId=checkoffdetail.schoolQuestionDId AND checkoffdetail.schoolOptionValue
			INNER JOIN schooldefaultquestiondetail ON schooldefaultquestiondetail.schoolQuestionDId=checkoffdetail.schoolOptionValue";
		$sql .= " WHERE checkoff.checkoffId=" . $checkoffId . " AND schooldefaultquestiondetail.schoolOptionText='No' ";
		//echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetCommentForCheckoffQuestion($checkoffId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = " SELECT checkoff.*,schooldefaultquestionmaster.schoolQuestionTitle,checkoffdetail.schoolOptionAnswerText FROM checkoff 
		INNER JOIN schools ON checkoff.schoolId=schools.schoolId 
		INNER JOIN schooltopicmaster ON checkoff.schoolTopicId=schooltopicmaster.schoolTopicId
		INNER JOIN checkoffdetail ON checkoff.checkoffId=checkoffdetail.checkoffId
		LEFT JOIN schooldefaultquestionmaster ON schooldefaultquestionmaster.schoolQuestionId=checkoffdetail.schoolQuestionDId";
		$sql .= " WHERE checkoff.checkoffId=" . $checkoffId . " AND schooldefaultquestionmaster.schoolQuestionTitle='(Note ANY and ALL dangerous actions; note advanced skills/knowledge.)' ";
		//echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetStudentCheckOffDetailsInServerSide(
		$schoolId,
		$rotationId = 0,
		$studentId = 0,
		$rankId = 0,
		$evaluator = 0,
		$locationId = 0,
		$hospitalSiteId = 0,
		$startdate = '',
		$endtdate = '',
		$checkoffTopic,
		$start,
		$length,
		$searchExpression,
		$orderByColumn,
		$orderByColumnDir
	) {
		$objDB = new clsDB();
		$rows = "";


		$sql = "SELECT checkoff.*, student.studentId, student.rankId, 
						student.firstName,student.lastName,rankmaster.rankId,rankmaster.title AS Rankname,
						clinician.clinicianId,clinician.firstName AS clinicianfname,clinician.lastName  
						AS clinicianlname,rotation.rotationId,rotation.title AS rotationname,
						schools.schoolId,schooltopicmaster.schoolTopicId,schooltopicmaster.checkoffTitleId,schooltopicmaster.schooltitle
						FROM checkoff
						INNER JOIN schools ON checkoff.schoolId=schools.schoolId
						INNER JOIN schooltopicmaster ON checkoff.schoolTopicId=schooltopicmaster.schoolTopicId
						INNER JOIN student ON checkoff.studentId=student.studentId
						INNER JOIN rankmaster ON student.rankId=rankmaster.rankId
						INNER JOIN clinician ON checkoff.clinicianId=clinician.clinicianId
						INNER JOIN rotation ON checkoff.rotationId=rotation.rotationId";
		$sql .= " WHERE schools.schoolId= " . $schoolId;

		if ($checkoffTopic > 0) {
			$sql .= " AND schooltopicmaster.schoolTopicId =" . $checkoffTopic;
		}

		if ($rotationId > 0)
			$sql .= " AND rotation.rotationId=" . $rotationId;
		if ($studentId > 0)
			$sql .= " AND student.studentId=" . $studentId;
		if ($rankId > 0)
			$sql .= " AND rankmaster.rankId=" . $rankId;
		if ($startdate != '' || $endtdate != '')
			$sql .= " AND date(checkoff.createdDate) >= '" . $startdate . "'
								AND date(checkoff.createdDate) <= '" . $endtdate . "' ";



		if ($length > 0) {
			$sql .= " LIMIT " . $start . " ," . $length . " ";
		}


		//echo $sql;
		$rows = $objDB->GetResultset($sql);
		unset($objDB);
		return $rows;
	}

	function GetTopicDetails($selTopicId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = " SELECT schooltitle,instructions FROM schooltopicmaster  WHERE schoolTopicId=" . $selTopicId;
		//echo $sql;
		$rows = $objDB->GetDataRow($sql);
		unset($objDB);
		return $rows;
	}

	function CalculateSelectedLabQuestions($checkoffId)
	{
		$objDB = new clsDB();
		$SelectedLab = 0;
		$sql = "SELECT checkoff.checkoffId AS SelectedLab
						FROM checkoff
						LEFT JOIN checkoffdetail on checkoff.checkoffId = checkoffdetail.checkoffId 
						LEFT JOIN schooldefaultquestionmaster on  
						schooldefaultquestionmaster.schoolQuestionId = checkoffdetail.schoolQuestionDId
						LEFT JOIN schooldefaultquestiondetail on 
						schooldefaultquestiondetail.schoolQuestionDId =checkoffdetail.schoolOptionValue
						WHERE checkoff.checkoffId=" . $checkoffId . " AND schooldefaultquestiondetail.schoolOptionText like 'Lab' ";

		$SelectedLab = $objDB->GetSingleFieldValue($sql);
		unset($objDB);
		return $SelectedLab;
	}
	function GetSelectedLabQuestions($checkoffId)
	{
		$objDB = new clsDB();
		$SelectedLab = 0;
		$sql = "SELECT calculatedSelectedLab AS SelectedLab FROM checkoff WHERE checkoffId=" . $checkoffId;
		$SelectedLab = $objDB->GetSingleFieldValue($sql);
		unset($objDB);

		// if(floatval($SelectedLab) == 0)
		// {
		// $SelectedLab =$this->CalculateSelectedLabQuestions($checkoffId);
		// $this->UpdateCalculatedSelectedLabQuestions($checkoffId,$SelectedLab); 
		// }

		return $SelectedLab;
	}

	function CalculateSelectedStudentQuestions($checkoffId)
	{
		$objDB = new clsDB();
		$rows = 0;
		$TotalCount = 0;
		$sql = "SELECT checkoff.checkoffId AS SelectedStudentSection 
					from checkoff 
					inner join checkoffdetail on checkoff.checkoffId = checkoffdetail.checkoffId 
					inner join schooldefaultquestionmaster on schooldefaultquestionmaster.schoolQuestionId = checkoffdetail.schoolQuestionDId 
					inner join schooltopicdetails on schooltopicdetails.schoolQuestionId = schooldefaultquestionmaster.schoolQuestionId 
					left join schoolsectionmaster on schooltopicdetails.schoolSectionId = schoolsectionmaster.schoolSectionId 
					where checkoffdetail.schoolOptionAnswerText !=''
					AND checkoff.checkoffId=" . $checkoffId . "  
					AND schoolsectionmaster.sortOrder=2
					GROUP BY schooltopicdetails.schoolQuestionId ";

		$rows = $objDB->GetResultset($sql);
		if ($rows) {
			$TotalCount = mysqli_num_rows($rows);
		}
		unset($objDB);
		return $TotalCount;
	}

	function GetSelectedStudentQuestions($checkoffId)
	{
		$objDB = new clsDB();
		$rows = '';
		$objDB = new clsDB();
		$SelectedStudentSection = 0;
		$sql = "SELECT calculatedSelectedStudentQuestions AS SelectedStudentSection FROM checkoff WHERE checkoffId=" . $checkoffId;
		$SelectedStudentSection = $objDB->GetSingleFieldValue($sql);
		unset($objDB);

		// if(floatval($SelectedStudentSection) == 0)
		// {
		// $SelectedStudentSection =$this->CalculateSelectedStudentQuestions($checkoffId);
		// $this->UpdateCalculatedSelectedStudentQuestions($checkoffId,$SelectedStudentSection); 
		// }

		return $SelectedStudentSection;
	}

	function CalculateSelectedPreceptor($checkoffId)
	{
		$objDB = new clsDB();
		$SelectedPreceptor = 0;
		$sql = "SELECT count(checkoff.checkoffId) AS SelectedPreceptor
				FROM checkoff
				LEFT JOIN checkoffdetail on checkoff.checkoffId = checkoffdetail.checkoffId 
				LEFT JOIN schooldefaultquestionmaster on  
				schooldefaultquestionmaster.schoolQuestionId = checkoffdetail.schoolQuestionDId
				LEFT JOIN schooldefaultquestiondetail on 
				schooldefaultquestiondetail.schoolQuestionDId =checkoffdetail.schoolOptionValue 
				where checkoff.checkoffId=" . $checkoffId . " AND schooldefaultquestionmaster.schoolQuestionType=7 ";

		$SelectedPreceptor = $objDB->GetSingleFieldValue($sql);
		unset($objDB);
		return $SelectedPreceptor;
	}


	function GetSelectedPreceptorQuestions($checkoffId)
	{
		$objDB = new clsDB();
		$SelectedPreceptor = '';
		$sql = "SELECT calculatedSelectedPreceptor AS SelectedPreceptor FROM checkoff WHERE checkoffId=" . $checkoffId;
		$SelectedPreceptor = $objDB->GetSingleFieldValue($sql);
		unset($objDB);

		// if(floatval($SelectedPreceptor) == 0)
		// {
		// 	$SelectedPreceptor =$this->CalculateSelectedPreceptor($checkoffId);
		// 	//$this->UpdateCalculatedCheckoffScore($checkoffId,$SelectedPreceptor); 
		// }
		return $SelectedPreceptor;
	}

	function CalculateSelectedClinicalQuestions($checkoffId)
	{
		$objDB = new clsDB();
		$rows = '';
		$sql = "SELECT checkoff.checkoffId AS SelectedClinical
				from checkoff
				left join checkoffdetail on checkoff.checkoffId = checkoffdetail.checkoffId 
				left join schooldefaultquestionmaster on  
				schooldefaultquestionmaster.schoolQuestionId = checkoffdetail.schoolQuestionDId
				left join schooldefaultquestiondetail on 
				schooldefaultquestiondetail.schoolQuestionDId =checkoffdetail.schoolOptionValue
				where checkoff.checkoffId=" . $checkoffId . " AND schooldefaultquestiondetail.schoolOptionText like 'Clinical' ";

		$rows = $objDB->GetSingleFieldValue($sql);
		//echo $sql;
		unset($objDB);
		return $rows;
	}

	function GetSelectedClinicalQuestions($checkoffId)
	{
		$objDB = new clsDB();
		$SelectedClinical = '';
		$sql = "SELECT calculatedSelectedClinical AS SelectedClinical FROM checkoff WHERE checkoffId=" . $checkoffId;
		$SelectedClinical = $objDB->GetSingleFieldValue($sql);
		unset($objDB);

		// if(floatval($SelectedClinical) == 0)
		// {
		// $SelectedClinical =$this->CalculateSelectedClinicalQuestions($checkoffId);
		// $this->UpdateCalculatedSelectedClinicalQuestions($checkoffId,$SelectedClinical); 
		// }

		return $SelectedClinical;
	}

	function GetServerSideStudentCheckoff($currentSchoolId, $studentId, $start, $length, $searchExpression, $orderByColumn, $orderByColumnDir, $rotationId, $courseId)
	{

		$objDB = new clsDB();
		$rows = "";

		$sql = "SELECT 
				checkoff.*,rotation.title as rotationname,clinician.firstName,clinician.lastName,
				CONCAT(clinician.firstName, ' ', clinician.lastName) as fullname,
				student.firstName as studentfirstname,rankmaster.title as rankname,student.lastName as studentlastname,
				courses.courseId,courses.title as coursename,schooltopicmaster.schooltitle,schooltopicmaster.checkoffTitleId
				FROM checkoff  
				LEFT JOIN rotation ON checkoff.rotationId=rotation.rotationId
				LEFT JOIN student ON checkoff.studentId=student.studentId
				left JOIN rankmaster ON rankmaster.rankId=student.rankId
				LEFT JOIN schooltopicmaster ON schooltopicmaster.schoolTopicId=checkoff.schoolTopicId
				LEFT JOIN courses ON rotation.courseId=courses.courseId
				LEFT JOIN clinician ON clinician.clinicianId=checkoff.clinicianId";
		$sql .= " WHERE checkoff.schoolId=" . $currentSchoolId;
		if ($studentId) {
			$sql .= "  AND checkoff.studentId=" . $studentId;
		}
		if ($rotationId) {
			$sql .= "  AND checkoff.rotationId=" . $rotationId;
		}
		if ($courseId) {
			$sql .= "  AND rotation.courseId=" . $courseId;
		}
		$sql .= " group by schooltopicmaster.schoolTopicId";
		$query = $objDB->GetResultset($sql);
		unset($objDB);
		return ($query);
	}

	function GetServerSideCheckOffDetailByClinician($currentstudentId, $SchoolId, $rotationId, $clinicianId = 0, $rankId, $columns, $requestData)
	{
		$objDB = new clsDB();
		$query = "";
		$sql = "SELECT checkoff.*,rotation.rotationId,rotation.title as rotationname,clinician.firstName,
					clinician.lastName,student.firstName as studentfirstname,rankmaster.title as rankname,
					student.lastName as studentlastname,
					courses.courseId,courses.title as coursename,schooltopicmaster.schooltitle
					FROM checkoff  
					LEFT JOIN rotation ON checkoff.rotationId=rotation.rotationId
					LEFT JOIN student ON checkoff.studentId=student.studentId
					INNER JOIN rankmaster ON rankmaster.rankId=student.rankId
					LEFT JOIN schooltopicmaster ON schooltopicmaster.schoolTopicId=checkoff.schoolTopicId
					LEFT JOIN courses ON rotation.courseId=courses.courseId
					INNER JOIN clinician ON clinician.clinicianId=checkoff.clinicianId";


		$sql .= " WHERE checkoff.schoolId=" . $SchoolId;

		if ($clinicianId) {
			$sql .= " AND checkoff.clinicianId=" . $clinicianId;
		}

		if ($rotationId) {
			$sql .= " AND checkoff.rotationId=" . $rotationId;
		}

		if ($rankId > 0) {
			$sql .= " AND student.rankId=" . $rankId;
		}
		if ($currentstudentId > 0) {
			$sql .= " AND student.studentId=" . $currentstudentId;
		}


		if (!empty($requestData['search']['value'])) {
			$sql .= " AND ( clinician.firstName LIKE '%" . $requestData['search']['value'] . "%' ";
			$sql .= " OR clinician.lastName LIKE '%" . $requestData['search']['value'] . "%' ";

			$sql .= " OR schooltopicmaster.schooltitle LIKE '%" . $requestData['search']['value'] . "%' )";
		}

		$query = $objDB->GetResultset($sql);
		$totalFiltered = mysqli_num_rows($query); // when there is a search parameter then we have to modify total number filtered rows as per search result. 
		$sql .= " ORDER BY " . $columns[$requestData['order'][0]['column']] . "   " . $requestData['order'][0]['dir'] . "  LIMIT " . $requestData['start'] . " ," . $requestData['length'] . "   ";
		//echo 'sql->'.$sql;

		$query = $objDB->GetResultset($sql);

		unset($objDB);
		return array($totalFiltered, $query);
	}

	function GetCheckOffForStudentServerSide($studentId, $rotationId, $currentSchoolId, $rankId, $start, $length, $searchExpression, $orderByColumn, $orderByColumnDir)
	{
		$objDB = new clsDB();
		$rows = "";

		if ($orderByColumn == '') {
			$orderByColumn = 'checkoffDateTime';
			$orderByColumnDir = 'desc';
		}
		if ($searchExpression != '') {
		} else {
			$sql = "SELECT 
					checkoff.checkoffDateTime,checkoff.checkoffId,checkoff.schoolTopicId,checkoff.student_evaluationDate,
					checkoff.evaluationDate,checkoff.rotationId,checkoff.school_evaluationDate ,rotation.title as rotationname,
					clinician.firstName, clinician.lastName,student.firstName as studentfirstname,rankmaster.title as rankname,
					student.lastName as studentlastname,courses.courseId,courses.title as coursename,schooltopicmaster.schooltitle, student.studentId
					FROM checkoff  
					LEFT JOIN rotation ON checkoff.rotationId=rotation.rotationId
					LEFT JOIN student ON checkoff.studentId=student.studentId
					INNER JOIN rankmaster ON rankmaster.rankId=student.rankId
					LEFT JOIN schooltopicmaster ON schooltopicmaster.schoolTopicId=checkoff.schoolTopicId
					LEFT JOIN courses ON rotation.courseId=courses.courseId
					LEFT JOIN clinician ON clinician.clinicianId=checkoff.clinicianId";
			$sql .= " WHERE checkoff.schoolId=" . $currentSchoolId;
			if ($studentId) {
				$sql .= "  AND checkoff.studentId=" . $studentId;
			}
			if ($rotationId) {
				$sql .= " AND checkoff.rotationId=" . $rotationId;
			}
			if ($rankId) {
				$sql .= " AND student.rankId=" . $rankId;
			}
			$sql .= " GROUP BY checkoff.checkoffId";
			$sql .= " order by " . $orderByColumn . " " . $orderByColumnDir;
		}

		if ($length > 0) {
			$sql .= " LIMIT " . $start . " ," . $length . " ";
		}

		//echo $sql;
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function UpdateCalculatedCheckoffScore($CheckoffId, $Score)
	{
		if ($CheckoffId > 0) {
			$objDB = new clsDB();
			$objDB->UpdateSingleColumnValueToTable('checkoff', 'calculatedScore', $Score, 'checkoffId', $CheckoffId);
			unset($objDB);
		}
	}

	function UpdateSelectedPreceptor($CheckoffId, $SelectedPreceptor)
	{
		if ($CheckoffId > 0) {
			$objDB = new clsDB();
			$objDB->UpdateSingleColumnValueToTable('checkoff', 'calculatedSelectedPreceptor', $SelectedPreceptor, 'checkoffId', $CheckoffId);
			unset($objDB);
		}
	}

	function UpdateCalculatedSelectedLabQuestions($CheckoffId, $SelectedLab)
	{
		if ($CheckoffId > 0) {
			$objDB = new clsDB();
			$objDB->UpdateSingleColumnValueToTable('checkoff', 'calculatedSelectedLab', $SelectedLab, 'checkoffId', $CheckoffId);
			unset($objDB);
		}
	}

	function UpdateCalculatedSelectedClinicalQuestions($CheckoffId, $SelectedClinical)
	{
		if ($CheckoffId > 0) {
			$objDB = new clsDB();
			$objDB->UpdateSingleColumnValueToTable('checkoff', 'calculatedSelectedClinical', $SelectedClinical, 'checkoffId', $CheckoffId);
			unset($objDB);
		}
	}

	function UpdateCalculatedSelectedStudentQuestions($CheckoffId, $SelectedStudentSection)
	{
		if ($CheckoffId > 0) {
			$objDB = new clsDB();
			$objDB->UpdateSingleColumnValueToTable('checkoff', 'calculatedSelectedStudentQuestions', $SelectedStudentSection, 'checkoffId', $CheckoffId);
			unset($objDB);
		}
	}

	function DeleteSchoolCheckoffData($currentSchoolId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT checkoff.*,checkoffdetail.* FROM checkoff  
			LEFT JOIN checkoffdetail ON checkoff.checkoffId=checkoffdetail.checkoffId";
		$sql .= " WHERE checkoff.schoolId=" . $currentSchoolId;
		//echo $sql;
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function CalculateUsafCheckoffStudentScores($checkoffId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT AVG(schooldefaultquestiondetail.choiceAnswer) AS EvaluationScore				
		FROM checkoff
		INNER JOIN checkoffdetail ON checkoffdetail.checkoffId=
		checkoff.checkoffId
		INNER JOIN schooldefaultquestiondetail  ON checkoffdetail.schoolOptionValue=
		schooldefaultquestiondetail.schoolQuestionDId 
		WHERE  checkoff.checkoffId=" . $checkoffId;
		// echo $sql;exit;
		$rows = $objDB->GetSingleFieldValue($sql);
		return $rows;
		unset($objDB);
	}

	function UpdateCalculatedUsafCheckoffScore($CheckoffId, $Score)
	{
		if ($CheckoffId > 0) {
			$objDB = new clsDB();
			$objDB->UpdateSingleColumnValueToTable('checkoff', 'calculatedUsafScore', $Score, 'checkoffId', $CheckoffId);
			unset($objDB);
		}
	}

	function GetScoreForUsafCheckoff($checkoffId)
	{
		$objDB = new clsDB();
		$score = 0;
		$sql = "SELECT calculatedUsafScore AS CheckoffScore FROM checkoff WHERE checkoffId=" . $checkoffId;
		$score = $objDB->GetSingleFieldValue($sql);
		unset($objDB);

		// //This is for all existing checkoff records who has not calculated score remove this once aal checkoff score calculate
		// if(floatval($score) == 0)
		// {
		// 	$score =$this->CalculateUsafCheckoffStudentScores($checkoffId);
		// 	//$this->UpdateCalculatedCheckoffScore($checkoffId,$score); 
		// }

		return $score;
	}

	function GetCheckoffByRotationId($rotationId)
	{
		$objDB = new clsDB();
		$checkoffId = 0;
		$sql = "SELECT checkoffId FROM checkoff WHERE rotationId=" . $rotationId;
		$checkoffId = $objDB->GetSingleFieldValue($sql);
		unset($objDB);

		// if(floatval($SelectedLab) == 0)
		// {
		// $SelectedLab =$this->CalculateSelectedLabQuestions($checkoffId);
		// $this->UpdateCalculatedSelectedLabQuestions($checkoffId,$SelectedLab); 
		// }

		return $checkoffId;
	}

	function SavestandardSideStudentCheckoff($checkoffId = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		if ($checkoffId > 0) {
			$sql = "UPDATE checkoff SET
						studentComment='" . ($this->studentComment) . "',												 
						studentComment='" . ($this->studentComment) . "',			
						updatedDate = '" . (date("Y-m-d h:i:s")) . "' 
						 Where checkoffId= " . $checkoffId;
			//echo $sql.'<hr>';exit;
			$checkoffId = $objDB->ExecuteQuery($sql);
		} else {
			$sql = "INSERT INTO checkoff (schoolId,studentId,checkoffDateTime,schoolTopicId,rotationId,
										clinicianId,student_evaluationDate,studentComment,createdStudentId,createdDate,completion1stPreceptorId,isExternalPreceptorcheckoff) VALUES (
					'" . ($this->schoolId) . "',
					'" . ($this->studentId) . "',
					'" . ($this->checkoffDateTime) . "',
					'" . ($this->schoolTopicId) . "',
					'" . ($this->rotationId) . "',
					'" . ($this->clinicianId) . "',
					'" . ($this->student_evaluationDate) . "',
					'" . ($this->studentComment) . "',
					'" . ($this->createdStudentId) . "',
					'" . (date("Y-m-d h:i:s")) . "',
					'" . ($this->completion1stPreceptorId) . "',
					'" . ($this->isExternalPreceptorcheckoff) . "'
					)";
			// echo 'insert->'.$sql;exit;
			$checkoffId = $objDB->ExecuteInsertQuery($sql);
		}

		unset($objDB);
		return $checkoffId;
	}

	function SaveUSAFSideStudentCheckoff($checkoffId = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "INSERT INTO checkoff (schoolId,studentId,checkoffDateTime,schoolTopicId,rotationId,
							clinicianId,evaluationDate,studentComment,createdStudentId,createdDate,completion1stPreceptorId,isExternalPreceptorcheckoff) VALUES (
		'" . ($this->schoolId) . "',
		'" . ($this->studentId) . "',
		'" . ($this->checkoffDateTime) . "',
		'" . ($this->schoolTopicId) . "',
		'" . ($this->rotationId) . "',
		'" . ($this->clinicianId) . "',
		'" . ($this->evaluationDate) . "',
		'" . ($this->studentComment) . "',
		'" . ($this->createdStudentId) . "',
		'" . (date("Y-m-d h:i:s")) . "',
		'" . ($this->completion1stPreceptorId) . "',
		'" . ($this->isExternalPreceptorcheckoff) . "'
		)";
		// echo 'insert	->'.$sql;exit;
		$checkoffId = $objDB->ExecuteInsertQuery($sql);


		unset($objDB);
		return $checkoffId;
	}

	function UpdatestandardSideStudentCheckoffByPreceptor($checkoffId = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		if ($checkoffId > 0) {
			$sql = "UPDATE checkoff SET
					evaluationDate='" . ($this->evaluationDate) . "',												 
					studentComment='" . ($this->studentComment) . "',												 
					is1stCompletionStatus='" . $this->is1stCompletionStatus . "',
					updatedDate = '" . (date("Y-m-d h:i:s")) . "' 
						Where checkoffId= " . $checkoffId;
			// echo $sql;exit;
			$checkoffId = $objDB->ExecuteQuery($sql);
		}
		unset($objDB);
		return $checkoffId;
	}

	function UpdateUSAFSideStudentCheckoffByPreceptor($checkoffId = 0, $evaluationDate = '')
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "UPDATE checkoff SET												 
					is1stCompletionStatus='1',
					updatedDate = '" . (date("Y-m-d h:i:s")) . "'";
		if ($evaluationDate != '')
			$sql .= ", evaluationDate='" . ($evaluationDate) . "'	";

		$sql .= "Where checkoffId= " . $checkoffId;
		$retCheckoffId = $objDB->ExecuteQuery($sql);
		// echo $sql;exit;
		unset($objDB);
		$retCheckoffId = ($retCheckoffId) ? $checkoffId : 0;
		return $retCheckoffId;
	}

	function GetCHeckoffDetailByCheckoffID($checkoffId)
	{
		$objDB = new clsDB();
		$score = 0;
		$sql = "SELECT * FROM checkoff WHERE checkoffId=" . $checkoffId;
		$score = $objDB->GetDataRow($sql);
		unset($objDB);

		return $score;
	}

	function GetAllCheckoffList($schoolId, $isStudentEvalutionComplete, $isSendToCanvas)
	{
		$objDB = new clsDB();
		$rows = '';
		$sql = "SELECT * FROM checkoff WHERE schoolId=" . $schoolId;

		if ($isStudentEvalutionComplete)
			$sql .= " AND (student_evaluationDate !='' OR student_evaluationDate !='0000-00-00 00:00:00') ";

		if ($isSendToCanvas)
			$sql .= " AND isSendToCanvas = " . $isSendToCanvas;

		$rows = $objDB->GetResultset($sql);
		unset($objDB);

		return $rows;
	}

	// checkopff report for clinician
	function GetStudentCheckOffDetailsForClinicianreport(
		$schoolId,
		$rotationId = 0,
		$subcborotation = 0,
		$studentId = '',
		$rankId = 0,
		$evaluator = 0,
		$clinicianId = 0,
		$locationId = 0,
		$hospitalSiteId = 0,
		$startdate = '',
		$endtdate = '',
		$checkoffTopic,
		$ascdesc,
		$sordorder,
		$cbosemester,
		$start,
		$length,
		$studentIdArr = ''
	) {
		$totalFiltered = 0;

		$studentIdArray =  $studentId ? unserialize($studentId) : [];
		$studentIds =  count($studentIdArray) ? implode(",", $studentIdArray) : '';
		$rotationId = str_replace(" ", ",", $rotationId);
		$cbosemester = str_replace(" ", ",", $cbosemester);
		$subcborotation = str_replace(" ", ",", $subcborotation);

		$objDB = new clsDB();
		$query = "";
		$sql = "SELECT checkoff.*, student.studentId, student.rankId, 
				student.firstName,student.lastName,rankmaster.rankId,rankmaster.title AS Rankname,
				clinician.clinicianId,clinician.firstName AS clinicianfname,clinician.lastName  
				AS clinicianlname,rotation.rotationId,rotation.title AS rotationname,
				schools.schoolId,schooltopicmaster.schoolTopicId,schooltopicmaster.checkoffTitleId,schooltopicmaster.schooltitle,  hospitalsites.title AS hospitalsite
				FROM checkoff
				INNER JOIN schools ON checkoff.schoolId=schools.schoolId
				INNER JOIN schooltopicmaster ON checkoff.schoolTopicId=schooltopicmaster.schoolTopicId
				INNER JOIN student ON checkoff.studentId=student.studentId
				INNER JOIN rankmaster ON student.rankId=rankmaster.rankId
				INNER JOIN clinician ON checkoff.clinicianId=clinician.clinicianId
				INNER JOIN rotation ON checkoff.rotationId=rotation.rotationId
				LEFT join hospitalsites on hospitalsites.hospitalSiteId = rotation.hospitalSiteId
		 		INNER join clinicianhospitalsite on clinicianhospitalsite.hospitalSiteId = rotation.hospitalSiteId AND clinicianhospitalsite.clinicianId = " . $clinicianId . "
				INNER JOIN courses ON courses.`courseId` = rotation.`courseId`
				INNER JOIN semestermaster ON courses.`semesterId` = semestermaster.`semesterId`";
		$sql .= " WHERE schools.schoolId= " . $schoolId;
		$sql .= "  AND (rankmaster.title !='Graduate' AND rankmaster.title NOT like '%Graduate%') and rankmaster.title !='Dropout'";
		if ($checkoffTopic > 0)
			$sql .= " AND schooltopicmaster.schoolTopicId =" . $checkoffTopic;
		if ($cbosemester > 0)
			$sql .= " AND semestermaster.semesterId IN ($cbosemester)";

		if ($subcborotation > 0)
			$sql .= " AND (rotation.rotationId IN ($subcborotation) OR rotation.rotationId=" . $rotationId . " )";
		else if ($rotationId > 0)
			$sql .= " AND rotation.rotationId=" . $rotationId;
		if ($studentIds > 0)
			$sql .= " AND student.studentId IN ($studentIds)";
		if ($rankId > 0)
			$sql .= " AND rankmaster.rankId=" . $rankId;
		if ($evaluator > 0) {
			$sql .= " AND checkoff.clinicianId =" . $evaluator;
		}

		if ($hospitalSiteId > 0) {
			$sql .= " AND hospitalsites.hospitalSiteId  =" . $hospitalSiteId;
		}
		if ($startdate != '' || $endtdate != '')
			$sql .= " AND date(checkoff.createdDate) >= '" . $startdate . "'
					AND date(checkoff.createdDate) <= '" . $endtdate . "' ";



		if ($ascdesc && $sordorder == 1)
			$sql .= "  ORDER BY student.firstName " . $ascdesc;
		elseif ($ascdesc && $sordorder == 2)
			$sql .= "  ORDER BY rankmaster.title " . $ascdesc;
		elseif ($ascdesc && $sordorder == 5)
			$sql .= "  ORDER BY rotation.title " . $ascdesc;

		else if ($ascdesc && $sordorder == 14)
			$sql .= "  ORDER BY student.firstName " . $ascdesc;

		else if ($ascdesc && $sordorder == 15)
			$sql .= "  ORDER BY student.lastName " . $ascdesc;

		else if ($ascdesc && $sordorder == 8)
			$sql .= "  ORDER BY hospitalsites.title " . $ascdesc;
		else
			$sql .= "  ORDER BY schooltopicmaster.checkoffTitleId ";

		$query = $objDB->GetResultset($sql);
		if ($query)
			$totalFiltered = mysqli_num_rows($query); // when there is a search parameter then we have to modify total number filtered rows as per search result. 

		if ($length > 0)
			$sql .= " LIMIT " . $start . " ," . $length . " ";

		// echo $sql;
		// exit;	
		$query = $objDB->GetResultset($sql);
		unset($objDB);
		return $query;
	}

	function GetCheckOffByCoursesForClinicianreport(
		$schoolId,
		$rotationId = 0,
		$subcborotation = 0,
		$studentId,
		$rankId = 0,
		$clinicianId = 0,
		$locationId = 0,
		$hospitalSiteId = 0,
		$startdate = '',
		$endtdate = '',
		$checkoffTopic,
		$ascdesc,
		$sordorder,
		$cbosemester,
		$courseTopicCompletion = ''
	) {

		$studentIds =  $studentId ? implode(',', $studentId) : '';
		$rotationId = str_replace(" ", ",", $rotationId);
		$cbosemester = str_replace(" ", ",", $cbosemester);
		$subcborotation = str_replace(" ", ",", $subcborotation);

		$objDB = new clsDB();
		$query = "";

		//Not Completed Course Topics
		if ($courseTopicCompletion == 2 || $courseTopicCompletion == '') {

			$sql = "SELECT student.firstName,student.lastName,checkoff.checkoffDateTime,checkoff.calculatedScore, checkoff.calculatedUsafScore, checkoff.checkoffId,schooltopicmaster.schooltitle AS schooltitle,schooltopicmaster.checkoffTitleId, 
			rotation.parentRotationId,rotation.locationId as rotationLocationId, courses.title AS courseTitle,courses.locationId,
			IF(checkoff.schoolTopicId IS NOT NULL,'Completed','Remaining') as Status ,clinician.firstName as clinicianfname,clinician.lastName as clinicianlname,rotation.title as rotationTitle
			FROM schooltopicmaster 
			INNER JOIN coursetopicdetail ON schooltopicmaster.schoolTopicId=coursetopicdetail.schoolTopicId 
			INNER JOIN rotation ON rotation.courseId=coursetopicdetail.courseId
		 	INNER join clinicianhospitalsite on clinicianhospitalsite.hospitalSiteId = rotation.hospitalSiteId AND clinicianhospitalsite.clinicianId = " . $clinicianId . "
			INNER JOIN courses ON rotation.courseId=courses.courseId
			INNER JOIN rotationdetails ON rotation.rotationId=rotationdetails.rotationId 
			INNER JOIN student ON student.studentId=rotationdetails.studentId 
			LEFT JOIN rankmaster ON student.rankId = rankmaster.rankId 
			LEFT JOIN checkoff ON schooltopicmaster.schoolTopicId=checkoff.schoolTopicId AND checkoff.rotationId=rotationdetails.rotationId AND checkoff.studentId=rotationdetails.studentId
			LEFT JOIN clinician ON clinician.clinicianId = checkoff.clinicianId
			WHERE checkoff.checkoffDateTime IS NULL AND schooltopicmaster.schoolId=" . $schoolId . " AND (rankmaster.title !='Graduate' AND rankmaster.title NOT like '%Graduate%') and rankmaster.title !='Dropout'";
			if ($checkoffTopic > 0)
				$sql .= " AND schooltopicmaster.schoolTopicId =" . $checkoffTopic;
			if ($cbosemester > 0)
				$sql .= " AND semestermaster.semesterId IN ($cbosemester)";

			if ($subcborotation > 0)
				$sql .= " AND (rotation.rotationId IN ($subcborotation) OR rotation.rotationId=" . $rotationId . " )";
			else if ($rotationId > 0)
				$sql .= " AND rotation.rotationId=" . $rotationId;
			if ($studentIds > 0)
				$sql .= " AND student.studentId IN ($studentIds)";
			if ($rankId > 0)
				$sql .= " AND rankmaster.rankId=" . $rankId;
		}

		// For Bothe Completed Or Not Completed
		if ($courseTopicCompletion == '')
			$sql .= " UNION ";

		//Completed Course Topics
		if ($courseTopicCompletion == 1 || $courseTopicCompletion == '') {
			if ($courseTopicCompletion == 1)
				$sql = '';

			$sql .= "SELECT student.firstName,student.lastName,checkoff.checkoffDateTime,checkoff.calculatedScore, checkoff.calculatedUsafScore, checkoff.checkoffId,schooltopicmaster.schooltitle AS schooltitle,schooltopicmaster.checkoffTitleId, 
			rotation.parentRotationId,rotation.locationId as rotationLocationId, courses.title AS courseTitle,courses.locationId,
			IF(checkoff.schoolTopicId IS NOT NULL,'Completed','Remaining') as Status,clinician.firstName as clinicianfname,clinician.lastName as clinicianlname,rotation.title as rotationTitle
			FROM schooltopicmaster 
			INNER JOIN coursetopicdetail ON schooltopicmaster.schoolTopicId=coursetopicdetail.schoolTopicId 
			INNER JOIN rotation ON rotation.courseId=coursetopicdetail.courseId
		 	INNER join clinicianhospitalsite on clinicianhospitalsite.hospitalSiteId = rotation.hospitalSiteId AND clinicianhospitalsite.clinicianId = " . $clinicianId . "
			INNER JOIN courses ON rotation.courseId=courses.courseId
			INNER JOIN rotationdetails ON rotation.rotationId=rotationdetails.rotationId 
			INNER JOIN student ON student.studentId=rotationdetails.studentId 
			LEFT JOIN rankmaster ON student.rankId = rankmaster.rankId 
			LEFT JOIN checkoff ON schooltopicmaster.schoolTopicId=checkoff.schoolTopicId AND checkoff.rotationId=rotationdetails.rotationId AND checkoff.studentId=rotationdetails.studentId
			LEFT JOIN clinician ON clinician.clinicianId = checkoff.clinicianId
			WHERE schooltopicmaster.schoolId= " . $schoolId . " AND (rankmaster.title !='Graduate' AND rankmaster.title NOT like '%Graduate%') and rankmaster.title !='Dropout'";
			if ($checkoffTopic > 0)
				$sql .= " AND schooltopicmaster.schoolTopicId =" . $checkoffTopic;
			if ($cbosemester > 0)
				$sql .= " AND semestermaster.semesterId IN ($cbosemester)";

			if ($subcborotation > 0)
				$sql .= " AND (rotation.rotationId IN ($subcborotation) OR rotation.rotationId=" . $rotationId . " )";
			else if ($rotationId > 0)
				$sql .= " AND rotation.rotationId=" . $rotationId;
			if ($studentIds > 0)
				$sql .= " AND student.studentId IN ($studentIds)";
			if ($rankId > 0)
				$sql .= " AND rankmaster.rankId=" . $rankId;
			if ($startdate != '' || $endtdate != '')
				$sql .= " AND  (date(checkoff.checkoffDateTime) >= '" . $startdate . "'
						AND date(checkoff.checkoffDateTime) <= '" . $endtdate . "') ";
		}

		// echo $sql;
		// exit;
		$query = $objDB->GetResultset($sql);

		unset($objDB);
		return $query;
	}

	function GetCheckOffScore($checkoffId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT SUM(schooldefaultquestionmaster.marks) AS score FROM checkoffdetail INNER JOIN checkoff on checkoffdetail.checkoffId = checkoff.checkoffId
		INNER JOIN 	schooldefaultquestiondetail on 	schooldefaultquestiondetail.schoolQuestionDId = checkoffdetail.schoolOptionValue
		INNER JOIN schooldefaultquestionmaster ON schooldefaultquestionmaster.schoolQuestionId = schooldefaultquestiondetail.schoolQuestionId
		WHERE checkoff.checkoffId = $checkoffId  AND  schooldefaultquestiondetail.schoolOptionText = 'Yes'";


		// echo $sql;
		$rows = $objDB->GetSingleFieldValue($sql);
		return $rows;
		unset($objDB);
	}

	function GetRotationTopics($rotationId, $studentId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT schooltopicmaster.*, rotation.rotationId
		FROM schooltopicmaster 
		INNER JOIN coursetopicdetail ON schooltopicmaster.schoolTopicId=coursetopicdetail.schoolTopicId
		INNER JOIN courses ON coursetopicdetail.courseId=courses.courseId
		INNER JOIN rotation ON rotation.courseId=courses.courseId 
		left JOIN checkoff ON schooltopicmaster.schoolTopicId =checkoff.schoolTopicId
		WHERE rotation.rotationId='" . $rotationId  .  "'
		AND NOT EXISTS (select checkoff.schoolTopicId from checkoff where schooltopicmaster.schoolTopicId =checkoff.schoolTopicId AND checkoff.studentId ='" . ($studentId) . "' AND checkoff.rotationId ='" . $rotationId . "')				
		GROUP BY `schooltopicmaster`.`checkoffTitleId` 
		ORDER BY `schooltopicmaster`.`checkoffTitleId` ASC";


		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}


	function GetStudentCheckoffQuestionDetails($questionId = 0, $checkoffId = 0, $currentSchoolId = 0, $isActiveCheckoff)
	{
		// 	echo $questionId."=>".$checkoffId."=>".$currentSchoolId."=>".$isActiveCheckoff;
		$objDB = new clsDB();
		$rows = "";

		$sql = "SELECT 
						schooldefaultquestiondetail.schoolQuestionDId AS OptionValue,						
						schooldefaultquestiondetail.schoolOptionValue,						
						schooldefaultquestiondetail.schoolOptionText AS optionText,						
						checkoffdetail.schoolOptionValue AS SelectedOptionValue,						
						checkoffdetail.checkoffDId AS DetailId,		
						checkoffdetail.checkoffId AS checkoffId,	
						checkoffdetail.comments AS questionComment	
						
						FROM
						schooldefaultquestiondetail
						INNER JOIN schooldefaultquestionmaster ON schooldefaultquestiondetail.schoolQuestionId=
																	schooldefaultquestionmaster.schoolQuestionId
																	
						INNER JOIN checkoffdetail ON checkoffdetail.schoolQuestionDId = 
																	schooldefaultquestiondetail.schoolQuestionId
						AND checkoffdetail.schoolOptionValue = schooldefaultquestiondetail.schoolQuestionDId 
						AND checkoffdetail.checkoffId =" . $checkoffId;

		$sql .= " WHERE schooldefaultquestiondetail.schoolQuestionId=" . $questionId;
		// .
		// 	" AND schooldefaultquestionmaster.schoolId=" . $currentSchoolId
		$sql .= "  group BY schooldefaultquestiondetail.schoolOptionText";
		if ($isActiveCheckoff == 0) {
			$sql .= "	ORDER BY schooldefaultquestiondetail.schoolOptionValue ASC";
		} else {
			$sql .= "	ORDER BY schooldefaultquestiondetail.schoolOptionText ASC";
		}
		// echo $sql;
		// exit;
		$rows = $objDB->GetDataRow($sql);
		return $rows;
		unset($objDB);
	}

	function GetStudentCheckoffTextQuestionDetails($questionId = 0, $checkoffId = 0, $currentSchoolId = 0)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT 
						checkoffdetail.checkoffDId,
						checkoffdetail.schoolQuestionDId,
						checkoffdetail.schoolOptionAnswerText AS optionText,
						checkoff.checkoffId
						FROM checkoffdetail
						INNER JOIN checkoff ON checkoffdetail.checkoffId=
													checkoff.checkoffId";
		$sql .= " WHERE checkoffdetail.schoolQuestionDId=" . $questionId . " AND
						 checkoff.checkoffId=" . $checkoffId .
			" AND checkoff.schoolId=" . $currentSchoolId;;
		// echo $sql;		
		$rows = $objDB->GetDataRow($sql);
		return $rows;
		unset($objDB);
	}

	function GetClinicianNote($checkoffId, $schoolId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = " SELECT checkoffdetail.schoolOptionAnswerText
			FROM checkoffdetail
			INNER JOIN checkoff ON checkoffdetail.checkoffId= checkoff.checkoffId
			LEFT JOIN schooldefaultquestionmaster on schooldefaultquestionmaster.schoolQuestionId = checkoffdetail.schoolQuestionDId
			WHERE checkoff.checkoffId=" . $checkoffId . " AND checkoff.schoolId=" . $schoolId . "
			AND (schooldefaultquestionmaster.schoolQuestionTitle LIKE '%Note ANY and ALL dangerous actions; note advanced skills/knowledge.%' OR schooldefaultquestionmaster.schoolQuestionTitle LIKE '%Note: Unsatisfactory performance must have comments%') ";

		// echo $sql;exit;
		$rows = $objDB->GetSingleFieldValue($sql);
		return $rows;
		unset($objDB);
	}

	function GetAdvanceCheckoffPreeptorDetails($checkoffId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT checkoff.completion1stPreceptorId,checkoff.completion2ndPreceptorId,checkoff.completion3rdPreceptorId,checkoff.completion4thPreceptorId,checkoff.completion5thPreceptorId,checkoff.is1stCompletionStatus,checkoff.is2ndCompletionStatus,checkoff.is3rdCompletionStatus,checkoff.is4thCompletionStatus,checkoff.is5thCompletionStatus
				FROM  checkoff ";
		$sql .= " WHERE checkoff.checkoffId=" . $checkoffId;
		// echo $sql;		
		$rows = $objDB->GetDataRow($sql);
		return $rows;
		unset($objDB);
	}
	// checkoff.completion1stPreceptorId,checkoff.is1stCompletionStatus,checkoff.is2ndCompletionStatus,checkoff.is3rdCompletionStatus,checkoff.is4thCompletionStatus,checkoff.is5thCompletionStatus

	function GetCheckOffDetailForAdvanceClinician($currentstudentId, $SchoolId, $rotationId, $rankId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT checkoff.*,rotation.rotationId,rotation.title as rotationname,clinician.firstName,
		        clinician.lastName,student.firstName as studentfirstname,rankmaster.title as rankname,
		        student.lastName as studentlastname,
				courses.courseId,courses.title as coursename,schooltopicmaster.schooltitle,schooltopicmaster.checkoffTitleId,rotation.parentRotationId,rotation.locationId as rotationLocationId,courses.locationId,schooltopicmaster.procedureCategoryId
                 FROM checkoff  
				 LEFT JOIN rotation ON checkoff.rotationId=rotation.rotationId
				 LEFT JOIN student ON checkoff.studentId=student.studentId
				 INNER JOIN rankmaster ON rankmaster.rankId=student.rankId
				 LEFT JOIN schooltopicmaster ON schooltopicmaster.schoolTopicId=checkoff.schoolTopicId
				 LEFT JOIN courses ON rotation.courseId=courses.courseId
				 LEFT JOIN clinician ON clinician.clinicianId=checkoff.clinicianId";

		if ($SchoolId) {
			$sql .= " WHERE checkoff.schoolId=" . $SchoolId . " ";
		}
		if ($rotationId) {
			$sql .= " AND checkoff.rotationId=" . $rotationId . " ";
		}

		if ($rankId > 0) {
			$sql .= " AND student.rankId=" . $rankId;
		}
		if ($currentstudentId > 0) {
			$sql .= " AND student.studentId=" . $currentstudentId;
		}

		// $sql .= "  AND (
		// 	(CURDATE() BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate))
		// 	OR
		// 	(rotation.parentRotationId > 0
		// 		AND EXISTS (
		// 			SELECT 1
		// 			FROM rotation AS parent
		// 			WHERE parent.rotationId = rotation.parentRotationId
		// 			AND CURDATE() BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
		// 			AND rotation.isSchedule = 1 
		// 		)
		// 	)
		// ) ";
		$sql .= " ORDER BY checkoff.checkoffDateTime desc";
		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetCheckOffDetailByAdvanceClinician($currentstudentId, $SchoolId, $rotationId, $clinicianId, $rankId, $isActiveCheckoff = 0)
	{

		$objDB = new clsDB();
		$rows = "";
		$sql = "SELECT checkoff.*,rotation.rotationId,rotation.title as rotationname,clinician.firstName,
		        clinician.lastName,student.firstName as studentfirstname,rankmaster.title as rankname,
		        student.lastName as studentlastname,
				courses.courseId,courses.title as coursename,schooltopicmaster.schooltitle,schooltopicmaster.checkoffTitleId,rotation.parentRotationId,rotation.locationId as rotationLocationId,courses.locationId,schooltopicmaster.procedureCategoryId
                 FROM checkoff  
				 LEFT JOIN rotation ON checkoff.rotationId=rotation.rotationId
				 LEFT JOIN student ON checkoff.studentId=student.studentId
				 INNER JOIN rankmaster ON rankmaster.rankId=student.rankId
				 LEFT JOIN schooltopicmaster ON schooltopicmaster.schoolTopicId=checkoff.schoolTopicId
				 LEFT JOIN courses ON rotation.courseId=courses.courseId
				 INNER JOIN clinician ON clinician.clinicianId=checkoff.clinicianId";


		$sql .= " WHERE checkoff.schoolId=" . $SchoolId . " AND checkoff.clinicianId=" . $clinicianId;

		if ($rotationId) {
			$sql .= " AND checkoff.rotationId=" . $rotationId . " ";
		}

		if ($rankId > 0) {
			$sql .= " AND student.rankId=" . $rankId;
		}
		if ($currentstudentId > 0) {
			$sql .= " AND student.studentId=" . $currentstudentId;
		}

		// $sql .= "  AND (
		// 	(CURDATE() BETWEEN DATE(rotation.startDate) AND DATE(rotation.endDate))
		// 	OR
		// 	(rotation.parentRotationId > 0
		// 		AND EXISTS (
		// 			SELECT 1
		// 			FROM rotation AS parent
		// 			WHERE parent.rotationId = rotation.parentRotationId
		// 			AND CURDATE() BETWEEN DATE(parent.startDate) AND DATE(parent.endDate)
		// 			AND rotation.isSchedule = 1 
		// 		)
		// 	)
		// ) ";

		$sql .= " ORDER BY checkoff.checkoffDateTime ASC";
		// echo $sql;
		$rows = $objDB->GetResultset($sql);
		return $rows;
		unset($objDB);
	}

	function GetCheckoffCompletionStatus($checkoffId, $preceptorId)
	{
		$objDB = new clsDB();
		$score = 0;
		$sql = "SELECT
				CASE
					WHEN completion1stPreceptorId = $preceptorId THEN is1stCompletionStatus
					WHEN completion2ndPreceptorId = $preceptorId THEN is2ndCompletionStatus
					WHEN completion3rdPreceptorId = $preceptorId THEN is3rdCompletionStatus
					WHEN completion4thPreceptorId = $preceptorId THEN is4thCompletionStatus
					WHEN completion5thPreceptorId = $preceptorId THEN is5thCompletionStatus
					ELSE '0'
				END AS completion_status
			FROM checkoff where checkoffId = " . $checkoffId;
		$status = $objDB->GetSingleFieldValue($sql);
		// echo $sql;exit;
		unset($objDB);
		return $status;
	}

	/**
	 * Returns the checkoff details for a given checkoffId. This function is used when generating the pdf.
	 * @param int $checkoffId The checkoffId.
	 * @return array The checkoff details.
	 */
	function GetCheckOffDetailsForPdf($checkoffId)
	{
		$objDB = new clsDB();
		$rows = "";
		$sql = "select checkoff.*,rotation.title As rotationName, concat(clinician.firstName, ' ', clinician.lastName) AS clinicianName,concat(rotation.title,' (',hospitalsites.title,')') as rotationName,CONCAT(student.firstName, ' ', student.lastName) AS studentFullName,schooltopicmaster.schooltitle as topicTitle from checkoff 
		LEFT JOIN clinician ON checkoff.clinicianId = clinician.clinicianId
		LEFT JOIN rotation ON checkoff.`rotationId` = rotation.`rotationId` 
		LEFT JOIN schooltopicmaster ON schooltopicmaster.schoolTopicId=checkoff.schoolTopicId
        inner Join student on student.studentId = checkoff.studentId 
		INNER JOIN hospitalsites ON hospitalsites.hospitalSiteId = rotation.hospitalSiteId
		WHERE checkoff.checkoffId=" . $checkoffId;
		$rows = $objDB->GetDataRow($sql);
		// echo $sql;
		return $rows;
		unset($objDB);
	}
	function GetStudentAllCheckoffQuestionDetails($questionId = 0, $checkoffId = 0, $currentSchoolId = 0, $isActiveCheckoff)
	{
		// 	echo $questionId."=>".$checkoffId."=>".$currentSchoolId."=>".$isActiveCheckoff;
		$objDB = new clsDB();
		$rows = "";

		$sql = "SELECT 
						GROUP_CONCAT(schooldefaultquestiondetail.schoolOptionText ORDER BY schooldefaultquestiondetail.schoolOptionText ASC) AS schoolOptionText
						
						FROM
						schooldefaultquestiondetail
						INNER JOIN schooldefaultquestionmaster ON schooldefaultquestiondetail.schoolQuestionId=
																	schooldefaultquestionmaster.schoolQuestionId
																	
						INNER JOIN checkoffdetail ON checkoffdetail.schoolQuestionDId = 
																	schooldefaultquestiondetail.schoolQuestionId
						AND checkoffdetail.schoolOptionValue = schooldefaultquestiondetail.schoolQuestionDId 
						AND checkoffdetail.checkoffId =" . $checkoffId;

		$sql .= " WHERE schooldefaultquestiondetail.schoolQuestionId=" . $questionId;
		// .
		// 	" AND schooldefaultquestionmaster.schoolId=" . $currentSchoolId
		// $sql .= "  group BY schooldefaultquestiondetail.schoolOptionText";
		// if ($isActiveCheckoff == 0) {
		// 	$sql .= "	ORDER BY schooldefaultquestiondetail.schoolOptionValue ASC";
		// } else {
		// 	$sql .= "	ORDER BY schooldefaultquestiondetail.schoolOptionText ASC";
		// }
		// echo $sql;
		// exit;
		$rows = $objDB->GetSingleFieldValue($sql);
		return $rows;
		unset($objDB);
	}

	function GetAllCheckoffDetailsForLogs($checkoffId, $schoolId = 0)
	{
		$objDB = new clsDB();

		$sql = "SELECT 
    c.*,
    r.title as rotationName,
	c.studentId as userId,
    s.displayName as schoolName,
	stm.schooltitle as topicName,
    CONCAT(st.firstName, ' ', st.lastName) AS userName,
	CONCAT(cn.firstName, ' ', cn.lastName) AS clinicianName,
    CONCAT(ep1.firstName, ' ', ep1.lastName) AS firstPreceptorName,
    CONCAT(ep2.firstName, ' ', ep2.lastName) AS secondPreceptorName,
    CONCAT(ep3.firstName, ' ', ep3.lastName) AS thirdPreceptorName,
    CONCAT(ep4.firstName, ' ', ep4.lastName) AS fourthPreceptorName,
    CONCAT(ep5.firstName, ' ', ep5.lastName) AS fifthPreceptorName
    
	FROM 
		checkoff c
	-- INNER JOINs for related tables
	INNER JOIN 
		rotation r 
		ON c.rotationId = r.rotationId
	INNER JOIN 
		schools s 
		ON c.schoolId = s.schoolId
	INNER JOIN 
		student st 
		ON c.studentId = st.studentId
	LEFT JOIN 
		clinician cn 
		ON c.clinicianId = cn.clinicianId
	LEFT JOIN 
		schooltopicmaster stm 
		ON c.schoolTopicId = stm.schoolTopicId
		
	-- LEFT JOINs for preceptors
	LEFT JOIN 
		extenal_preceptors ep1 
		ON c.completion1stPreceptorId > 0 AND c.completion1stPreceptorId = ep1.id
	LEFT JOIN 
		extenal_preceptors ep2 
		ON c.completion2ndPreceptorId > 0 AND c.completion2ndPreceptorId = ep2.id
	LEFT JOIN 
		extenal_preceptors ep3 
		ON c.completion3rdPreceptorId > 0 AND c.completion3rdPreceptorId = ep3.id
	LEFT JOIN 
		extenal_preceptors ep4 
		ON c.completion4thPreceptorId > 0 AND c.completion4thPreceptorId = ep4.id
	LEFT JOIN 
		extenal_preceptors ep5 
		ON c.completion5thPreceptorId > 0 AND c.completion5thPreceptorId = ep5.id


		WHERE c.checkoffId = " . $checkoffId;

		if ($schoolId) {
			$sql .= " AND checkoff.schoolId=" . $schoolId;
		}
		// echo $sql;exit;
		$row = $objDB->GetDataRow($sql);
		unset($objDB);
		return $row;
	}

	function createCheckoffLog($checkoffId, $action, $userId, $userType)
	{
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$objcheckoff = new clscheckoff(); // Assuming `Attendance` class is used for `prepareAttendanceLogData`

		// Get user details based on user type
		$userDetails = getUserDetails($userId, $userType);

		// Retrieve checkoff details for the given checkoff ID and school ID
		$rowData = $objcheckoff->GetAllcheckoffDetailsForLogs($checkoffId);

		if ($rowData != '')
			$logData = $objLog->generateLogData($rowData, $userType);

		$additionalData = '';

		// Populate log data with user details
		$logData['userId'] = $userDetails['userId'];
		$logData['userName'] = $userDetails['userName'];
		$logMessage = '';
		if ($action == 'Add') {
			$logMessage = $logData['userName'] . ' added checkoff for ' . $logData['rotationName'] . ' rotation.';
		} else if ($action == 'Edit') {
			$logMessage = $logData['userName'] . ' updated checkoff from ' . $logData['rotationName'] . ' rotation.';
		} else if ($action == 'Delete') {
			$logMessage = $logData['userName'] . ' deleted ' . $rowData['userName'] . ' checkoff from ' . $logData['rotationName'] . ' rotation.';
		} else if ($action == 'Signoff' && $userType == 'Student') {
			$logMessage = $logData['userName'] . ' signoff checkoff from ' . $logData['rotationName'] . ' rotation.';
		} else if ($action == 'Signoff' && $userType == 'Preceptor') {
			$logMessage = $logData['userName'] . ' signoff checkoff from ' . $logData['rotationName'] . ' rotation.';
		}

		// Construct log message
		$logData['message'] = $logMessage;

		// Return the data
		return [$logData, $rowData, $additionalData];
	}

	function saveCheckoffAuditLog($checkoffId, $retCheckoffId, $clinicianId, $userType, $action, $isMobile = 0)
	{
		// Instantiate the Logger and Checkoff classes
		$objLog = new clsLogger();
		$objCheckoff = new clsCheckoff();

		// Prepare log data
		[$logData, $rowData, $additionalData] = $objCheckoff->createCheckoffLog($retCheckoffId, $action, $clinicianId, $userType);
		$logData['isMobile'] = $isMobile;

		if ($action == 'Delete') {

			// // Initialize database object
			$objDB = new clsDB();

			// Fetch data from `studentdailyevaldetails` table for the given master ID
			$evaluationDetailsResult = $objDB->GetAllSelectedDataFromTable('checkoffDetail', '', 'checkoffId', $retCheckoffId);
			unset($objDB);

			if ($evaluationDetailsResult) {
				// Convert the result set into an array
				$evaluationDetailsArray = fetchResultAsArray($evaluationDetailsResult);

				// Generate the JSON array (if needed for logs or other purposes)
				$additionalData = $evaluationDetailsArray;
			}
			$objCheckoff->DeleteCheckoff($retCheckoffId);
		}
		// Save the log details
		$objLog->saveLogs($logData, $action, $retCheckoffId, 'Checkoff', $rowData, $additionalData);

		// Clean up
		unset($objLog);
		unset($objCheckoff);

		return true; // Return success or handle further actions as needed
	}

	function cloneCheckoff($originalCheckoffId, $newStudentId)
	{
		$objDB = new clsDB();

		// Escape inputs to prevent SQL injection
		$originalCheckoffId = (int)$originalCheckoffId;
		$newStudentId = (int)$newStudentId;

		$sql = "INSERT INTO checkoff 
				(schoolId, studentId, checkoffDateTime, schoolTopicId, rotationId, clinicianId,
				evaluationDate, student_evaluationDate, school_evaluationDate,
				calculatedScore, calculatedUsafScore, completion1stPreceptorId, is1stCompletionStatus,
				createdBy, createdDate)
				SELECT schoolId, $newStudentId, checkoffDateTime, schoolTopicId, rotationId, clinicianId,
					evaluationDate, student_evaluationDate, school_evaluationDate,
					calculatedScore, calculatedUsafScore, completion1stPreceptorId, is1stCompletionStatus,
					createdBy, createdDate
				FROM checkoff
				WHERE checkoffId = $originalCheckoffId";


		// echo '</br>cloneCheckoff - '.$sql;
		// exit;
		$newCheckoffId = $objDB->ExecuteInsertQuery($sql);


		unset($objDB);
		return $newCheckoffId; // this is the new inserted checkoffId
	}

	function cloneCheckoffDetails($oldCheckoffId, $newCheckoffId)
	{
		$objDB = new clsDB();

		// Sanitize input
		$oldCheckoffId = (int)$oldCheckoffId;
		$newCheckoffId = (int)$newCheckoffId;

		$sql = "INSERT INTO checkoffdetail 
					(checkoffId, questionId, schoolQuestionDId, schoolOptionValue, 
					schoolOptionAnswerText, comments)
				SELECT 
					$newCheckoffId, questionId, schoolQuestionDId, schoolOptionValue, 
					schoolOptionAnswerText, comments
				FROM checkoffdetail
				WHERE checkoffId = $oldCheckoffId";
		// echo '</br>cloneCheckoffDetails - '.$sql;


		$objDB->ExecuteInsertQuery($sql);

		unset($objDB);
	}
}
