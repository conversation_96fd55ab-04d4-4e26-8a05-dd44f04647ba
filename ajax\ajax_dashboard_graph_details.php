<?php

include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsSchool.php');
include('../class/clsStudent.php');


if (isset($_GET['currentSchoolId'])) {

    $schoolId = isset($_GET['currentSchoolId']) ? $_GET['currentSchoolId'] : 0;
    $isAdmin = isset($_GET['isAdmin']) ? $_GET['isAdmin'] : 0;
    $startDate = isset($_GET['startDate']) ? $_GET['startDate'] : 0;
    $endDate = isset($_GET['endDate']) ? $_GET['endDate'] : 0;

    $objSchool = new clsSchool();
    $objStudent = new clsStudent();
    $data = [];
    if ($schoolId == 1) {
        $schooldetails = $objSchool->GetSchoolsForGraph($startDate,  $endDate);
        $totalSchools = ($schooldetails != '') ? mysqli_num_rows($schooldetails) : 0;
        if ($totalSchools > 0) {
            while ($row = mysqli_fetch_array($schooldetails)) {

                $data[] = [
                    'schoolId' => $row['schoolId'],
                    'schoolname' => $row['displayName'],
                    'totalstudents' => $row['totalstudents'],
                    'activestudents' => $row['activestudents'],
                    'rankcount' => $row['rankcount']
                ];
            }
        }
    } else {


        $studentDetails = $objStudent->getStudentCountForGraph($schoolId, $startDate, $endDate);
        if ($studentDetails && mysqli_num_rows($studentDetails) > 0) {
            while ($row = mysqli_fetch_assoc($studentDetails)) {
                $data[] = [
                    'dates' => $row['activity_date'],
                    'checkoffCount' => $row['checkoffCount'],
                    'journalCount' => $row['journalCount'],
                    'interactionCount' => $row['interactionCount'],
                    'studentCount' => $row['checkoffCount']+$row['journalCount']+$row['interactionCount']
                ];
            }
        }
    }

    echo json_encode($data);
    exit;
}
