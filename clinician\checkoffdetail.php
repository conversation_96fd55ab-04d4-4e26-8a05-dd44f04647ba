<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../setRequest.php');
include('../class/clsRotation.php');
include('../class/clscheckoff.php');

$objcheckoff = new clscheckoff();
$rotationId = 0;
$clinicianId = 0;
$topicId = 0;
$loggedClinicianType = '';
$schoolId = '';
$studentId = 0;
$loggedClinicianType = $_SESSION['loggedClinicianType'];
$isActiveCheckoff = $_SESSION["isActiveCheckoff"];
$clinicianId = $_SESSION["loggedClinicianId"];
$isMultipleCheckoff = 0;
if (isset($_GET['loggedClinicianType'])) //Edit Mode
{
	$loggedClinicianType = DecodeQueryData($_GET['loggedClinicianType']);
}
if (isset($_GET['clinicianId'])) //Edit Mode
{
	$clinicianId = DecodeQueryData($_GET['clinicianId']);
}
if (isset($_GET['studentId'])) //Edit Mode
{
	$studentId = DecodeQueryData($_GET['studentId']);
}

$objrotation = new clsRotation();
if ($loggedClinicianType == 'p' || $loggedClinicianType == 'P' || $loggedClinicianType == 'C' || $loggedClinicianType == 'c') {
	$rotation = $objrotation->GetAllActiverotationByClinician($currentSchoolId, $studentId, $clinicianId);
} else {
	$rotation = $objrotation->GetActiveRotationBySchool($currentSchoolId, $studentId);
}
unset($objrotation);

//get all checkoff
//For checkoff Type
$objDB = new clsDB();
$checkoffType = $objDB->GetSingleColumnValueFromTable('schools', 'checkoffType', 'schoolId', $currentSchoolId);
$isMultipleCheckoff = $objDB->GetSingleColumnValueFromTable('schoolsettings', 'status', 'schoolId', $currentSchoolId, 'type', 'multipleCheckoff');
unset($objDB);
$topic = $objcheckoff->GetAllcheckoff($currentSchoolId);
$AllTopics = $objcheckoff->GetAllCheckoffTopicsBySchool($currentSchoolId, $checkoffType);
?>

<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">

<style>
	.form-horizontal .form-group {
		margin-left: 0 !important;
		margin-right: 0 !important;
	}

	.button {
		border: 1px solid #5cb85c;
		background-color: #5cb85c;
		color: #fff;
		cursor: pointer;
		padding: 10px 30px;
		border-radius: 10px;
	}

	.flex-end {
		display: flex;
		justify-content: end;
	}

	.pt-0 {
		padding-top: 0;
	}

	.mfp-close {
		display: none !important;
	}

	.required-input {
		border-left: 3px solid red !important;
	}

	/* Ensure AlertifyJS appears above modal */
	.alertify-notifier {
		z-index: 9999 !important;
	}

	.alertify .ajs-dialog {
		z-index: 9999 !important;
	}

	/* Or to contain alerts within modal */
	#myModal .alertify-notifier {
		position: absolute !important;
		z-index: 1060 !important;
	}
</style>
<style>
	.some-class {
		float: left;
		clear: none;
	}

	.modalPosition {
		position: unset !important;
		top: 20px;
		margin-left: 10px;
		width: auto;
	}

	.input-group-addon {
		position: absolute;
		right: 7px;
		/* width: 100%; */
		z-index: 99;
		width: 35px;
		margin: auto;
		top: 5px;
		border-left: 1px solid #ccc;
		border-radius: 50% !important;
		padding: 10px -2px;
		height: 35px;
		/* background: #01A750; */
		/* color: #fff; */
		color: #555;
		background: #f6f9f9;
		border: none;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.panel-default>.panel-heading {
		background-color: transparent !important;
	}

	.btn-success,
	.btn-default {
		padding: 8px 25px;
		border-radius: 10px;
	}

	.panel {
		border-radius: 14px !important;
	}

	.select2-container--default .select2-selection--single .select2-selection__rendered {
		line-height: 45px !important;
	}

	.required-select2 {
		border-left: 3px solid red !important;
		border-radius: 12px !important;
	}

	.select2-container--default .select2-selection--single {
		background-color: #f6f9f9 !important;
		cursor: default !important;
		height: 45px !important;
		border-radius: 10px !important;
	}

	.select2-container--default .select2-selection--single {
		border: none !important;
	}

	.select2-container--default .select2-selection--multiple {
		min-height: 45px;
		background-color: #f6f9f9 !important;
		border-radius: 12px !important;
		box-shadow: none !important;
		border: none !important;
	}

	.select2-container--default .select2-selection--multiple .select2-selection__rendered li {
		text-wrap: wrap;
	}

	.select2-container--default .select2-selection--multiple .select2-selection__rendered {
		box-sizing: border-box;
		list-style: none;
		margin: 0;
		padding: 5px 10px;
		width: 100%;
	}


	.panel,
	.form-group {
		margin-bottom: 10px;
	}

	.bootstrap-datetimepicker-widget {
		border-radius: 12px !important;
	}

	.form-control {
		height: 45px;
	}


	.input-group {
		width: 100%;
	}

	.required-input {
		border-left: 3px solid red !important;
	}

	.input-group-addon {
		background: transparent;
	}

	/* Style for the collapsible content */
	.panel-collapse {
		display: none;
		/* Hidden by default */
		/* padding: 10px;
            border: 1px solid #ddd;
            background-color: #f9f9f9; */
	}

	/* Style for the collapsible button */
	.collapsible {
		/* background-color: #3498db;
            color: white;
            padding: 10px;
            border: none; */
		width: 100%;
		cursor: pointer;
		display: flex;
		justify-content: space-between;
		/* Align content horizontally */
	}

	.panel-heading {
		width: 100%;
	}

	/* Style for the arrow icons */
	.arrow-icon {
		transition: transform 0.3s;
	}

	.collapsible.expanded .arrow-icon i {
		transform: rotate(180deg);
	}

	.studdent-multiselect>.select2-container--default {
		border-left: 3px solid red !important;
		border-radius: 12px !important;
	}

	.select2-selection__rendered input:read-only {
		background-color: transparent !important;
	}

	@media screen and (max-width: 500px) {
		.panel-body ol {
			padding-left: 20px;
		}

		.panel-default>.panel-heading {
			padding-left: 5px;
		}
	}

	disabled {
		color: #999;
		background-color: #f5f5f5;
	}
</style>

<div id="myModal" class="" role="dialog">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
				<h4 class="modal-title">Check Off</h4>
			</div>
			<div class="modal-body">
				<div class="modal-body">

					<form id="frmCheckoff" autocomplete="off" data-parsley-validate class="form-horizontal" method="POST" <?php if ($isActiveCheckoff == 2) { ?> action="addusafcheckoff.html" <?php } else { ?> action="addcheckoff.html" <?php } ?>>
						<input type="hidden" name="isMultipleCheckoff" id="isMultipleCheckoff" value="<?php echo ($isMultipleCheckoff) ?>">

						<div class="form-group">
							<label class="control-label" for="evaluationDate">Checkoff Date </label>
							<div>
								<div class='input-group date w-full' id='startdatetime' style="position: relative;">

									<input type='text' name="startdatetime" id="startdatetime" class="form-control input-md required-input rotation_date dateInputFormat startdatetime" value="" required data-parsley-errors-container="#error-startdatetime" placeholder="MM/DD/YYYY" />
									<span class="input-group-addon calender-icon">
										<span class="glyphicon glyphicon-calendar"></span>
									</span>
								</div>
								<div id="error-startdatetime"></div>
							</div>
						</div>


						<div class="form-group">
							<label for="cborotation" class="control-label">Rotation:</label>
							<select id="cborotation" name="cborotation" class=" cborotation input-md required-input select2_single form-control" onChange="getstudent(this.value);gettopics(this.value);" required data-parsley-errors-container="#error-cborotation">
								<option value="" selected>Select</option>
								<?php
								if ($rotation != "") {
									while ($row = mysqli_fetch_assoc($rotation)) {
										$selrotationId  = $row['rotationId'];
										$name  = stripslashes($row['title']);

								?>
										<option rotationId="<?php echo ($selrotationId); ?>" value="<?php echo (EncodeQueryData($selrotationId)); ?>"><?php echo ($name); ?></option>
								<?php

									}
								}
								?>
							</select>
							<div id="error-cborotation"></div>

						</div>
						<div class="form-group">
							<label for="recipient-name" class="control-label">Course Topics:</label>

							<?php if ($isMultipleCheckoff) { ?>
								<div class=" studdent-multiselect">

									<select id="cbotopic" name="cbotopic[]" multiple="multiple" class="form-control input-md select2_single form-control  select2_tags required-input select2_multiple_topic" required data-parsley-errors-container="#error-cbotopic">
								</div>

							<?php } else { ?>
								<select id="cbotopic" name="cbotopic" class="input-md required-input select2_single form-control" required data-parsley-errors-container="#error-cbotopic">
									<option value="" selected>Select</option>
								<?php } ?>
								</select>
								<div id="error-cbotopic"></div>

						</div>
						<div class="form-group">
							<label for="recipient-name" class="control-label">All Topics:</label>

							<?php if ($isMultipleCheckoff) { ?>
								<div class=" studdent-multiselect">

									<select id="alltopic" name="alltopic[]" multiple="multiple" class="input-md required-input form-control select2_multiple_topic " required data-parsley-errors-container="#error-alltopic">
								</div>
							<?php } else { ?>
								<select id="alltopic" name="alltopic" class="input-md required-input select2_single form-control" required data-parsley-errors-container="#error-alltopic">
									<option value="" selected>Select</option>
								<?php } ?>

								<?php
								if ($AllTopics != "") {
									while ($row = mysqli_fetch_assoc($AllTopics)) {
										$selschoolTopicId  = $row['schoolTopicId'];
										$name  = stripslashes($row['schooltitle']);

								?>
										<option schoolTopicId="<?php echo ($selschoolTopicId); ?>" value="<?php echo ($selschoolTopicId); ?>"><?php echo ($name); ?></option>
								<?php
									}
								}
								?>
								</select>
								<div id="error-alltopic"></div>

						</div>

						<div class="form-group">
							<label for="recipient-name" class="control-label"> Student:</label>
							<div class=" studdent-multiselect">

								<select id="cboStudent" name="cboStudent[]" multiple="multiple" class="form-control input-md  select2_tags select2_multiple_student" required data-parsley-errors-container="#error-cboStudent">
									<option value=""> </option>
								</select>
							</div>
							<div id="error-cboStudent"></div>

						</div>
						<div class="flex-end">
							<button id="btnSend" name="btnSend" class="btn button mt-10">Send</button>
						</div>
					</form>

				</div>

				<!-- <div class="modal-footer">
      
    </div> -->
				<!-- /.modal-content -->
			</div><!-- /.modal-dialog -->
		</div><!-- /.modal -->
		<script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>
		<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>

		<script>
			$(document).ready(function() {
				$("#alltopic").select2({
					dropdownParent: $("#myModal")
				});
				$("#cborotation").select2({
					dropdownParent: $("#myModal")
				});
				$("#cbotopic").select2({
					dropdownParent: $("#myModal")
				});
				$("#cboStudent").select2({
					dropdownParent: $("#myModal")
				});
				reinitializeAllTopicsSelect2();

			});

			$('.close').click(function() {
				$.magnificPopup.close();
			});

			$(window).load(function() {
				$('#frmCheckoff').parsley().on('field:validated', function() {
						var ok = $('.parsley-error').length === 0;
					})
					.on('form:submit', function() {
						ShowProgressAnimation();
						return true; // Don't submit form for this demo
					});
			});

			function getstudent(val, selectedval) {
				selectedval = selectedval == undefined ? 0 : selectedval;
				$.ajax({
					type: "POST",
					url: "<?php echo ($dynamicOrgUrl); ?>/clinician/get_student.html",
					data: 'Rotation_Id=' + val + "&studentId=" + selectedval,
					success: function(data) {
						$("#cboStudent").html(data);

					}
				});
			}

			function reinitializeAllTopicsSelect2() {
				// Destroy existing Select2 instance
				$('#alltopic').select2('destroy');
				// $('#cboStudent').select2('destroy');
				$(".select2_single").select2({
					dropdownParent: $("#myModal")

				});

				// Reinitialize with the same configuration
				var isMultipleCheckoff = $('#isMultipleCheckoff').val();
				if (isMultipleCheckoff == 1) {
					$('#alltopic, #cbotopic').select2({
						placeholder: "Select",
						allowClear: true,
						// maximumSelectionLength: 3,
						dropdownParent: $("#myModal")
					});
				}
				$('#cboStudent').select2({
					placeholder: "Select",
					allowClear: true,
					// maximumSelectionLength: 3 // 👈 change to your desired limit
					dropdownParent: $("#myModal")

				});

				$('#select2-cbotopic-container').addClass('required-select2');
				$('#select2-alltopic-container').addClass('required-select2');
				$('#select2-cborotation-container').addClass('required-select2');
				$('#form-control step2 input-md select2_single').addClass('required-select2');

				$('#startdatetime').datetimepicker({
					format: 'MM/DD/YYYY',
					maxDate: moment(),
					// dropdownParent: $("#myModal")

				});
			}

			function gettopics(val, selectedval) {
				var studentId = '<?php echo $studentId; ?>';
				selectedval = selectedval == undefined ? 0 : selectedval;
				$.ajax({
					type: "POST",
					url: "<?php echo ($dynamicOrgUrl); ?>/clinician/get_topics.html",
					data: 'studentId=' + studentId + '&Rotation_Id=' + val + "&schoolTopicId=" + selectedval + "&isMultipleCheckoff=" + $('#isMultipleCheckoff').val(),
					success: function(data) {
						$("#cbotopic").html(data);

						// Reinitialize alltopic select2 after content changes
						reinitializeAllTopicsSelect2();
					}
				});
			}

			$('#cbotopic').change(function() {
				var x = $('#cbotopic option:selected').val();

				//$('#questionType').val(x);
				if (x > 0) {
					$('#alltopic').attr('disabled', true);
					$('#alltopic').removeAttr('required');
				} else {
					$('#alltopic').attr('disabled', false);
					$('#alltopic').attr('required', true);
				}

			});

			$('#alltopic').change(function() {
				var x = $('#alltopic option:selected').val();

				//$('#questionType').val(x);
				if (x > 0) {
					$('#cbotopic').attr('disabled', true);
					$('#cbotopic').removeAttr('required');
				} else {
					$('#cbotopic').attr('disabled', false);
					$('#cbotopic').attr('required', true);
				}

			});

			// Function to enforce maximum selection
			function validateMaxSelectionCount(selectorName, maxAllowed) {
				var selector = '.select2_multiple_' + selectorName;
				$(selector).on('change', function() {
					const selected = $(this).val() || [];

					if (selected.length > maxAllowed) {
						const trimmed = selected.slice(0, maxAllowed);
						$(this).val(trimmed).trigger('change.select2');

						// Configure alertify to show within modal
						alertify.set('notifier', 'position', 'top-center');
						alertify.error('You can only select up to ' + maxAllowed + ' ' + selectorName + 's');
					}
				});
			}

			// Apply to specific selectors with custom modal alert
			validateMaxSelectionCount('topic', 5);
			validateMaxSelectionCount('student', 10);
		</script>
		<script>
  const dateInput = document.getElementById("startdatetime");

  // Block letters and special characters (except / and -)
  dateInput.addEventListener("keypress", function (e) {
    const char = String.fromCharCode(e.which);
    if (!/[0-9\/\-]/.test(char)) {
      e.preventDefault();
    }
  });

  // Optional: Block pasting invalid characters
  dateInput.addEventListener("paste", function (e) {
    const paste = (e.clipboardData || window.clipboardData).getData("text");
    if (!/^[0-9\/\-]+$/.test(paste)) {
      e.preventDefault();
    }
  });
</script>